# Toco Design App IntelliJ IDEA Plugin

Enhance your Toco Design App experience with improved code design, debugging, and development tools.

## Compatibility

Compatible with IntelliJ IDEA versions 2020.3.5981.155 to 2024.2. For the best experience, use the latest version.

## Main Features

- **Two-way Positioning(Locator)**: Quickly navigate between IDE and App for seamless project and code access.
- **Internationalization Support**: Multi-language support for a global reach.
- **Login**: Authenticate with your Toco Design account.
- **Toco Design Project Verification**: Ensure your project is a valid Toco Design project with automatic and manual checks.
- **Plugin Settings**: Configure login, project updates, main domain, shortcuts, language, and privacy settings.
- **JDK Compatibility Check**: Enter a JDK major version to identify JARs compiled with higher versions.

## Supported IntelliJ IDEA Versions

- Minimum supported version: IntelliJ IDEA 2020.3.5981.155
- Maximum tested version: IntelliJ IDEA 2024.2

We recommend using the most recent version for optimal performance and full feature access.