import org.commonmark.parser.Parser
import org.commonmark.renderer.html.HtmlRenderer
import org.gradle.api.logging.Logger
import org.gradle.api.logging.Logging
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.util.*

/**
 * Utility object for handling markdown content in build scripts.
 * Provides functionality for loading localized markdown files and converting markdown to HTML.
 */
object BuildUtils {
    // Initialize the CommonMark parser for markdown processing
    private val parser = Parser.builder().build()

    // Initialize the HTML renderer for converting parsed markdown to HTML
    private val renderer = HtmlRenderer.builder().build()

    // Set up logging for this utility
    private val logger: Logger = Logging.getLogger(BuildUtils::class.java)

    /**
     * Retrieves localized markdown content from resources.
     * Attempts to find a markdown file matching the current locale, falling back to English if not found.
     *
     * @param baseFileName The base name of the markdown file without language code or extension.
     * @param resourcePath The path to the resource directory relative to the classpath root.
     * @return The content of the localized markdown file as a string, or an empty string if not found.
     */
    fun getLocalizedMarkdownContent(baseFileName: String, resourcePath: String): String {
        val classLoader = BuildUtils::class.java.classLoader
        val locale = Locale.getDefault()
        // Define a list of language codes to try, from most specific to least
        val languageCodes = listOf(
            "${locale.language}_${locale.country}", // e.g., en_US
            locale.language,                        // e.g., en
            "en"                                    // Default fallback
        )

        // Attempt to find and load a localized version of the file
        for (code in languageCodes) {
            val resourceName = "$resourcePath/${baseFileName}_$code.md"
            classLoader.getResourceAsStream(resourceName)?.use { inputStream ->
                logger.info("Found localized resource: $resourceName")
                return InputStreamReader(inputStream, StandardCharsets.UTF_8).readText()
            }
        }

        // If no localized file is found, try the base filename without a language code
        val defaultResourceName = "$resourcePath/$baseFileName.md"
        classLoader.getResourceAsStream(defaultResourceName)?.use { inputStream ->
            logger.info("Using default resource: $defaultResourceName")
            return InputStreamReader(inputStream, StandardCharsets.UTF_8).readText()
        }

        // Log a warning if no suitable file is found
        logger.warn("No suitable $baseFileName resource found in $resourcePath for locale: $locale")
        return ""
    }

    /**
     * Converts markdown content to HTML using the CommonMark parser and renderer.
     *
     * @param markdown The markdown content to convert as a string.
     * @return The HTML representation of the markdown content, or an empty string if conversion fails.
     */
    fun convertMarkdownToHtml(markdown: String): String {
        return try {
            val document = parser.parse(markdown)
            renderer.render(document)
        } catch (e: Exception) {
            logger.error("Error converting Markdown to HTML", e)
            ""
        }
    }
}
