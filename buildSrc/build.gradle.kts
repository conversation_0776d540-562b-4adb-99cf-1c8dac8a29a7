// Build script for the buildSrc project

// Apply the Kotlin DSL plugin to enable Kotlin support for build scripts
plugins {
    `kotlin-dsl`
}

// Configure repositories for dependency resolution
repositories {
    // Use Maven Central repository for downloading dependencies
    mavenCentral()
}

// Declare dependencies for the buildSrc project
dependencies {
    // Include the CommonMark library for Markdown processing
    // Version 0.20.0 is used, but can be updated as needed
    implementation("org.commonmark:commonmark:0.20.0")
}
