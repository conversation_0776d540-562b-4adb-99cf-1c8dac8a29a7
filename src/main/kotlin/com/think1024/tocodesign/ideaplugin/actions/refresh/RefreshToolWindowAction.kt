package com.think1024.tocodesign.ideaplugin.actions.refresh

import com.intellij.openapi.actionSystem.AnActionEvent
import com.think1024.tocodesign.ideaplugin.actions.ToolWindowAction
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserManager

/**
 * 刷新 Tool Window 内容的基础 Action
 * 
 * 类似于 DevTools Action，这个 Action 会刷新指定 Tool Window 的页面内容
 */
abstract class RefreshToolWindowAction(private val targetWindowId: String) : ToolWindowAction(targetWindowId) {

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        
        // 获取对应的 Browser 并刷新页面
        val tocoBrowser = TocoBrowserManager.getInstance(project).getBrowser(targetWindowId)
        tocoBrowser.refresh()
    }
}