package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseSearchManager
import com.intellij.openapi.components.service
import com.intellij.openapi.ui.Messages
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import javax.swing.JOptionPane

class SearchAction : AnAction() {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val searchManager = project.service<CodeBaseSearchManager>()
        
        // Show input dialog to get search query
        val query = JOptionPane.showInputDialog(
            null,
            "Enter search query:",
            "Search",
            JOptionPane.QUESTION_MESSAGE
        ) ?: return

        if (query.isNotBlank()) {
            val results = searchManager.search(query)
            // TODO: Show results in a custom tool window or dialog
            Messages.showInfoMessage(
                project,
                "Found ${results.size} results for query: $query",
                "Search Results"
            )
        }
    }

    override fun update(e: AnActionEvent) {
        if (e.project == null) {
            e.presentation.isEnabledAndVisible = false
        } else {
            e.presentation.isEnabledAndVisible = ProjectPluginSettings.getInstance(e.project!!).projectId != null
        }
    }
} 