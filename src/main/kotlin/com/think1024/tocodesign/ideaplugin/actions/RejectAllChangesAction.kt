package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.icons.AllIcons
import com.intellij.ui.JBColor
import com.intellij.util.IconUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString

class RejectAllChangesAction(initialText: String, targetFile: VirtualFile): ApplyCodeAction(
    getI18nString("code.apply.reject.all"),
    getI18nString("code.apply.reject.all.description"),
    IconUtil.scale(IconUtil.colorize(AllIcons.Actions.Cancel, JBColor.RED), null,  0.85f),
    targetFile,
    initialText
) {
}