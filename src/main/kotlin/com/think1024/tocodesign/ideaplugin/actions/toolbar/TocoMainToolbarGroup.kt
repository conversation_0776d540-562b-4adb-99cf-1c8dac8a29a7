package com.think1024.tocodesign.ideaplugin.actions.toolbar

import com.intellij.openapi.actionSystem.*
import com.intellij.ide.ui.UISettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings

class TocoMainToolbarGroup : DefaultActionGroup() {
    init {
        isPopup = false
    }

    override fun update(e: AnActionEvent) {
        if (e.project == null || ProjectPluginSettings.getInstance(e.project!!).projectId == null) {
            e.presentation.isVisible = false
            return
        }

        val presentation = e.presentation
        val showMainToolbar = UISettings.getInstance().showMainToolbar
        
        when (e.place) {
            ActionPlaces.MAIN_TOOLBAR -> {
                presentation.isVisible = showMainToolbar
            }
            ActionPlaces.NAVIGATION_BAR_TOOLBAR -> {
                presentation.isVisible = !showMainToolbar
            }
            else -> {
                presentation.isVisible = true
            }
        }
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.EDT
    }
}