package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.ide.IdeBundle
import com.intellij.ide.actions.CloseEditorsActionBase
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.actionSystem.ex.ActionUtil.copyFrom
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx
import com.intellij.openapi.fileEditor.impl.EditorComposite
import com.intellij.openapi.fileEditor.impl.EditorWindow
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.util.Comparing
import com.intellij.openapi.util.NlsContexts
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.containers.ContainerUtil
import com.think1024.tocodesign.ideaplugin.toco.TocoVirtualFile
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewEditor
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.*
import kotlin.reflect.KFunction
import kotlin.reflect.full.declaredFunctions
import kotlin.reflect.jvm.isAccessible


fun callProtectedMethodKt(obj: Any, methodName: String, vararg args: Any?): Any? {
    // 获取对象的类及其所有父类
    var currentClass: Class<*>? = obj::class.java
    var func: KFunction<*>? = null
    
    // 遍历类层次结构查找方法
    while (currentClass != null && func == null) {
        // 使用 kotlin 反射查找当前类中的方法
        func = currentClass.kotlin.declaredFunctions
            .firstOrNull { it.name == methodName }
        
        // 如果当前类中没找到，继续查找父类
        if (func == null) {
            currentClass = currentClass.superclass
        }
    }
    
    // 如果没有找到方法，抛出异常
    if (func == null) {
        throw NoSuchMethodException("Method $methodName not found in class hierarchy")
    }
    
    // 解除访问限制
    func.isAccessible = true
    
    // 调用方法并返回结果
    return func.call(obj, *args)
}

class HookCloseEditorAction(origAction: AnAction): HookAction(origAction) {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val file = e.getData(CommonDataKeys.VIRTUAL_FILE) ?: return
        if (isTocoFileModified(file, project)) {
            ApplicationManager.getApplication().invokeLater {
                val result = Messages.showYesNoDialog(project,
                    getI18nString("message.editor.modified.confirm.message"),
                    getI18nString("message.editor.modified.confirm.title"),
                    Messages.getWarningIcon())
                if (result == Messages.YES) {
                    origAction.actionPerformed(e)
                }
            }
            return
        }
        origAction.actionPerformed(e)
    }
}

class HookCloseEditorsAction(private val origAction: CloseEditorsActionBase): CloseEditorsActionBase() {
    override fun isFileToClose(editor: EditorComposite, window: EditorWindow, fileEditorManager: FileEditorManagerEx): Boolean {
        if (editor.file is TocoVirtualFile && editor.isModified) {
            return false
        }
       try {
           val result = callProtectedMethodKt(origAction, "isFileToClose", editor, window, fileEditorManager)
           return result as Boolean
       } catch (_: Exception) {
           return false
       }
    }

    override fun isFileToCloseInContext(dataContext: DataContext?, editor: EditorComposite?, window: EditorWindow?): Boolean {
        if (editor?.file is TocoVirtualFile && editor.isModified) {
            return false
        }
        try {
            val result = callProtectedMethodKt(origAction, "isFileToCloseInContext", dataContext, editor, window)
            return result as Boolean
        } catch (_: Exception) {
            return false
        }
    }

    override fun getPresentationText(p0: Boolean): @NlsContexts.Command String? {
        try {
            val result = callProtectedMethodKt(origAction, "getPresentationText", p0)
            return result as String?
        } catch (_: Exception) {
            return null
        }
    }

}

class HookCloseAllAction(origAction: AnAction): HookAction(origAction) {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.getData<Project?>(CommonDataKeys.PROJECT)
        val commandProcessor: CommandProcessor = CommandProcessor.getInstance()
        commandProcessor.executeCommand(
            project, {
                val window = e.getData<EditorWindow?>(EditorWindow.DATA_KEY)
                if (window != null) {
                    for (file in window.fileList) {
                        if (isTocoFileModified(file, project!!)) {
                            continue
                        }
                        window.closeFile(file)
                    }
                    return@executeCommand
                }
                val fileEditorManager = FileEditorManagerEx.getInstanceEx(project!!)
                val selectedFile = fileEditorManager.selectedFiles[0]
                for (openFile in fileEditorManager.getSiblings(selectedFile)) {
                    if (isTocoFileModified(openFile, project)) {
                        continue
                    }
                    fileEditorManager.closeFile(openFile)
                }
            }, IdeBundle.message("command.close.all.editors"), null
        )
    }
}

class HookCloseAllExceptAction(origAction: AnAction): HookAction(origAction) {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.getData<Project?>(CommonDataKeys.PROJECT)
        if (project == null) return
        val fileEditorManager = FileEditorManagerEx.getInstanceEx(project)
        val window = e.getData<EditorWindow?>(EditorWindow.DATA_KEY)
        val current = e.getData(CommonDataKeys.VIRTUAL_FILE)
        if (window != null) {
            for (file in window.fileList) {
                if (file == current || isTocoFileModified(file, project)) {
                    continue
                }
                window.closeFile(file)
            }
            return
        }
        val selectedFile = fileEditorManager.selectedFiles[0]
        for (sibling in fileEditorManager.getSiblings(selectedFile)) {
            if (isTocoFileModified(sibling, project)) {
                continue
            }
            if (!Comparing.equal<VirtualFile?>(selectedFile, sibling)) {
                fileEditorManager.closeFile(sibling)
            }
        }
    }
}

fun isTocoFileModified(file: VirtualFile, project: Project): Boolean {
    val editors: Array<FileEditor?> = FileEditorManager.getInstance(project).getEditors(file)
    for (editor in editors) {
        if (editor is TocoWebViewEditor) {
            return editor.isModified
        }
    }
    return false
}

fun hookCloseActions() {
    val actionManager = ActionManager.getInstance()
    actionManager.replaceAction(
        IdeActions.ACTION_CLOSE_ALL_EDITORS,
        HookCloseAllAction(actionManager.getAction(IdeActions.ACTION_CLOSE_ALL_EDITORS))
    )
    actionManager.replaceAction(
        IdeActions.ACTION_CLOSE_ALL_EDITORS_BUT_THIS,
        HookCloseAllExceptAction(actionManager.getAction(IdeActions.ACTION_CLOSE_ALL_EDITORS_BUT_THIS))
    )

    listOf(
        IdeActions.ACTION_CLOSE_ACTIVE_TAB,
        IdeActions.ACTION_CLOSE,
        IdeActions.ACTION_CLOSE_EDITOR
    ).forEach {
        val origAction = actionManager.getAction(it)
        actionManager.replaceAction(it, HookCloseEditorAction(origAction))
    }

    listOf(
        IdeActions.ACTION_CLOSE_ALL_UNMODIFIED_EDITORS,
        "CloseAllReadonly",
        "CloseAllToTheRight",
        "CloseAllToTheLeft",
        "CloseAllUnpinnedEditors"
    ).forEach {
        val origAction = actionManager.getAction(it)
        if (origAction is CloseEditorsActionBase) {
            actionManager.replaceAction(it, HookCloseEditorsAction(origAction))
        }
    }
}

fun hookCloseTab(project: Project, file: VirtualFile) {
    val fileEditorManager = FileEditorManagerEx.getInstanceEx(project)
    val editorWindow = fileEditorManager.currentWindow
    if (editorWindow == null) {
        return
    }
    val tabbedContainer = editorWindow.tabbedPane
    val tab = tabbedContainer.tabs.findInfo(file)
    if (tab?.tabLabelActions !is DefaultActionGroup) {
        return
    }
    val oldGroup = tab.tabLabelActions as DefaultActionGroup
    val actionGroup = DefaultActionGroup(oldGroup.getChildren(ActionManager.getInstance()).toList())
    val actions = actionGroup.getChildren(ActionManager.getInstance())
    val closeAction = actions.find { it.javaClass.simpleName == "CloseTab" }
    if (closeAction == null) {
        return
    }
    val newAction = HookCloseEditorAction(closeAction)
    actionGroup.replaceAction(closeAction, newAction)
    tab.setTabLabelActions(actionGroup, ActionPlaces.EDITOR_TAB)
}


class TocoActionPromoter : ActionPromoter {
    override fun promote(actions: MutableList<out AnAction?>, context: DataContext): MutableList<AnAction?>? {
        val action: AnAction? = ContainerUtil.findInstance<Any?, HookShortCutCloseAction?>(actions, HookShortCutCloseAction::class.java)
        return if (action != null) Collections.singletonList(action) else Collections.emptyList()
    }
}

class HookShortCutCloseAction : AnAction() {
    init {
        copyFrom(this, IdeActions.ACTION_CLOSE)
    }
    override fun actionPerformed(e: AnActionEvent) {
        val action = ActionManager.getInstance().getAction(IdeActions.ACTION_CLOSE)
        action.actionPerformed(e)
    }
}