package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.AnActionEvent
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge

class ApplyAllChangesAction(replacementText: String, targetFile: VirtualFile): ApplyCodeAction(
    getI18nString("code.apply.accept.all"),
    getI18nString("code.apply.accept.all.description"),
    AllIcons.General.InspectionsOK,
    targetFile,
    replacementText
) {

    override fun actionPerformed(e: AnActionEvent) {
        super.actionPerformed(e)

        // 通过bridge回调给webview，通知对应路径的文件的已经完成apply
        val project = e.project ?: return

        // 获取targetFile的相对路径
        val projectBasePath = project.basePath
        if (projectBasePath != null) {
            val filePath = targetFile.path
            val relativePath = if (filePath.startsWith(projectBasePath)) {
                // 确保处理路径分隔符，保证前导分隔符被去除
                val pathWithoutBase = filePath.substring(projectBasePath.length)
                if (pathWithoutBase.startsWith("/") || pathWithoutBase.startsWith("\\")) {
                    pathWithoutBase.substring(1)
                } else {
                    pathWithoutBase
                }
            } else {
                // 如果不是以项目路径开头，则返回完整路径
                filePath
            }

            WebViewBridge.sendIfReady(project, "TocoDesign", "code-applied", mapOf("path" to relativePath))
        }
    }
}