package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import java.awt.KeyboardFocusManager
import javax.swing.SwingUtilities


abstract class ToolWindowAction(private val targetWindowId: String) : AnAction() {
    override fun update(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            e.presentation.isEnabled = false
            return
        }

        // 只有当对应的 Tool Window 可见时才启用此 Action
        val toolWindow = ToolWindowManager.getInstance(project).getToolWindow(targetWindowId)
        e.presentation.isEnabled = toolWindow?.isVisible == true && isFocused(project)
    }

    private fun isFocused(project: Project): Boolean {
        val toolWindow = ToolWindowManager.getInstance(project).getToolWindow(targetWindowId)
        if (toolWindow?.component == null) {
            return false
        }
        val focusOwner = KeyboardFocusManager.getCurrentKeyboardFocusManager().focusOwner
        return focusOwner != null && SwingUtilities.isDescendingFrom(focusOwner, toolWindow.component)
    }
}