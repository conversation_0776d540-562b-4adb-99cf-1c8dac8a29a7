package com.think1024.tocodesign.ideaplugin.actions.refresh

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.fileEditor.FileEditorManager
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewEditor

/**
 * 刷新 TocoWebView 编辑器内容的 Action
 */
class TocoWebViewRefreshAction : AnAction() {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val editor = FileEditorManager.getInstance(project).selectedEditor
        
        if (editor is TocoWebViewEditor) {
            editor.refresh()
        }
    }

    override fun update(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            e.presentation.isEnabledAndVisible = false
            return
        }

        val editor = FileEditorManager.getInstance(project).selectedEditor
        e.presentation.isEnabledAndVisible = editor is TocoWebViewEditor
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.EDT
    }
}