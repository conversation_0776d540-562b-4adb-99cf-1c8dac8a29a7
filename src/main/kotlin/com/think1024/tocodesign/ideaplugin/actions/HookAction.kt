package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.ShortcutSet
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.isAccessible

open class HookAction(val origAction: AnAction) : AnAction() {
    init {
        copyFrom(origAction)
    }

    override fun actionPerformed(e: AnActionEvent) {
        return origAction.actionPerformed(e)
    }

    override fun update(e: AnActionEvent) {
        return origAction.update(e)
    }

    override fun setShortcutSet(shortcutSet: ShortcutSet) {
        try {
            val property = AnAction::class.java.getDeclaredField("myShortcutSet")
            property.isAccessible = true
            property.set(this, shortcutSet)
        } catch (_: Exception) {
            super.shortcutSet = shortcutSet
        }
    }
}