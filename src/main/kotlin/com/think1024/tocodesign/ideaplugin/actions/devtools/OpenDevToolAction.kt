package com.think1024.tocodesign.ideaplugin.actions.devtools

import com.intellij.openapi.actionSystem.AnActionEvent
import com.think1024.tocodesign.ideaplugin.actions.ToolWindowAction
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserManager

abstract class OpenDevToolAction(private val targetWindowId: String) : ToolWindowAction(targetWindowId) {
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
//        val tocoPluginBrowser = getTocoPluginBrowser(project, targetWindowId) ?: return
//        tocoPluginBrowser.openDevtools()
        // 用tocoBrowser打开dev tool，内部有关闭dev tool的销毁逻辑 （其中tocoBrowser.browser就是通过getTocoPluginBrowser拿到的browser）
        val tocoBrowser = TocoBrowserManager.getInstance(project).getBrowser(targetWindowId)
        tocoBrowser.openDevTools()
    }
}