package com.think1024.tocodesign.ideaplugin.constants

/**
 * Tool window IDs used throughout the plugin.
 * These IDs must match the ones defined in plugin.xml under <toolWindow id="...">
 */
object WindowIds {
    /**
     * @see plugin.xml: <toolWindow id="TocoMenu" ...>
     */
    const val TOCO_MENU = "TocoMenu"

    /**
     * @see plugin.xml: <toolWindow id="TocoDesign" ...>
     */
    const val TOCO_DESIGN = "TocoDesign"

    /**
     * @see plugin.xml: <toolWindow id="Toco Build" ...>
     */
    const val TOCO_BUILD = "Toco Build"

    val ALL = setOf(TOCO_MENU, TOCO_DESIGN, TOCO_BUILD)
}