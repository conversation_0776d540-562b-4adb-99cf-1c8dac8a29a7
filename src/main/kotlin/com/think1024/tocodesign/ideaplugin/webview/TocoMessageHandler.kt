package com.think1024.tocodesign.ideaplugin.webview

import com.intellij.ui.jcef.JBCefJSQuery
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.callback.CefQueryCallback
import org.cef.handler.CefMessageRouterHandlerAdapter

class TocoMessageHandler(val handler: (msg: String?) -> JBCefJSQuery.Response?): CefMessageRouterHandlerAdapter() {
    override fun onQuery(
        browser: CefBrowser?,
        frame: CefFrame?,
        queryId: Long,
        request: String?,
        persistent: <PERSON><PERSON><PERSON>,
        callback: CefQueryCallback?
    ): <PERSON><PERSON><PERSON> {
        val response = handler(request)
        if (callback != null && response != null) {
            if (response.isSuccess && response.hasResponse()) {
                callback.success(response.response())
            } else {
                callback.failure(response.errCode(), response.errMsg())
            }
        } else callback?.success("")

        return true
    }

    override fun onQueryCanceled(browser: CefBrowser?, frame: CefFrame?, queryId: Long) {

    }
}