/*
 * Copyright 2010 <PERSON><PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@file:Suppress("SENSELESS_COMPARISON")
package com.think1024.tocodesign.ideaplugin.webview

import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.headers.HeaderReader
import net.lingala.zip4j.headers.HeaderUtil
import net.lingala.zip4j.headers.HeaderWriter
import net.lingala.zip4j.io.inputstream.NumberedSplitRandomAccessFile
import net.lingala.zip4j.io.inputstream.ZipInputStream
import net.lingala.zip4j.model.*
import net.lingala.zip4j.model.enums.RandomAccessFileMode
import net.lingala.zip4j.progress.ProgressMonitor
import net.lingala.zip4j.tasks.*
import net.lingala.zip4j.tasks.AddFilesToZipTask.AddFilesToZipTaskParameters
import net.lingala.zip4j.tasks.AddFolderToZipTask.AddFolderToZipTaskParameters
import net.lingala.zip4j.tasks.AddStreamToZipTask.AddStreamToZipTaskParameters
import net.lingala.zip4j.tasks.AsyncZipTask.AsyncTaskParameters
import net.lingala.zip4j.tasks.ExtractAllFilesTask.ExtractAllFilesTaskParameters
import net.lingala.zip4j.tasks.ExtractFileTask.ExtractFileTaskParameters
import net.lingala.zip4j.tasks.MergeSplitZipFileTask.MergeSplitZipFileTaskParameters
import net.lingala.zip4j.tasks.RemoveFilesFromZipTask.RemoveFilesFromZipTaskParameters
import net.lingala.zip4j.tasks.RenameFilesTask.RenameFilesTaskParameters
import net.lingala.zip4j.tasks.SetCommentTask.SetCommentTaskTaskParameters
import net.lingala.zip4j.util.*
import java.io.*
import java.nio.charset.Charset
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory

/**
 * Base class to handle zip files. Some of the operations supported
 * in this class are:<br></br>
 *
 *  * Create Zip File
 *  * Add files to zip file
 *  * Add folder to zip file
 *  * Extract files from zip files
 *  * Remove files from zip file
 *
 */
class ZipFile @JvmOverloads constructor(zipFile: File, password: CharArray? = null) : Closeable {
    val file: File
    private var zipModel: ZipModel? = null

    @get:Throws(ZipException::class)
    var isEncrypted: Boolean = false
        /**
         * Checks to see if the zip file is encrypted
         *
         * @return true if encrypted, false if not
         * @throws ZipException
         */
        get() {
            if (zipModel == null) {
                readZipInfo()
                if (zipModel == null) {
                    throw ZipException("Zip Model is null")
                }
            }

            if (zipModel!!.getCentralDirectory() == null || zipModel!!.getCentralDirectory().getFileHeaders() == null) {
                throw ZipException("invalid zip file")
            }

            for (fileHeader in zipModel!!.getCentralDirectory().getFileHeaders()) {
                if (fileHeader != null) {
                    if (fileHeader.isEncrypted()) {
                        field = true
                        break
                    }
                }
            }

            return field
        }
        private set
    val progressMonitor: ProgressMonitor?
    var isRunInThread: Boolean
    private var password: CharArray?
    private val headerWriter = HeaderWriter()
    private var charset: Charset? = null
    private var threadFactory: ThreadFactory? = null
    var executorService: ExecutorService? = null
        private set
    private var bufferSize = InternalZipConstants.BUFF_SIZE
    private val openInputStreams: MutableList<InputStream> = ArrayList<InputStream>()
    var isUseUtf8CharsetForPasswords: Boolean = InternalZipConstants.USE_UTF8_FOR_PASSWORD_ENCODING_DECODING

    /**
     * Creates a new ZipFile instance with the zip file at the location specified in zipFile.
     * This constructor does not yet create a zip file if it does not exist. Creation happens when adding files
     * to this ZipFile instance
     * @param zipFile
     */
    constructor(zipFile: String) : this(File(zipFile), null)

    /**
     * Creates a new ZipFile instance with the zip file at the location specified in zipFile.
     * Input password will be used for any zip operations like adding files or extracting files.
     * This constructor does not yet create a zip file if it does not exist. Creation happens when adding files
     * to this ZipFile instance
     * @param zipFile
     */
    constructor(zipFile: String, password: CharArray?) : this(File(zipFile), password)

    /**
     * Creates a new Zip File Object with the input file.
     * If the zip file does not exist, it is not created at this point.
     *
     * @param zipFile file reference to the zip file
     * @param password password to use for the zip file
     * @throws IllegalArgumentException when zip file parameter is null
     */
    /**
     * Creates a new Zip File Object with the input file.
     * If the zip file does not exist, it is not created at this point.
     *
     * @param zipFile file reference to the zip file
     * @throws IllegalArgumentException when zip file parameter is null
     */
    init {
        requireNotNull(zipFile) { "input zip file parameter is null" }

        this.file = zipFile
        this.password = password
        this.isRunInThread = false
        this.progressMonitor = ProgressMonitor()
    }

    /**
     * Creates a zip file and adds the list of source file(s) to the zip file. If the zip file
     * exists then this method throws an exception. Parameters such as compression type, etc
     * can be set in the input parameters. While the method addFile/addFiles also creates the
     * zip file if it does not exist, the main functionality of this method is to create a split
     * zip file. To create a split zip file, set the splitArchive parameter to true with a valid
     * splitLength. Split Length has to be more than 65536 bytes
     *
     * @param filesToAdd - File to be added to the zip file
     * @param parameters     - zip parameters for this file list
     * @param splitArchive   - if archive has to be split or not
     * @param splitLength    - if archive has to be split, then length in bytes at which it has to be split
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun createSplitZipFile(
        filesToAdd: MutableList<File?>, parameters: ZipParameters?, splitArchive: Boolean,
        splitLength: Long
    ) {
        if (file.exists()) {
            throw ZipException(
                ("zip file: " + this.file
                        + " already exists. To add files to existing zip file use addFile method")
            )
        }

        if (filesToAdd == null || filesToAdd.size == 0) {
            throw ZipException("input file List is null, cannot create zip file")
        }

        createNewZipModel()
        zipModel!!.setSplitArchive(splitArchive)
        zipModel!!.setSplitLength(splitLength)

        AddFilesToZipTask(zipModel, password, headerWriter, buildAsyncParameters()).execute(
            AddFilesToZipTaskParameters(filesToAdd, parameters, buildConfig())
        )
    }

    /**
     * Creates a zip file and adds the files/folders from the specified folder to the zip file.
     * This method does the same functionality as in addFolder method except that this method
     * can also create split zip files when adding a folder. To create a split zip file, set the
     * splitArchive parameter to true and specify the splitLength. Split length has to be more than
     * or equal to 65536 bytes. Note that this method throws an exception if the zip file already
     * exists.
     *
     * @param folderToAdd
     * @param parameters
     * @param splitArchive
     * @param splitLength
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun createSplitZipFileFromFolder(
        folderToAdd: File, parameters: ZipParameters, splitArchive: Boolean,
        splitLength: Long
    ) {
        if (folderToAdd == null) {
            throw ZipException("folderToAdd is null, cannot create zip file from folder")
        }

        if (parameters == null) {
            throw ZipException("input parameters are null, cannot create zip file from folder")
        }

        if (file.exists()) {
            throw ZipException(
                ("zip file: " + this.file
                        + " already exists. To add files to existing zip file use addFolder method")
            )
        }

        createNewZipModel()
        zipModel!!.setSplitArchive(splitArchive)

        if (splitArchive) {
            zipModel!!.setSplitLength(splitLength)
        }

        addFolder(folderToAdd, parameters, false)
    }

    /**
     * Adds input source file to the zip file with provided zip parameters. If zip file does not exist,
     * this method creates a new zip file.
     *
     * @param fileToAdd - File with path to be added to the zip file
     * @param zipParameters - parameters for the entry to be added to zip
     * @throws ZipException
     */
    /**
     * Adds input source file to the zip file with default zip parameters. If zip file does not exist,
     * this method creates a new zip file.
     *
     * @param fileToAdd - File with path to be added to the zip file
     * @throws ZipException
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun addFile(fileToAdd: String?, zipParameters: ZipParameters = ZipParameters()) {
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(fileToAdd)) {
            throw ZipException("file to add is null or empty")
        }

        addFiles(mutableListOf<File?>(File(fileToAdd)), zipParameters)
    }

    /**
     * Adds input source file to the zip file with default zip parameters. If zip file does not exist,
     * this method creates a new zip file.
     *
     * @param fileToAdd - File to be added to the zip file
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun addFile(fileToAdd: File?) {
        addFiles(mutableListOf<File?>(fileToAdd), ZipParameters())
    }

    /**
     * Adds input source file to the zip file. If zip file does not exist,
     * this method creates a new zip file. Parameters such as compression type, etc
     * can be set in the input parameters.
     *
     * @param fileToAdd - File to be added to the zip file
     * @param parameters - zip parameters for this file
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun addFile(fileToAdd: File?, parameters: ZipParameters) {
        addFiles(mutableListOf<File?>(fileToAdd), parameters)
    }

    /**
     * Adds the list of input files to the zip file. If zip file does not exist, then
     * this method creates a new zip file. Parameters such as compression type, etc
     * can be set in the input parameters.
     *
     * @param filesToAdd
     * @param parameters
     * @throws ZipException
     */
    /**
     * Adds the list of input files to the zip file with default zip parameters. If zip file does not exist,
     * this method creates a new zip file.
     *
     * @param filesToAdd
     * @throws ZipException
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun addFiles(filesToAdd: MutableList<File?>, parameters: ZipParameters = ZipParameters()) {
        if (filesToAdd == null || filesToAdd.size == 0) {
            throw ZipException("input file List is null or empty")
        }

        if (parameters == null) {
            throw ZipException("input parameters are null")
        }

        readZipInfo()

        if (zipModel == null) {
            throw ZipException("internal error: zip model is null")
        }

        if (file.exists() && zipModel!!.isSplitArchive()) {
            throw ZipException("Zip file already exists. Zip file format does not allow updating split/spanned files")
        }

        AddFilesToZipTask(zipModel, password, headerWriter, buildAsyncParameters()).execute(
            AddFilesToZipTaskParameters(filesToAdd, parameters, buildConfig())
        )
    }

    /**
     * Adds the folder in the given file object to the zip file. If zip file does not exist,
     * then a new zip file is created. If input folder is invalid then an exception
     * is thrown. Zip parameters for the files in the folder to be added can be set in
     * the input parameters
     *
     * @param folderToAdd
     * @param zipParameters
     * @throws ZipException
     */
    /**
     * Adds the folder in the given file object to the zip file with default zip parameters. If zip file does not exist,
     * then a new zip file is created. If input folder is invalid then an exception
     * is thrown.
     *
     * @param folderToAdd
     * @throws ZipException
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun addFolder(folderToAdd: File, zipParameters: ZipParameters = ZipParameters()) {
        if (folderToAdd == null) {
            throw ZipException("input path is null, cannot add folder to zip file")
        }

        if (!folderToAdd.exists()) {
            throw ZipException("folder does not exist")
        }

        if (!folderToAdd.isDirectory()) {
            throw ZipException("input folder is not a directory")
        }

        if (!folderToAdd.canRead()) {
            throw ZipException("cannot read input folder")
        }

        if (zipParameters == null) {
            throw ZipException("input parameters are null, cannot add folder to zip file")
        }

        addFolder(folderToAdd, zipParameters, true)
    }

    /**
     * Internal method to add a folder to the zip file.
     *
     * @param folderToAdd
     * @param zipParameters
     * @param checkSplitArchive
     * @throws ZipException
     */
    @Throws(ZipException::class)
    private fun addFolder(folderToAdd: File?, zipParameters: ZipParameters?, checkSplitArchive: Boolean) {
        readZipInfo()

        if (zipModel == null) {
            throw ZipException("internal error: zip model is null")
        }

        if (checkSplitArchive) {
            if (zipModel!!.isSplitArchive()) {
                throw ZipException("This is a split archive. Zip file format does not allow updating split/spanned files")
            }
        }

        AddFolderToZipTask(zipModel, password, headerWriter, buildAsyncParameters()).execute(
            AddFolderToZipTaskParameters(folderToAdd, zipParameters, buildConfig())
        )
    }

    /**
     * Creates a new entry in the zip file and adds the content of the input stream to the
     * zip file. ZipParameters.isSourceExternalStream and ZipParameters.fileNameInZip have to be
     * set before in the input parameters. If the file name ends with / or \, this method treats the
     * content as a directory. Setting the flag ProgressMonitor.setRunInThread to true will have
     * no effect for this method and hence this method cannot be used to add content to zip in
     * thread mode
     *
     * @param inputStream
     * @param parameters
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun addStream(inputStream: InputStream, parameters: ZipParameters) {
        if (inputStream == null) {
            throw ZipException("inputstream is null, cannot add file to zip")
        }

        if (parameters == null) {
            throw ZipException("zip parameters are null")
        }

        this.isRunInThread = false

        readZipInfo()

        if (zipModel == null) {
            throw ZipException("internal error: zip model is null")
        }

        if (file.exists() && zipModel!!.isSplitArchive()) {
            throw ZipException("Zip file already exists. Zip file format does not allow updating split/spanned files")
        }

        AddStreamToZipTask(zipModel, password, headerWriter, buildAsyncParameters()).execute(
            AddStreamToZipTaskParameters(inputStream, parameters, buildConfig())
        )
    }

    /**
     * Extracts all entries in the zip file to the destination path considering the options defined in
     * UnzipParameters
     *
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param unzipParameters parameters to be considered during extraction
     * @throws ZipException when an issue occurs during extraction
     */
    /**
     * Extracts all the files in the given zip file to the input destination path.
     * If zip file does not exist or destination path is invalid then an
     * exception is thrown.
     *
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @throws ZipException when an issue occurs during extraction
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun extractAll(destinationPath: String?, unzipParameters: UnzipParameters? = UnzipParameters()) {
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(destinationPath)) {
            throw ZipException("output path is null or invalid")
        }

        if (!Zip4jUtil.createDirectoryIfNotExists(File(destinationPath))) {
            throw ZipException("invalid output path")
        }

        if (zipModel == null) {
            readZipInfo()
        }

        // Throw an exception if zipModel is still null
        if (zipModel == null) {
            throw ZipException("Internal error occurred when extracting zip file")
        }

        ExtractAllFilesTask(zipModel, password, unzipParameters, buildAsyncParameters()).execute(
            ExtractAllFilesTaskParameters(destinationPath, buildConfig())
        )
    }

    /**
     * Extracts a specific file from the zip file to the destination path.
     * If destination path is invalid, then this method throws an exception.
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory
     *
     * @param fileHeader file header corresponding to the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param unzipParameters any parameters that have to be considered during extraction
     * @throws ZipException when an issue occurs during extraction
     */
    @Throws(ZipException::class)
    fun extractFile(fileHeader: FileHeader, destinationPath: String?, unzipParameters: UnzipParameters?) {
        extractFile(fileHeader, destinationPath, null, unzipParameters)
    }

    /**
     * Extracts a specific file from the zip file to the destination path.
     * If destination path is invalid, then this method throws an exception.
     * <br></br><br></br>
     * If newFileName is not null or empty, newly created file name will be replaced by
     * the value in newFileName. If this value is null, then the file name will be the
     * value in FileHeader.getFileName. If file being extract is a directory, the directory name
     * will be replaced with the newFileName
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     * <br></br><br></br>
     * Any parameters that have to be considered during extraction can be passed in through unzipParameters
     *
     * @param fileHeader file header corresponding to the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param newFileName if not null, this will be the name given to the file upon extraction
     * @param unzipParameters any parameters that have to be considered during extraction
     * @throws ZipException when an issue occurs during extraction
     */
    /**
     * Extracts a specific file from the zip file to the destination path.
     * If destination path is invalid, then this method throws an exception.
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory
     *
     * @param fileHeader file header corresponding to the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @throws ZipException when an issue occurs during extraction
     */
    /**
     * Extracts a specific file from the zip file to the destination path.
     * If destination path is invalid, then this method throws an exception.
     * <br></br><br></br>
     * If newFileName is not null or empty, newly created file name will be replaced by
     * the value in newFileName. If this value is null, then the file name will be the
     * value in FileHeader.getFileName. If file being extract is a directory, the directory name
     * will be replaced with the newFileName
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     *
     * @param fileHeader file header corresponding to the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param newFileName if not null, this will be the name given to the file upon extraction
     * @throws ZipException when an issue occurs during extraction
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun extractFile(
        fileHeader: FileHeader, destinationPath: String?, newFileName: String? = null,
        unzipParameters: UnzipParameters? = UnzipParameters()
    ) {
        if (fileHeader == null) {
            throw ZipException("input file header is null, cannot extract file")
        }

        extractFile(fileHeader.getFileName(), destinationPath, newFileName, unzipParameters)
    }

    /**
     * Extracts a specific file from the zip file to the destination path.
     * This method first finds the necessary file header from the input file name.
     * <br></br><br></br>
     * File name is relative file name in the zip file. For example if a zip file contains
     * a file "a.txt", then to extract this file, input file name has to be "a.txt". Another
     * example is if there is a file "b.txt" in a folder "abc" in the zip file, then the
     * input file name has to be abc/b.txt
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     * <br></br><br></br>
     * Any parameters that have to be considered during extraction can be passed in through unzipParameters
     * <br></br><br></br>
     * Throws an exception of type [ZipException.Type.FILE_NOT_FOUND] if file header could not be found for the given file name.
     * Throws an exception if the destination path is invalid.
     *
     * @param fileName name of the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param unzipParameters any parameters that have to be considered during extraction
     * @throws ZipException when an issue occurs during extraction
     */
    @Throws(ZipException::class)
    fun extractFile(fileName: String?, destinationPath: String?, unzipParameters: UnzipParameters?) {
        extractFile(fileName, destinationPath, null, unzipParameters)
    }

    /**
     * Extracts a specific file from the zip file to the destination path.
     * This method first finds the necessary file header from the input file name.
     * <br></br><br></br>
     * File name is relative file name in the zip file. For example if a zip file contains
     * a file "a.txt", then to extract this file, input file name has to be "a.txt". Another
     * example is if there is a file "b.txt" in a folder "abc" in the zip file, then the
     * input file name has to be abc/b.txt
     * <br></br><br></br>
     * If newFileName is not null or empty, newly created file name will be replaced by
     * the value in newFileName. If this value is null, then the file name will be the
     * value in FileHeader.getFileName. If file being extract is a directory, the directory name
     * will be replaced with the newFileName
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     * <br></br><br></br>
     * Any parameters that have to be considered during extraction can be passed in through unzipParameters
     * <br></br><br></br>
     * Throws an exception of type [ZipException.Type.FILE_NOT_FOUND] if file header could not be found for the
     * given file name.
     * Throws an exception if the destination path is invalid.
     *
     * @param fileName name of the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param newFileName if not null, this will be the name given to the file upon extraction
     * @param unzipParameters any parameters that have to be considered during extraction
     * @throws ZipException when an issue occurs during extraction
     */
    /**
     * Extracts a specific file from the zip file to the destination path.
     * This method first finds the necessary file header from the input file name.
     * <br></br><br></br>
     * File name is relative file name in the zip file. For example if a zip file contains
     * a file "a.txt", then to extract this file, input file name has to be "a.txt". Another
     * example is if there is a file "b.txt" in a folder "abc" in the zip file, then the
     * input file name has to be abc/b.txt
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     * <br></br><br></br>
     * Throws an exception of type [ZipException.Type.FILE_NOT_FOUND] if file header could not be found for the given file name.
     * Throws an exception if the destination path is invalid.
     *
     * @param fileName name of the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @throws ZipException when an issue occurs during extraction
     */
    /**
     * Extracts a specific file from the zip file to the destination path.
     * This method first finds the necessary file header from the input file name.
     * <br></br><br></br>
     * File name is relative file name in the zip file. For example if a zip file contains
     * a file "a.txt", then to extract this file, input file name has to be "a.txt". Another
     * example is if there is a file "b.txt" in a folder "abc" in the zip file, then the
     * input file name has to be abc/b.txt
     * <br></br><br></br>
     * If newFileName is not null or empty, newly created file name will be replaced by
     * the value in newFileName. If this value is null, then the file name will be the
     * value in FileHeader.getFileName. If file being extract is a directory, the directory name
     * will be replaced with the newFileName
     * <br></br><br></br>
     * If fileHeader is a directory, this method extracts all files under this directory.
     * <br></br><br></br>
     * Throws an exception of type [ZipException.Type.FILE_NOT_FOUND] if file header could not be found for the given file name.
     * Throws an exception if the destination path is invalid.
     *
     * @param fileName name of the entry which has to be extracted
     * @param destinationPath path to which the entries of the zip are to be extracted
     * @param newFileName if not null, this will be the name given to the file upon extraction
     * @throws ZipException when an issue occurs during extraction
     */
    @JvmOverloads
    @Throws(ZipException::class)
    fun extractFile(
        fileName: String?,
        destinationPath: String?,
        newFileName: String? = null,
        unzipParameters: UnzipParameters? = UnzipParameters()
    ) {
        var unzipParameters = unzipParameters
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(fileName)) {
            throw ZipException("file to extract is null or empty, cannot extract file")
        }

        if (!Zip4jUtil.isStringNotNullAndNotEmpty(destinationPath)) {
            throw ZipException("destination path is empty or null, cannot extract file")
        }

        if (unzipParameters == null) {
            unzipParameters = UnzipParameters()
        }

        readZipInfo()

        ExtractFileTask(zipModel, password, unzipParameters, buildAsyncParameters()).execute(
            ExtractFileTaskParameters(destinationPath, fileName, newFileName, buildConfig())
        )
    }

    @get:Throws(ZipException::class)
    val fileHeaders: MutableList<FileHeader?>?
        /**
         * Returns the list of file headers in the zip file. Returns an empty list if the zip file does not exist.
         *
         * @return list of file headers
         * @throws ZipException
         */
        get() {
            readZipInfo()
            if (zipModel == null || zipModel!!.getCentralDirectory() == null) {
                return mutableListOf<FileHeader?>()
            }
            return zipModel!!.getCentralDirectory().getFileHeaders()
        }

    /**
     * Returns FileHeader if a file header with the given fileHeader
     * string exists in the zip model: If not returns null
     *
     * @param fileName
     * @return FileHeader
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun getFileHeader(fileName: String?): FileHeader? {
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(fileName)) {
            throw ZipException("input file name is emtpy or null, cannot get FileHeader")
        }

        readZipInfo()
        if (zipModel == null || zipModel!!.getCentralDirectory() == null) {
            return null
        }

        return HeaderUtil.getFileHeader(zipModel, fileName)
    }

    @get:Throws(ZipException::class)
    val isSplitArchive: Boolean
        /**
         * Checks if the zip file is a split archive
         *
         * @return true if split archive, false if not
         * @throws ZipException
         */
        get() {
            if (zipModel == null) {
                readZipInfo()
                if (zipModel == null) {
                    throw ZipException("Zip Model is null")
                }
            }

            return zipModel!!.isSplitArchive()
        }

    /**
     * Removes the file provided in the input file header from the zip file.
     *
     * If zip file is a split zip file, then this method throws an exception as
     * zip specification does not allow for updating split zip archives.
     *
     * If this file header is a directory, all files and directories
     * under this directory will be removed as well.
     *
     * @param fileHeader
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun removeFile(fileHeader: FileHeader) {
        if (fileHeader == null) {
            throw ZipException("input file header is null, cannot remove file")
        }

        removeFile(fileHeader.getFileName())
    }

    /**
     * Removes the file provided in the input parameters from the zip file.
     * This method first finds the file header and then removes the file.
     *
     * If file does not exist, then this method throws an exception.
     *
     * If zip file is a split zip file, then this method throws an exception as
     * zip specification does not allow for updating split zip archives.
     *
     * If the entry representing this file name is a directory, all files and directories
     * under this directory will be removed as well.
     *
     * @param fileName
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun removeFile(fileName: String?) {
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(fileName)) {
            throw ZipException("file name is empty or null, cannot remove file")
        }

        removeFiles(mutableListOf<String?>(fileName))
    }

    /**
     * Removes all files from the zip file that match the names in the input list.
     *
     * If any of the file is a directory, all the files and directories under this directory
     * will be removed as well
     *
     * If zip file is a split zip file, then this method throws an exception as
     * zip specification does not allow for updating split zip archives.
     *
     * @param fileNames
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun removeFiles(fileNames: MutableList<String?>) {
        if (fileNames == null) {
            throw ZipException("fileNames list is null")
        }

        if (fileNames.isEmpty()) {
            return
        }

        if (zipModel == null) {
            readZipInfo()
        }

        if (zipModel!!.isSplitArchive()) {
            throw ZipException("Zip file format does not allow updating split/spanned files")
        }

        RemoveFilesFromZipTask(zipModel, headerWriter, buildAsyncParameters()).execute(
            RemoveFilesFromZipTaskParameters(fileNames, buildConfig())
        )
    }

    /**
     * Renames file name of the entry represented by file header. If the file name in the input file header does not
     * match any entry in the zip file, the zip file will not be modified.
     *
     * If the file header is a folder in the zip file, all sub-files and sub-folders in the zip file will also be renamed.
     *
     * Zip file format does not allow modifying a split zip file. Therefore if the zip file being dealt with is a split
     * zip file, this method throws an exception
     *
     * @param fileHeader file header to be changed
     * @param newFileName the file name that has to be changed to
     * @throws ZipException if fileHeader is null or newFileName is null or empty or if the zip file is a split file
     */
    @Throws(ZipException::class)
    fun renameFile(fileHeader: FileHeader, newFileName: String?) {
        if (fileHeader == null) {
            throw ZipException("File header is null")
        }

        renameFile(fileHeader.getFileName(), newFileName)
    }

    /**
     * Renames file name of the entry represented by input fileNameToRename. If there is no entry in the zip file matching
     * the file name as in fileNameToRename, the zip file will not be modified.
     *
     * If the entry with fileNameToRename is a folder in the zip file, all sub-files and sub-folders in the zip file will
     * also be renamed. For a folder, the fileNameToRename has to end with zip file separator "/". For example, if a
     * folder name "some-folder-name" has to be modified to "new-folder-name", then value of fileNameToRename should be
     * "some-folder-name/". If newFileName does not end with a separator, zip4j will add a separator.
     *
     * Zip file format does not allow modifying a split zip file. Therefore if the zip file being dealt with is a split
     * zip file, this method throws an exception
     *
     * @param fileNameToRename file name in the zip that has to be renamed
     * @param newFileName the file name that has to be changed to
     * @throws ZipException if fileNameToRename is empty or newFileName is empty or if the zip file is a split file
     */
    @Throws(ZipException::class)
    fun renameFile(fileNameToRename: String?, newFileName: String?) {
        if (!Zip4jUtil.isStringNotNullAndNotEmpty(fileNameToRename)) {
            throw ZipException("file name to be changed is null or empty")
        }

        if (!Zip4jUtil.isStringNotNullAndNotEmpty(newFileName)) {
            throw ZipException("newFileName is null or empty")
        }

        renameFiles(Collections.singletonMap<String?, String?>(fileNameToRename, newFileName))
    }

    /**
     * Renames all the entries in the zip file that match the keys in the map to their corresponding values in the map. If
     * there are no entries matching any of the keys from the map, the zip file is not modified.
     *
     * If any of the entry in the map represents a folder, all files and folders will be renamed so that their parent
     * represents the renamed folder.
     *
     * Zip file format does not allow modifying a split zip file. Therefore if the zip file being dealt with is a split
     * zip file, this method throws an exception
     *
     * @param fileNamesMap map of file names that have to be changed with values in the map being the name to be changed to
     * @throws ZipException if map is null or if the zip file is a split file
     */
    @Throws(ZipException::class)
    fun renameFiles(fileNamesMap: MutableMap<String?, String?>) {
        if (fileNamesMap == null) {
            throw ZipException("fileNamesMap is null")
        }

        if (fileNamesMap.size == 0) {
            return
        }

        readZipInfo()

        if (zipModel!!.isSplitArchive()) {
            throw ZipException("Zip file format does not allow updating split/spanned files")
        }

        val asyncTaskParameters = buildAsyncParameters()
        RenameFilesTask(zipModel, headerWriter, RawIO(), asyncTaskParameters).execute(
            RenameFilesTaskParameters(fileNamesMap, buildConfig())
        )
    }

    /**
     * Merges split zip files into a single zip file without the need to extract the
     * files in the archive
     *
     * @param outputZipFile
     * @throws ZipException
     */
    @Throws(ZipException::class)
    fun mergeSplitFiles(outputZipFile: File) {
        if (outputZipFile == null) {
            throw ZipException("outputZipFile is null, cannot merge split files")
        }

        if (outputZipFile.exists()) {
            throw ZipException("output Zip File already exists")
        }

        readZipInfo()

        if (this.zipModel == null) {
            throw ZipException("zip model is null, corrupt zip file?")
        }

        MergeSplitZipFileTask(zipModel, buildAsyncParameters()).execute(
            MergeSplitZipFileTaskParameters(outputZipFile, buildConfig())
        )
    }

    @get:Throws(ZipException::class)
    @set:Throws(ZipException::class)
    var comment: String?
        /**
         * Returns the comment set for the Zip file
         *
         * @return String
         * @throws ZipException
         */
        get() {
            if (!file.exists()) {
                throw ZipException("zip file does not exist, cannot read comment")
            }

            readZipInfo()

            if (zipModel == null) {
                throw ZipException("zip model is null, cannot read comment")
            }

            if (zipModel!!.getEndOfCentralDirectoryRecord() == null) {
                throw ZipException("end of central directory record is null, cannot read comment")
            }

            return zipModel!!.getEndOfCentralDirectoryRecord().getComment()
        }
        /**
         * Sets comment for the Zip file
         *
         * @param comment
         * @throws ZipException
         */
        set(comment) {
            if (comment == null) {
                throw ZipException("input comment is null, cannot update zip file")
            }

            if (!file.exists()) {
                throw ZipException("zip file does not exist, cannot set comment for zip file")
            }

            readZipInfo()

            if (zipModel == null) {
                throw ZipException("zipModel is null, cannot update zip file")
            }

            if (zipModel!!.getEndOfCentralDirectoryRecord() == null) {
                throw ZipException("end of central directory is null, cannot set comment")
            }

            SetCommentTask(zipModel, buildAsyncParameters()).execute(
                SetCommentTaskTaskParameters(comment, buildConfig())
            )
        }

    /**
     * Returns an input stream for reading the contents of the Zip file corresponding
     * to the input FileHeader. Throws an exception if the FileHeader does not exist
     * in the ZipFile
     *
     * @param fileHeader
     * @return ZipInputStream
     * @throws ZipException
     */
    @Throws(IOException::class)
    fun getInputStream(fileHeader: FileHeader, manageStream: Boolean): ZipInputStream {
        if (fileHeader == null) {
            throw ZipException("FileHeader is null, cannot get InputStream")
        }

        readZipInfo()

        if (zipModel == null) {
            throw ZipException("zip model is null, cannot get inputstream")
        }

        val zipInputStream = UnzipUtil.createZipInputStream(zipModel, fileHeader, password)
        if (manageStream) {
            openInputStreams.add(zipInputStream)
        }
        return zipInputStream
    }

    val isValidZipFile: Boolean
        /**
         * Checks to see if the input zip file is a valid zip file. This method
         * will try to read zip headers. If headers are read successfully, this
         * method returns true else false.
         *
         * Since v2.7.0: if the zip file is a split zip file, this method also checks to see if
         * all the split files of the zip exists.
         *
         * @return boolean - true if a valid zip file, i.e, zip4j is able to read the
         * zip headers, and in case of a split zip file, all split files of the zip exists; false otherwise
         *
         * @since 1.2.3
         */
        get() {
            if (!file.exists()) {
                return false
            }

            try {
                readZipInfo()

                if (zipModel!!.isSplitArchive() && !verifyAllSplitFilesOfZipExists(this.splitZipFiles)) {
                    return false
                }

                return true
            } catch (e: Exception) {
                return false
            }
        }

    @get:Throws(ZipException::class)
    val splitZipFiles: MutableList<File>
        /**
         * Returns the full file path+names of all split zip files
         * in an ArrayList. For example: If a split zip file(abc.zip) has a 10 split parts
         * this method returns an array list with path + "abc.z01", path + "abc.z02", etc.
         * Returns null if the zip file does not exist
         *
         * @return List of Split zip Files
         * @throws ZipException
         */
        get() {
            readZipInfo()
            return FileUtils.getSplitZipFiles(zipModel)
        }

    /**
     * Closes any open streams that were open by an instance of this class.
     *
     * @throws IOException when the underlying input stream throws an exception when trying to close it
     */
    @Throws(IOException::class)
    override fun close() {
        for (inputStream in openInputStreams) {
            inputStream.close()
        }
        openInputStreams.clear()
    }

    /**
     * Sets a password to be used for the zip file. Will override if a password supplied via ZipFile constructor
     * @param password - char array of the password to be used
     */
    fun setPassword(password: CharArray?) {
        this.password = password
    }

    /**
     * Returns the size of the buffer used to read streams
     *
     * @return size of the buffer used to read streams
     */
    fun getBufferSize(): Int {
        return bufferSize
    }

    /**
     * Sets the size of buffer that should be used when reading streams. This size cannot be less than the value defined
     * in InternalZipConstants.MIN_BUFF_SIZE
     *
     * @param bufferSize size of the buffer that should be used when reading streams
     * @throws IllegalArgumentException if bufferSize is less than value configured in InternalZipConstants.MIN_BUFF_SIZE
     */
    fun setBufferSize(bufferSize: Int) {
        require(bufferSize >= InternalZipConstants.MIN_BUFF_SIZE) { "Buffer size cannot be less than " + InternalZipConstants.MIN_BUFF_SIZE + " bytes" }

        this.bufferSize = bufferSize
    }

    /**
     * Reads the zip header information for this zip file. If the zip file
     * does not exist, it creates an empty zip model.<br></br><br></br>
     * **Note:** This method does not read local file header information
     *
     * @throws ZipException
     */
    @Throws(ZipException::class)
    private fun readZipInfo() {
        if (zipModel != null) {
            return
        }

        if (!file.exists()) {
            createNewZipModel()
            return
        }

        if (!file.canRead()) {
            throw ZipException("no read access for the input zip file")
        }

        try {
            initializeRandomAccessFileForHeaderReading().use { randomAccessFile ->
                val headerReader = HeaderReader()
                zipModel = headerReader.readAllHeaders(randomAccessFile, buildConfig())
                zipModel!!.setZipFile(this.file)
            }
        } catch (e: ZipException) {
            throw e
        } catch (e: IOException) {
            throw ZipException(e)
        }
    }

    private fun createNewZipModel() {
        zipModel = ZipModel()
        zipModel!!.setZipFile(this.file)
    }

    @Throws(IOException::class)
    private fun initializeRandomAccessFileForHeaderReading(): RandomAccessFile {
        if (FileUtils.isNumberedSplitFile(this.file)) {
            val allSplitFiles = FileUtils.getAllSortedNumberedSplitFiles(
                this.file
            )
            val numberedSplitRandomAccessFile = NumberedSplitRandomAccessFile(
                this.file,
                RandomAccessFileMode.READ.getValue(), allSplitFiles
            )
            numberedSplitRandomAccessFile.openLastSplitFileForReading()
            return numberedSplitRandomAccessFile
        }

        return RandomAccessFile(this.file, RandomAccessFileMode.READ.getValue())
    }

    private fun buildAsyncParameters(): AsyncTaskParameters {
        if (this.isRunInThread) {
            if (threadFactory == null) {
                threadFactory = Executors.defaultThreadFactory()
            }
            executorService = Executors.newSingleThreadExecutor(threadFactory)
        }

        return AsyncTaskParameters(executorService, this.isRunInThread, progressMonitor)
    }

    private fun verifyAllSplitFilesOfZipExists(allSplitFiles: MutableList<File>): Boolean {
        for (splitFile in allSplitFiles) {
            if (!splitFile.exists()) {
                return false
            }
        }
        return true
    }

    /**
     * Returns user defined charset that was set by setCharset() method. If no charset was explicitly defined
     * (by calling setCharset()), this method returns the default charset which zip4j uses, which is utf-8.
     *
     * @return user-defined charset or utf-8 if no charset explicitly set
     */
    fun getCharset(): Charset? {
        if (charset == null) {
            return InternalZipConstants.CHARSET_UTF_8
        }
        return charset
    }

    /**
     * Sets the charset to be used for encoding file names and comments
     *
     * @param charset charset to use to encode file names and comments
     * @throws IllegalArgumentException if charset is null
     */
    @Throws(IllegalArgumentException::class)
    fun setCharset(charset: Charset) {
        requireNotNull(charset) { "charset cannot be null" }
        this.charset = charset
    }

    fun setThreadFactory(threadFactory: ThreadFactory?) {
        this.threadFactory = threadFactory
    }

    override fun toString(): String {
        return file.toString()
    }

    private fun buildConfig(): Zip4jConfig {
        return Zip4jConfig(charset, bufferSize, this.isUseUtf8CharsetForPasswords)
    }
}