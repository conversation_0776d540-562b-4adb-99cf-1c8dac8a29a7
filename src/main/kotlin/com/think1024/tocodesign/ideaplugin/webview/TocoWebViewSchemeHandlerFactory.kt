package com.think1024.tocodesign.ideaplugin.webview

import com.intellij.openapi.project.DumbAware
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import net.lingala.zip4j.io.inputstream.ZipInputStream
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.callback.CefCallback
import org.cef.callback.CefSchemeHandlerFactory
import org.cef.handler.CefLoadHandler
import org.cef.handler.CefResourceHandler
import org.cef.misc.IntRef
import org.cef.misc.StringRef
import org.cef.network.CefRequest
import org.cef.network.CefResponse
import java.net.URL
import java.net.URLConnection.guessContentTypeFromName

class TocoWebViewSchemeHandlerFactory : CefSchemeHandlerFactory {
    private val apiPath = listOf<String>("workspace", "api", "rpc", "oauth2", "vsf", "mis", "repository", "lm")
    private val loginPath = "manage"
    override fun create(browser: CefBrowser?, frame: CefFrame?, schemeName: String, request: CefRequest): CefResourceHandler? {
        if (ApplicationPluginSettings.getInstance().onlineWeb) {
            return null
        }
        val url = URL(request.url)
        val parts = url.path.split("/").filter { it.isNotEmpty() }
        if (!apiPath.contains(parts[0]) && loginPath != parts[0]) {
            return CustomResourceHandler()
        }
        return null
    }
}

class CustomResourceHandler : CefResourceHandler, DumbAware {
    private var requestURL: String? = null
    private var relativePath: String? = null
    private var mimeType: String? = null
    private val extensionPattern = Regex("\\.[^.\\/]+$")
    private var inputStream: ZipInputStream? = null

    override fun processRequest(cefRequest: CefRequest, cefCallback: CefCallback): Boolean {
        val urlString = cefRequest.url
        val url = URL(urlString)
        requestURL = urlString
        relativePath = if (extensionPattern.containsMatchIn(urlString)) {
            // 文件
            (url.path + (url.query?.let { "?$it" } ?: "")).trim('/')
        } else {
            val parts = url.path.split("/").filter { it.isNotEmpty() }
            if (parts[0] == "f") {
                // frontend页面
                "f/index.html"
            } else {
                // studio页面
                "index.html"
            }
        }
        mimeType =  guessContentTypeFromName(relativePath)
        cefCallback.Continue()
        return true
    }

    override fun getResponseHeaders(
        cefResponse: CefResponse,
        responseLength: IntRef,
        redirectUrl: StringRef
    ) {
        val header = WebviewPackage.getFileHeader(relativePath)
        if (header != null) {
            if (mimeType !== null) {
                cefResponse.mimeType = mimeType
            }
            responseLength.set(header.uncompressedSize.toInt())
            cefResponse.status = 200
            // 允许cef缓存一周，以加快加载速度
            cefResponse.setHeaderByName("Cache-Control", "max-age=604800", true)
        } else {
            cefResponse.error = CefLoadHandler.ErrorCode.ERR_FILE_NOT_FOUND
            cefResponse.statusText = "file not exist in webview package"
            cefResponse.status = 404
        }
    }

    override fun readResponse(
        dataOut: ByteArray,
        bytesToRead: Int,
        bytesRead: IntRef,
        callback: CefCallback
    ): Boolean {
        if (inputStream == null) {
            inputStream = WebviewPackage.getInputStream(relativePath)
        }
        return inputStream?.let { inputStream ->
            val availableSize = inputStream.available()
            return if (availableSize > 0) {
                val size = inputStream.read(dataOut, 0, bytesToRead)
                val read = if (size > 0) size else 0
                bytesRead.set(read)
                if (inputStream.available() == 0) {
                    inputStream.close()
                }
                read > 0
            } else {
                inputStream.close()
                false
            }
        } ?: false
    }

    override fun cancel() {
        inputStream?.close()
    }
}