package com.think1024.tocodesign.ideaplugin.webview

import com.think1024.tocodesign.ideaplugin.utils.PathUtil
import com.think1024.tocodesign.ideaplugin.utils.ResourceLoader
import net.lingala.zip4j.io.inputstream.ZipInputStream
import net.lingala.zip4j.model.FileHeader
import java.io.File
import java.nio.file.Files
import java.nio.file.Path


object WebviewPackage {
    private val zipFile: ZipFile
    init {
        val zipFilePath = "/package.zip"
        val webviewDir = Path.of(PathUtil.getPluginDataDir().toString(), "webview")
        Files.createDirectories(webviewDir)

        val targetPath = webviewDir.resolve("package.zip").toString()
        ResourceLoader.extractResource(zipFilePath, targetPath)
        val file = File(targetPath)
        zipFile = ZipFile(file, "oVxiHR38zahYZpc".toCharArray())
    }

    fun getFileHeader(relativePath: String?): FileHeader? {
        if (relativePath.isNullOrEmpty()) {
            return null
        }
        val fileHeader = zipFile.getFileHeader(relativePath)
        return fileHeader
    }

    fun getInputStream(relativePath: String?): ZipInputStream? {
        val fileHeader = getFileHeader(relativePath)
        if (fileHeader == null) {
            return null
        }
        return zipFile.getInputStream(fileHeader, false)
    }

    fun getFileData(relativePath: String?): ByteArray? {
        val inputStream = getInputStream(relativePath)
        if (inputStream == null) {
            return null
        }
        return try {
            inputStream.use { input ->
                val output = mutableListOf<Byte>()
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    output.addAll(buffer.sliceArray(0 until bytesRead).asList())
                }
                output.toByteArray()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}
