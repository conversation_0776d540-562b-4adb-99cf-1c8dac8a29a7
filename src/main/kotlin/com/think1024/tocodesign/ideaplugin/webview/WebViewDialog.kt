package com.think1024.tocodesign.ideaplugin.webview

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.KeyStroke
import java.awt.event.KeyEvent

class WebViewDialog(
    project: Project? = null,
    url: String,
    dialogTitle: String = "WebView",
    loadingMode: LoadingMode = LoadingMode.INIT_PAGE_CALLBACK, // 默认使用 INIT_PAGE_CALLBACK 模式
    isOSR: Boolean = true
) : DialogWrapper(project) {
    private val myProject = project
    private val logger = Logger.getInstance(WebViewDialog::class.java)
    private val webViewLoader: WebViewLoader = WebViewLoader(
        project,
        url,
        TocoBrowserConfig(isFrame = false, isOSR),
        null, // onInitPage 回调为 null
        loadingMode // 传入加载模式
    )

    init {
        title = dialogTitle
        isResizable = true
        logger.info("WebViewDialog 初始化: url=$url, title=$dialogTitle, loadingMode=$loadingMode")
        init() // 初始化 DialogWrapper
        WebViewManager.register(project, webViewLoader.getBrowser()) // 注册到 WebViewManager
        
        // 禁用回车键触发 OK 按钮
        disableEnterKeyAsOkAction()
    }
    
    /**
     * 禁用回车键触发 OK 按钮的功能
     */
    private fun disableEnterKeyAsOkAction() {
        val rootPane = rootPane
        if (rootPane != null) {
            // 移除默认的 ENTER 键绑定
            val inputMap = rootPane.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW)
            inputMap.put(KeyStroke.getKeyStroke(KeyEvent.VK_ENTER, 0), "none")
        }
    }

    override fun createCenterPanel(): JComponent {
        logger.info("WebViewDialog createCenterPanel")
        return JPanel(BorderLayout()).apply {
            preferredSize = Dimension(1000, 700)
            add(webViewLoader.getComponent(), BorderLayout.CENTER)
        }
    }

    override fun getPreferredFocusedComponent(): JComponent? {
        return webViewLoader.getPreferredFocusedComponent()
    }

    /**
     * 获取对话框中的 TocoBrowser 对象
     * 
     * @return TocoBrowser 对象，用于访问浏览器功能
     */
    fun getBrowser(): TocoBrowser {
        return webViewLoader.getBrowser()
    }

    override fun dispose() {
        logger.info("WebViewDialog dispose")
        WebViewManager.unregister(myProject, webViewLoader.getBrowser())
        webViewLoader.dispose()
        super.dispose()
    }
    
    /**
     * 重写此方法以禁用回车键触发 OK 按钮
     */
    override fun createDefaultActions() {
        super.createDefaultActions()
        // 禁用回车键触发 OK 按钮
        myOKAction = object : OkAction() {
            override fun isEnabled(): Boolean {
                return super.isEnabled()
            }
        }
    }
}