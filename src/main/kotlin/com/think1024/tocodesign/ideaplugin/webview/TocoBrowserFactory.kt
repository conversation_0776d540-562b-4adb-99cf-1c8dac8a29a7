import com.intellij.ui.jcef.JBCefBrowser
import com.think1024.tocodesign.ideaplugin.utils.CookieManager
import org.cef.network.CefCookie
import org.cef.network.CefCookieManager
import java.util.*

object TocoBrowserFactory {

    fun createBrowserWithCookies(url: String, cookies: Map<String, String>, browser: JBCefBrowser?): JBCefBrowser {
        val browser = browser ?: JBCefBrowser(url)
        val cookieManager = CefCookieManager.getGlobalManager()

        val domain = getDomainFromUrl(url)
        
        // 如果是登出操作（U_TOKEN 为空），先尝试删除所有 cookie
        if (cookies.containsKey(CookieManager.U_TOKEN) && cookies[CookieManager.U_TOKEN]?.isEmpty() == true) {
            // 删除所有 cookie
            cookieManager.deleteCookies("", "")  // 空字符串表示删除所有 cookie
            
            // 特别确保删除 U_TOKEN
            val expiredDate = Date(0)  // 1970-01-01，表示已过期
            
            val expiredCookie = CefCookie(
                CookieManager.U_TOKEN,
                "",
                domain,
                "/",
                false,
                false,
                Date(),
                Date(),
                true,
                expiredDate
            )
            
            cookieManager.setCookie(url, expiredCookie)
        }

        // 设置新的 cookie
        val now = Date()
        val thirtyDaysLater = Date(now.time + 30L * 24 * 60 * 60 * 1000)

        cookies.forEach { (name, value) ->
            val cookie = CefCookie(
                name,
                value,
                domain,
                "/",
                false,
                false,
                now,
                now,
                true,
                thirtyDaysLater
            )

            cookieManager.setCookie(url, cookie)
        }

        return browser
    }

    private fun getDomainFromUrl(url: String): String? {
        return try {
            val uri = java.net.URI(url)
            val host = uri.host ?: return "localhost"
            val parts = host.split(".")
            // 排除 IP、localhost、单级域名
            if (host == "localhost" || parts.size < 2 || host.matches(Regex("\\d+\\.\\d+\\.\\d+\\.\\d+"))) {
                null
            } else {
                // 提取根域并加上点前缀
                "." + parts.takeLast(2).joinToString(".")
            }
        } catch (_: Exception) {
            null
        }
    }
}