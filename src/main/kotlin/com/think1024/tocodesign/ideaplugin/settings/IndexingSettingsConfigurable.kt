package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.events.CodeBaseSyncStatusListener
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseVectorIndex
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import javax.swing.*
import java.awt.*
import com.intellij.icons.AllIcons
import com.intellij.openapi.ui.DialogPanel
import com.intellij.ui.AnimatedIcon
import com.intellij.ui.IdeBorderFactory
import com.intellij.ui.components.JBList
import com.intellij.util.ui.JBUI
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseSearchManager
import com.intellij.util.concurrency.AppExecutorUtil
import javax.swing.DefaultListCellRenderer
import javax.swing.BorderFactory
import javax.swing.DefaultListModel
import com.intellij.util.messages.MessageBusConnection

class IndexingSettingsConfigurable(private val project: Project) : Configurable {
    companion object {
        private val logger = Logger.getInstance(IndexingSettingsConfigurable::class.java)
        private val loadingIcon = AnimatedIcon(
            100,
            AllIcons.Process.Step_1,
            AllIcons.Process.Step_2,
            AllIcons.Process.Step_3,
            AllIcons.Process.Step_4,
            AllIcons.Process.Step_5,
            AllIcons.Process.Step_6,
            AllIcons.Process.Step_7,
            AllIcons.Process.Step_8
        )
    }

    private var progressBar: JProgressBar? = null
    private var progressLabel: JLabel? = null
    private var statusLabel: JLabel? = null
    private var syncingFilesList: JBList<String>? = null
    private var codeBaseVectorIndex: CodeBaseVectorIndex? = null
    private var codeBaseSearchManager: CodeBaseSearchManager? = null
    private var startButton: JButton? = null
    private var pauseButton: JButton? = null
    private var continueButton: JButton? = null
    private var resyncButton: JButton? = null
    private var deleteIndexButton: JButton? = null
    private var filesPanel: JPanel? = null
    private var mainPanel: JPanel? = null
    private var progressPanel: JPanel? = null
    private var messageBusConnection: MessageBusConnection? = null

    private var isSyncing: Boolean = false
    private var syncedFiles: Int = 0
    private var totalFiles: Int = 0
    private var currentSyncingFileList: List<String> = emptyList()

    override fun getDisplayName(): String = getI18nString("settings.tab.indexing")

    override fun createComponent(): JComponent {
        codeBaseVectorIndex = project.getService(CodeBaseVectorIndex::class.java)
        codeBaseSearchManager = project.getService(CodeBaseSearchManager::class.java)

        // 初始化状态数据
        initDatas()

        // 订阅同步状态变化事件
        registerSyncStatusListener()

        mainPanel = DialogPanel(GridBagLayout())

        val gbc = GridBagConstraints().apply {
            fill = GridBagConstraints.HORIZONTAL
            weightx = 1.0
            insets = JBUI.insets(5)
            anchor = GridBagConstraints.NORTH
            weighty = 1.0
        }

        gbc.gridy = 0
        mainPanel?.add(createGroupPanel(getI18nString("indexing.codebase.title"), createCodebasePanel()), gbc)

        return mainPanel!!
    }

    private fun initDatas() {
        val notSyncedFiles = codeBaseVectorIndex?.getNotSynedFiles() ?: Int.MAX_VALUE
        currentSyncingFileList = codeBaseVectorIndex?.getCurrentSyncingFiles() ?: emptyList()
        totalFiles = codeBaseSearchManager?.getTotalFiles() ?: 0
        syncedFiles = totalFiles - kotlin.math.min(totalFiles, notSyncedFiles)
        isSyncing = codeBaseVectorIndex?.getIsSyncing() ?: false

        SwingUtilities.invokeLater {
            updateUI()
        }
    }

    private fun registerSyncStatusListener() {
        messageBusConnection = project.messageBus.connect().apply {
            subscribe(CodeBaseSyncStatusListener.TOPIC, object : CodeBaseSyncStatusListener {
                override fun syncingStateChanged(v: Boolean) {
                    SwingUtilities.invokeLater {
                        isSyncing = v
                        updateUI()
                    }
                }

                override fun notSyncedFilesChanged(v: Int) {
                    SwingUtilities.invokeLater {
                        syncedFiles = totalFiles - kotlin.math.min(totalFiles, v)
                        updateUI()
                    }
                }

                override fun totalFilesChanged(v: Int) {
                    SwingUtilities.invokeLater {
                        totalFiles = v
                        updateUI()
                    }
                }

                override fun currentFileListChanged(v: List<String>) {
                    SwingUtilities.invokeLater {
                        currentSyncingFileList = v
                        updateUI()
                    }
                }
            })
        }

    }

    private fun createCodebasePanel(): JPanel {
        val tipPanel = JPanel(BorderLayout())
        val tipLabel = JTextArea(getI18nString("indexing.codebase.tip")).apply {
            isEditable = false
            lineWrap = true
            wrapStyleWord = true
            font = font.deriveFont(Font.PLAIN, 12f)
            border = null
        }
        tipPanel.add(tipLabel, BorderLayout.CENTER)

        progressPanel = JPanel()
        progressPanel?.layout = BoxLayout(progressPanel, BoxLayout.Y_AXIS)

        progressLabel = JLabel("0%").apply {
            font = font.deriveFont(Font.PLAIN, 12f)
            border = null
            alignmentX = Component.LEFT_ALIGNMENT
        }

        progressBar = JProgressBar(0, 100).apply {
            alignmentX = Component.LEFT_ALIGNMENT
        }

        statusLabel = JLabel("0" + " " + getI18nString("indexing.codebase.labelSuccess")).apply {
            font = font.deriveFont(Font.PLAIN, 12f)
            border = null
            alignmentX = Component.LEFT_ALIGNMENT
        }

        progressPanel?.add(progressLabel)
        progressPanel?.add(Box.createVerticalStrut(5))
        progressPanel?.add(progressBar)
        progressPanel?.add(Box.createVerticalStrut(5))
        progressPanel?.add(statusLabel)

        syncingFilesList = JBList<String>().apply {
            font = Font(Font.MONOSPACED, Font.PLAIN, 12)
            cellRenderer = object : DefaultListCellRenderer() {
                override fun getListCellRendererComponent(
                    list: JList<*>,
                    value: Any,
                    index: Int,
                    isSelected: Boolean,
                    cellHasFocus: Boolean
                ): Component {
                    val label =
                        super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus) as JLabel
                    label.icon = AllIcons.FileTypes.Java
                    label.iconTextGap = 8
                    label.border = BorderFactory.createEmptyBorder(2, 4, 2, 4)
                    label.text =
                        "<html><div style=\"overflow:hidden;text-overflow:ellipsis;white-space:no-wrap\">" + value.toString() + "</div></html>"
                    label.toolTipText = value.toString()
                    return label
                }
            }
        }

        filesPanel = JPanel(GridBagLayout()).apply {
            isVisible = false
        }
        val gbcFiles = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.HORIZONTAL
            weightx = 1.0
            gridwidth = GridBagConstraints.REMAINDER
            insets = JBUI.insets(5)  // Add left inset
        }
        gbcFiles.gridy = 0
        filesPanel?.add(syncingFilesList, gbcFiles)

        val buttonPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 5, 0))
        startButton = JButton(getI18nString("indexing.codebase.button.start")).apply {
            isVisible = false
            putClientProperty(
                "DefaultButton.borderColor",
                UIManager.getColor("Button.intellij.native.activeBorderColor")
            )
            putClientProperty("DialogButtonStyle", "primary")  // 设置为 primary 样式
            addActionListener {
                startAction()
            }
        }
        pauseButton = JButton(getI18nString("indexing.codebase.button.pause"), AllIcons.Actions.Pause).apply {
            isVisible = false
            addActionListener {
                pauseAction()
            }
        }
        continueButton =
            JButton(getI18nString("indexing.codebase.button.continue"), AllIcons.Actions.Play_forward).apply {
                isVisible = false
                addActionListener {
                    continueAction()
                }
            }
        resyncButton = JButton(getI18nString("indexing.codebase.button.sync"), AllIcons.Actions.Refresh).apply {
            isVisible = false
            addActionListener {
                startAction()
            }
        }
        deleteIndexButton = JButton(getI18nString("indexing.codebase.button.delete"), AllIcons.Actions.GC).apply {
            isVisible = false
            addActionListener {
                deleteAction()
            }
        }
        buttonPanel.add(startButton)
        buttonPanel.add(pauseButton)
        buttonPanel.add(continueButton)
        buttonPanel.add(resyncButton)
        buttonPanel.add(deleteIndexButton)

        val contentPanel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.HORIZONTAL
            weightx = 1.0
            gridwidth = GridBagConstraints.REMAINDER
            insets = JBUI.insets(5, 15, 5, 5)  // Add left inset
        }

        gbc.gridy = 0
        contentPanel.add(tipPanel, gbc)

        gbc.gridy = 1
        contentPanel.add(progressPanel, gbc)

        gbc.gridy = 2
        contentPanel.add(JSeparator(), gbc)

        gbc.gridy = 3
        contentPanel.add(buttonPanel, gbc)

        gbc.gridy = 4
        contentPanel.add(filesPanel, gbc)

        return contentPanel
    }

    private fun createGroupPanel(title: String, content: JComponent): JPanel {
        val panel = JPanel(BorderLayout())
        panel.border = IdeBorderFactory.createTitledBorder(title, false)
        panel.add(content, BorderLayout.CENTER)
        return panel
    }

    private fun updateUI() {
        val syncedCount = syncedFiles
        val left = kotlin.math.min(syncedCount, totalFiles)
        val right = kotlin.math.max(syncedCount, totalFiles)
        val progress = if (right > 0) (left.toDouble() / right) * 100 else 0.0

        progressBar?.value = progress.toInt()
        progressLabel?.text = String.format("%.1f%%", progress)
        statusLabel?.text = "$syncedCount ${getI18nString("indexing.codebase.labelSuccess")}"

        if (!isSyncing) {
            if (syncedCount == totalFiles && syncedCount > 0) {
                syncedState()
            } else if (syncedCount == 0){
                initState()
            } else {
                pauseState()
            }
        } else {
            syncingState()

            if (currentSyncingFileList.isEmpty()) {
                filesPanel?.isVisible = false
            } else {
                val model = DefaultListModel<String>()
                currentSyncingFileList.forEach { model.addElement(it) }
                syncingFilesList?.model = model
                filesPanel?.isVisible = true
            }
        }
    }

    /**
     * 没有在syncing的初始状态
     * 只显示startButton
     */
    fun initState() {
        startButton?.isVisible = true
        resyncButton?.isVisible = false
        deleteIndexButton?.isVisible = false
        pauseButton?.isVisible = false
        continueButton?.isVisible = false
        progressPanel?.isVisible = false
        filesPanel?.isVisible = false
    }

    /**
     * 正在syncing的状态
     * 显示pause按钮、进度条和文件列表
     */
    fun syncingState() {
        startButton?.isVisible = false
        resyncButton?.isVisible = false
        deleteIndexButton?.isVisible = false
        pauseButton?.isVisible = true
        continueButton?.isVisible = false
        progressPanel?.isVisible = true
        filesPanel?.isVisible = true
    }

    fun pauseState() {
        startButton?.isVisible = false
        resyncButton?.isVisible = false
        deleteIndexButton?.isVisible = true
        pauseButton?.isVisible = false
        continueButton?.isVisible = true
        progressPanel?.isVisible = true
        filesPanel?.isVisible = true
    }

    fun syncedState() {
        startButton?.isVisible = false
        resyncButton?.isVisible = true
        deleteIndexButton?.isVisible = true
        pauseButton?.isVisible = false
        continueButton?.isVisible = false
        progressPanel?.isVisible = true
        filesPanel?.isVisible = false
    }

    /**
     * start/resync的操作
     */
    fun startAction() {

        startButton?.isEnabled = false
        startButton?.icon = loadingIcon
        resyncButton?.isEnabled = false
        resyncButton?.icon = loadingIcon
        AppExecutorUtil.getAppExecutorService().submit {
            try {
                codeBaseSearchManager?.clearIndex()
                SwingUtilities.invokeLater {
                    startButton?.isEnabled = true
                    startButton?.icon = null
                    resyncButton?.isEnabled = true
                    resyncButton?.icon = null
                    syncingState()
                }
                codeBaseSearchManager?.startScanCodeBase()
            } catch (e: Exception) {
                logger.error("Failed to clear index", e)
                SwingUtilities.invokeLater {
                    startButton?.isEnabled = true
                    startButton?.icon = null
                }
            }
        }
    }

    fun pauseAction() {
        pauseState()
        codeBaseVectorIndex?.pauseSync()
    }

    fun continueAction() {
        initDatas()
        syncingState()
        AppExecutorUtil.getAppExecutorService().submit {
            try {
                codeBaseSearchManager?.startScanCodeBase()
            } catch (e: Exception) {
                logger.error("Failed to continue scan code base", e)
            }
        }
    }

    fun deleteAction() {
        deleteIndexButton?.isEnabled = false
        deleteIndexButton?.icon = loadingIcon
        AppExecutorUtil.getAppExecutorService().submit {
            try {
                codeBaseSearchManager?.clearIndex()
                SwingUtilities.invokeLater {
                    deleteIndexButton?.isEnabled = true
                    deleteIndexButton?.icon = AllIcons.Actions.GC
                    initState()
                }
            } catch (e: Exception) {
                logger.error("Failed to clear index", e)
                SwingUtilities.invokeLater {
                    deleteIndexButton?.isEnabled = true
                    deleteIndexButton?.icon = AllIcons.Actions.GC
                }
            }
        }
    }

    override fun isModified(): Boolean = false

    override fun apply() {
        // No settings to apply
    }

    override fun reset() {
        // No settings to reset
    }

    override fun disposeUIResources() {
        messageBusConnection?.disconnect()
        messageBusConnection = null
        mainPanel = null
        progressBar = null
        progressLabel = null
        statusLabel = null
        syncingFilesList = null
        pauseButton = null
        continueButton = null
        resyncButton = null
        deleteIndexButton = null
        filesPanel = null
        codeBaseVectorIndex = null
    }
}
