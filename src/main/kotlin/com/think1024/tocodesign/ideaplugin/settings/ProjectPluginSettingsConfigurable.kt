package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.options.BoundConfigurable
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogPanel

/**
 * Configurable for project-level plugin settings.
 * This class is responsible for creating and managing the UI for project-specific settings.
 *
 * @param project The current project instance
 */
class ProjectPluginSettingsConfigurable(private val project: Project) : BoundConfigurable("Project Plugin Settings") {
    private val component = ProjectPluginSettingsComponent(project)

    /**
     * Creates and returns the UI panel for project-specific settings.
     */
    override fun createPanel(): DialogPanel {
        return component.getPanel()
    }
}