package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.options.BoundConfigurable
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogPanel

/**
 * Configurable for application-level plugin settings.
 * This class is responsible for creating and managing the UI for global application settings.
 * It extends BoundConfigurable to integrate with IntelliJ IDEA's settings framework.
 *
 * @param project The current project instance
 */
class ApplicationPluginSettingsConfigurable(private val project: Project) :
    BoundConfigurable("Application Plugin Settings") {

    // The component that contains the actual UI elements and logic
    private val component = ApplicationPluginSettingsComponent(project)

    // Flag to track if settings have been modified
    private var isModified = false

    init {
        // Set up a callback to be notified when settings are changed
        component.onSettingsChanged = {
            isModified = true
        }
    }

    /**
     * Creates and returns the UI panel for application-specific settings.
     * This method is called by the IntelliJ platform to render the settings UI.
     *
     * @return DialogPanel containing all the settings UI components
     */
    override fun createPanel(): DialogPanel {
        return component.getPanel()
    }

    /**
     * Checks if the settings have been modified.
     * This method is used by the IntelliJ platform to determine if the Apply button should be enabled.
     *
     * @return true if settings have been modified, false otherwise
     */
    override fun isModified(): Boolean = isModified || component.isModified()

    /**
     * Applies the current settings.
     * This method is called when the user clicks the Apply button in the settings dialog.
     */
    override fun apply() {
        component.apply()
        isModified = false
    }

    /**
     * Resets the settings to their last saved state.
     * This method is called when the user clicks the Reset button in the settings dialog.
     */
    override fun reset() {
        component.reset()
        isModified = false
    }
}
