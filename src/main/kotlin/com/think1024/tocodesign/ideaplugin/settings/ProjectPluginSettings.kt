package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.util.xmlb.XmlSerializerUtil
import com.think1024.tocodesign.ideaplugin.listeners.ProjectIdChangeListener
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.properties.Delegates

/**
 * Persistent project settings for the Toco Design plugin.
 * This class stores and manages the plugin's settings, including project configurations and various caches.
 * It implements PersistentStateComponent to allow for automatic saving and loading of settings.
 */
@Service(Service.Level.PROJECT)
@State(name = "ProjectPluginSettings", storages = [Storage("tocodesign_project_settings.xml")])
class ProjectPluginSettings : PersistentStateComponent<ProjectPluginSettings> {
    private val logger = Logger.getInstance(ProjectPluginSettings::class.java)

    private lateinit var project: Project

    /**
     * Cache for configuration data. The outer map key is the file name, the inner map contains property key-value pairs.
     */
    val configCache: MutableMap<String, MutableMap<String, String>> = ConcurrentHashMap()

    /**
     * Cache for file paths. Maps file names to their full paths.
     */
    val filePathCache: MutableMap<String, String> = ConcurrentHashMap()

    /**
     * Cache for module paths. Maps module IDs to their file system paths.
     */
    val moduleIdToPathCache: MutableMap<String, String> = ConcurrentHashMap()

    var ignoreFileModal: Map<String,Boolean> = ConcurrentHashMap()

    private var notifyChanges = false

    var projectId: String? by Delegates.observable(null) { _, oldValue, newValue ->
        if (notifyChanges && oldValue != newValue) {
            project.messageBus.syncPublisher(ProjectIdChangeListener.TOPIC).projectIdChanged(newValue)
            logger.info("ProjectId changed from $oldValue to $newValue")
        }

        // Initialize or remove from bubbleMessagesMap based on newValue
        if (!newValue.isNullOrBlank()) {
            bubbleMessagesMap[newValue] = true
//            logger.info("Initialized bubbleMessagesMap for projectId: $newValue with value true")
        } else {
            oldValue?.let { bubbleMessagesMap.remove(it) }
//            logger.info("Removed projectId: $oldValue from bubbleMessagesMap")
        }
    }

    /**
     * The name of the current project.
     */
    var projectName: String? = null

    // Map to store bubble message settings for different projects
    var bubbleMessagesMap: MutableMap<String, Boolean> = ConcurrentHashMap()

    /**
     * Flag to track whether the settings have been initialized.
     */
    private val initialized = AtomicBoolean(false)

    companion object {
        const val PROJECT_CONFIG_FILE = "project"
        const val PROJECT_NAME_KEY = "name"
        const val PROJECT_ID_KEY = "id"

        /**
         * Retrieves the ProjectPluginSettings instance for the given project.
         *
         * @param project The IntelliJ project
         * @return The ProjectPluginSettings instance
         */
        fun getInstance(project: Project): ProjectPluginSettings {
            val setting = project.getService(ProjectPluginSettings::class.java)
            if (setting.projectId == null && project.basePath != null) {
                // 新建项目时会存在projectId和name没有在cache里的情况导致初始化时读取不到projectId
                // 在出现这种情况时直接读取文件系统
                setting.projectId =  ProjectConfigUtil.getProjectInfo(project.basePath!!)?.get("id")
                setting.projectName =  ProjectConfigUtil.getProjectInfo(project.basePath!!)?.get("name")
            }
            return  setting
        }
    }

    fun initialize(project: Project) {
        this.project = project
    }

    /**
     * Returns the current state of the settings.
     *
     * @return This ProjectPluginSettings instance
     */
    override fun getState(): ProjectPluginSettings = this

    /**
     * Loads the state from the given settings object.
     *
     * @param state The state to load
     */
    override fun loadState(state: ProjectPluginSettings) {
        notifyChanges = false  // Disable notifications during deserialization
        XmlSerializerUtil.copyBean(state, this)
        notifyChanges = true  // Re-enable notifications
    }

    /**
     * Ensures that the settings are initialized only once.
     *
     * @param project The IntelliJ project
     */
    @Synchronized
    fun ensureInitialized(project: Project) {
        if (initialized.compareAndSet(false, true)) {
            initialize(project)
            initializeCache(project)
        }
    }

    /**
     * Forces reinitialization of the settings.
     *
     * @param project The IntelliJ project
     */
    fun forceReinitialization(project: Project) {
        initialized.set(false)
        ensureInitialized(project)
    }

    /**
     * Initializes the cache by reading the project configuration.
     * Note: This should not be called from ProjectConfigUtil.readProjectConfig to avoid infinite recursion.
     *
     * @param project The IntelliJ project
     */
    private fun initializeCache(project: Project) {
        ProjectConfigUtil.readProjectConfig(project, PROJECT_CONFIG_FILE)
    }

    /**
     * Updates the file path cache with a new entry.
     *
     * @param fileName The name of the file
     * @param filePath The full path of the file
     */
    fun updateFilePathCache(fileName: String, filePath: String) {
        filePathCache[fileName] = filePath
    }

    /**
     * Clears the configuration cache, either for a specific file or entirely.
     *
     * @param fileName The name of the file to clear from cache, or null to clear all
     */
    fun clearConfigCache(fileName: String? = null) {
        if (fileName != null) {
            configCache.remove(fileName)
        } else {
            configCache.clear()
        }
    }

    /**
     * Clears the file path cache, either for a specific file or entirely.
     *
     * @param fileName The name of the file to clear from cache, or null to clear all
     */
    fun clearFilePathCache(fileName: String? = null) {
        if (fileName != null) {
            filePathCache.remove(fileName)
        } else {
            filePathCache.clear()
        }
    }

    /**
     * Updates a cached property for a specific file.
     *
     * @param fileName The name of the file
     * @param propertyName The name of the property
     * @param propertyValue The value of the property
     */
    fun updateCachedProperty(fileName: String, propertyName: String, propertyValue: String) {
        configCache.getOrPut(fileName) { mutableMapOf() }[propertyName] = propertyValue
    }

    /**
     * Updates the module ID to path cache.
     *
     * @param moduleId The ID of the module
     * @param modulePath The file system path of the module
     */
    fun updateModuleIdToPathCache(moduleId: String, modulePath: String) {
        moduleIdToPathCache[moduleId] = modulePath
    }

    /**
     * Retrieves the module path from the cache for a given module ID.
     *
     * @param moduleId The ID of the module
     * @return The file system path of the module, or null if not found
     */
    fun getModulePathFromCache(moduleId: String): String? = moduleIdToPathCache[moduleId]

    /**
     * Clears the module ID to path cache.
     */
    fun clearModuleIdToPathCache() = moduleIdToPathCache.clear()

    fun shouldShowIgnoreFileModal(moduleId: String?): Boolean {
        if (moduleId.isNullOrEmpty()) {
            return false
        }
        return ignoreFileModal[moduleId] != false
    }

    fun setShouldShowIgnoreFileModal(moduleId: String?, shouldShow: Boolean) {
        if (moduleId.isNullOrEmpty()) {
            return
        }
        ignoreFileModal = ignoreFileModal + (moduleId to shouldShow)
    }
}
