package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogPanel
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.Messages
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.JBUI
import com.think1024.tocodesign.ideaplugin.utils.JdkCompatibilityChecker
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.getPluginSettings
import java.awt.*
import javax.swing.*

/**
 * ProjectPluginSettingsComponent is responsible for creating and managing the UI
 * for project-specific plugin settings.
 *
 * @property project The current IntelliJ IDEA project.
 */
class ProjectPluginSettingsComponent(private val project: Project) {
    private val settings = project.getPluginSettings()

    // Store original project ID and name for comparison
    private var originalProjectId: String = ""
    private var originalProjectName: String = ""

    // Labels for project ID and name, set to bold font
    private val projectIdLabel = JBLabel(getI18nString("project.id.label")).apply {
        font = font.deriveFont(Font.BOLD)
    }

    private val projectNameLabel = JBLabel(getI18nString("project.name.label")).apply {
        font = font.deriveFont(Font.BOLD)
    }

    // Labels to display project ID and name values
    private val projectIdValue = JBLabel().apply {
        text = project.getPluginSettings().projectId ?: ""
        originalProjectId = text
    }

    private val projectNameValue = JBLabel().apply {
        text = project.getPluginSettings().projectName ?: ""
        originalProjectName = text
    }

    // Checkbox to toggle bubble messages
    private val bubbleMessageToggle = JCheckBox(getI18nString("toggle.bubble.messages.current.project")).apply {
        // Retrieve current project ID
        val currentProjectId = settings.projectId ?: ""

        // Set checkbox state based on the current project's bubble message setting
        isSelected = settings.bubbleMessagesMap[currentProjectId] ?: true

        // Add action listener to update the setting when the checkbox is toggled
        addActionListener { toggleBubbleMessages(isSelected) }
    }

    // Button to trigger project configuration check
    private val checkButton = JButton(getI18nString("button.check")).apply {
        addActionListener { checkProject() }
    }

    // Label to display status messages
    private val messageLabel = JBLabel().apply {
        border = JBUI.Borders.emptyLeft(5)
        isVisible = false
    }

    /**
     * A text area for displaying the project configuration description.
     * This component is styled to look like a label but with text wrapping capabilities.
     */
    private val descriptionLabel = JTextArea(getI18nString("project.config.description")).apply {
        isEditable = false
        isOpaque = false
        lineWrap = true
        wrapStyleWord = true
        font = JBLabel().font
        foreground = JBColor.GRAY
        border = JBUI.Borders.emptyTop(5)
    }

    /**
     * Label for JDK version input, set to bold font
     */
    private val jdkVersionLabel = JBLabel(getI18nString("jdk.version.label")).apply {
        font = font.deriveFont(Font.BOLD)
    }

    /**
     * List of available JDK versions, sorted by their corresponding integer values
     */
    private val jdkVersions = JdkCompatibilityChecker.JDK_VERSION_MAP.entries
        .sortedBy { it.value }
        .map { it.key }

    /**
     * Placeholder text for JDK version input, showing the range of available versions
     */
    private val placeholderText = "[${jdkVersions.first()}, ${jdkVersions.last()}]"

    /**
     * Text field for JDK version input with placeholder text
     */
    private val jdkVersionInput = JBTextField().apply {
        columns = 15
        emptyText.text = placeholderText
    }

    /**
     * Button to trigger JDK compatibility check
     */
    private val checkJdkCompatibilityButton = JButton(getI18nString("button.check.jdk.compatibility")).apply {
        addActionListener { checkJdkCompatibility() }
    }

    /**
     * Creates and returns the settings panel.
     *
     * @return A DialogPanel containing the settings UI.
     */
    fun getPanel(): DialogPanel {
        return DialogPanel().apply {
            layout = GridBagLayout()
            val gbc = GridBagConstraints().apply {
                fill = GridBagConstraints.HORIZONTAL
                anchor = GridBagConstraints.NORTHWEST
                weightx = 1.0
                gridwidth = GridBagConstraints.REMAINDER
                insets = JBUI.insetsBottom(2)
            }

            // Add a small top padding
            gbc.weighty = 0.0
            add(Box.createVerticalStrut(JBUI.scale(5)), gbc)

            // Add the labeled components (project ID and name)
            add(createLabeledComponents(), gbc)

            // Add check button and message label for project configuration
            gbc.insets = JBUI.insets(10, 0, 2, 0)
            gbc.weighty = 0.0
            add(createCheckButtonWithMessage(), gbc)

            // Add separator between project config and JDK compatibility sections
            gbc.insets = JBUI.insets(10, 0)
            add(JSeparator(), gbc)

            // Add toggle for bubble messages
            gbc.insets = JBUI.insets(5, 0, 2, 0)
            add(bubbleMessageToggle, gbc)

            // Add JDK version input and check button
            gbc.insets = JBUI.insetsBottom(2)
            gbc.weighty = 0.0
            add(createJdkCompatibilityComponents(), gbc)

            // Add vertical glue to push all components to the top
            gbc.weighty = 1.0
            add(Box.createVerticalGlue(), gbc)

            // Check and display error message if projectId or projectName is empty
            if (projectIdValue.text.isBlank() || projectNameValue.text.isBlank()) {
                showInlineMessage(getI18nString("error.invalid.config"), isError = true)
            }
        }
    }

    /**
     * Toggles the display of bubble messages based on user preference.
     *
     * @param showMessages True if bubble messages should be shown, false otherwise.
     */
    private fun toggleBubbleMessages(showMessages: Boolean) {
        val currentProjectId = settings.projectId ?: ""

        // Update the bubble message setting for the current project
        settings.bubbleMessagesMap[currentProjectId] = showMessages

        // Save settings if necessary (depends on implementation)
    }

    /**
     * Creates a panel with labeled components (project ID and name).
     *
     * @return JPanel containing the labeled components.
     */
    private fun createLabeledComponents(): JPanel {
        return JPanel(GridBagLayout()).apply {
            val gbc = GridBagConstraints().apply {
                fill = GridBagConstraints.HORIZONTAL
                anchor = GridBagConstraints.WEST
                insets = JBUI.insets(2)
            }

            // Add project ID label and value
            gbc.gridx = 0
            gbc.gridy = 0
            gbc.weightx = 0.0
            add(projectIdLabel, gbc)

            gbc.gridx = 1
            gbc.weightx = 1.0
            add(projectIdValue, gbc)

            // Add project name label and value
            gbc.gridx = 0
            gbc.gridy = 1
            gbc.weightx = 0.0
            add(projectNameLabel, gbc)

            gbc.gridx = 1
            gbc.weightx = 1.0
            add(projectNameValue, gbc)

            // Add invisible component to push everything to the left
            gbc.gridx = 2
            gbc.weightx = 1.0
            add(Box.createHorizontalGlue(), gbc)
        }
    }

    /**
     * Creates a panel containing the check button, message label, and description label.
     *
     * @return JPanel containing the check button, message label, and description label.
     */
    private fun createCheckButtonWithMessage(): JPanel {
        return JPanel(BorderLayout()).apply {
            // Create a sub-panel for the check button and message label
            add(JPanel().apply {
                layout = BoxLayout(this, BoxLayout.X_AXIS)
                add(checkButton)
                add(Box.createHorizontalStrut(5))
                add(messageLabel)
                add(Box.createHorizontalGlue())
            }, BorderLayout.NORTH)

            // Add the description label to the center of the panel
            add(descriptionLabel, BorderLayout.CENTER)

            border = JBUI.Borders.empty(5)

            // Set preferred size for the description label
            val parentWidth = preferredSize.width
            descriptionLabel.preferredSize = Dimension(parentWidth, 50)
        }
    }

    /**
     * Creates a panel containing the JDK compatibility check components.
     *
     * This method assembles a panel with the following components:
     * - JDK version label
     * - JDK version input field
     * - JDK compatibility check button
     *
     * The components are arranged horizontally using a left-aligned FlowLayout.
     *
     * @return JPanel containing the JDK compatibility check components
     */
    private fun createJdkCompatibilityComponents(): JPanel {
        return JPanel(FlowLayout(FlowLayout.LEFT)).apply {
            // Add the JDK version label to the panel
            add(jdkVersionLabel)

            // Add the JDK version input field to the panel
            add(jdkVersionInput)

            // Add the JDK compatibility check button to the panel
            add(checkJdkCompatibilityButton)
        }
    }

    /**
     * Checks the project configuration and updates the UI accordingly.
     * This method is called when the check button is clicked.
     */
    private fun checkProject() {
        ProjectConfigUtil.clearCache(project, ProjectPluginSettings.PROJECT_CONFIG_FILE)
        val config = ProjectConfigUtil.readProjectConfig(project, ProjectPluginSettings.PROJECT_CONFIG_FILE)

        if (config.isNullOrEmpty()) {
//            showInlineMessage(getI18nString("error.config.not.found"), isError = true)
            updateUIIfDifferent(null, null)
            return
        }

        val newProjectId = config["id"]
        val newProjectName = config["name"]

        if (newProjectId.isNullOrEmpty() || newProjectName.isNullOrEmpty()) {
            showInlineMessage(getI18nString("error.invalid.config"), isError = true)
            updateUIIfDifferent(newProjectId, newProjectName)
            return
        }

        updateUIIfDifferent(newProjectId, newProjectName)
        showInlineMessage(getI18nString("success.config.updated"), isError = false)
    }

    /**
     * Updates the UI labels if the new values are different from the original ones.
     *
     * @param newProjectId The new project ID.
     * @param newProjectName The new project name.
     */
    private fun updateUIIfDifferent(newProjectId: String?, newProjectName: String?) {
        if (newProjectId != originalProjectId) {
            projectIdValue.text = newProjectId ?: ""
            originalProjectId = newProjectId ?: ""
        }

        if (newProjectName != originalProjectName) {
            projectNameValue.text = newProjectName ?: ""
            originalProjectName = newProjectName ?: ""
        }
    }

    /**
     * Displays an inline message with the specified text and color.
     *
     * @param message The message to display.
     * @param isError If true, the message is displayed in red; otherwise, in green.
     */
    private fun showInlineMessage(message: String, isError: Boolean) {
        messageLabel.text = message
        messageLabel.foreground = if (isError) JBColor.RED else JBColor.GREEN
        messageLabel.isVisible = true
    }

    /**
     * Clears the inline message and hides the message label.
     */
    private fun clearMessage() {
        messageLabel.text = ""
        messageLabel.isVisible = false
    }

    /**
     * Checks the JDK compatibility based on the user input.
     *
     * This function performs the following steps:
     * 1. Validates the input JDK version
     * 2. Checks if the JDK version is supported
     * 3. Checks for incompatible items
     * 4. Displays appropriate messages based on the results
     */
    private fun checkJdkCompatibility() {
        // Get the target JDK version from the input field
        val targetJdkVersion = jdkVersionInput.text

        // Check if the input is empty
        if (targetJdkVersion.isBlank()) {
            Messages.showErrorDialog(project, getI18nString("error.jdk.version.empty"), getI18nString("error.title"))
            return
        }

        // Check if the JDK version is supported
        if (!JdkCompatibilityChecker.checkAndNotifySupportedJdkVersion(project, targetJdkVersion)) {
            return
        }

        // Check for incompatible items
        val incompatibleItems = JdkCompatibilityChecker.checkCompatibility(project, targetJdkVersion)

        // Display results
        if (incompatibleItems.isEmpty()) {
            Messages.showInfoMessage(
                project,
                getI18nString("success.jdk.compatibility"),
                getI18nString("success.title")
            )
        } else {
            showIncompatibleItemsDialog(incompatibleItems)
        }
    }

    /**
     * Displays a dialog showing incompatible items.
     *
     * This function creates a custom dialog to display the list of incompatible items.
     * The dialog includes a scrollable text area with the incompatible items and a close button.
     *
     * @param incompatibleItems List of strings representing incompatible items
     */
    private fun showIncompatibleItemsDialog(incompatibleItems: List<String>) {
        object : DialogWrapper(project) {
            init {
                title = getI18nString("dialog.incompatible.items.title")
                init()
            }

            override fun createCenterPanel(): JComponent {
                // Create a text area to display incompatible items
                val message = JTextArea(incompatibleItems.joinToString("\n")).apply {
                    isEditable = false
                    lineWrap = true
                    wrapStyleWord = true
                    font = JBUI.Fonts.create("Monospaced", 12)
                    border = JBUI.Borders.empty(10)
                }

                // Wrap the text area in a scroll pane
                val scrollPane = JBScrollPane(message).apply {
                    preferredSize = Dimension(400, 500)
                }

                // Create a panel to hold the scroll pane
                return JPanel(BorderLayout()).apply {
                    add(scrollPane, BorderLayout.CENTER)
                }
            }

            override fun createActions(): Array<Action> {
                // Create a close button for the dialog
                val closeAction = object : AbstractAction(getI18nString("dialog.button.close")) {
                    override fun actionPerformed(e: java.awt.event.ActionEvent) {
                        close(CLOSE_EXIT_CODE)
                    }
                }
                return arrayOf(closeAction)
            }
        }.show()
    }
}
