package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.options.ConfigurationException
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.ComboBox
import com.intellij.openapi.ui.DialogPanel
import com.intellij.openapi.ui.Messages
import com.intellij.ui.IdeBorderFactory
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.JBUI
import com.think1024.tocodesign.ideaplugin.ui.LoginHandler
import com.think1024.tocodesign.ideaplugin.ui.UIUtil
import com.think1024.tocodesign.ideaplugin.utils.ColorUtil
import com.think1024.tocodesign.ideaplugin.utils.Constants
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.webview.WebViewManager
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.net.URI
import javax.swing.*

/**
 * This class represents the settings component for the application plugin.
 * It creates and manages the UI for plugin settings, including account management,
 * general settings, and privacy statement.
 *
 * @property project The current IntelliJ IDEA project
 */
class ApplicationPluginSettingsComponent(private val project: Project) {
    // UI components
    private lateinit var usernameLabel: JLabel
    private lateinit var loginButton: JButton
    private lateinit var logoutButton: JButton
    private var languageComboBox: ComboBox<String> =
        ComboBox(DefaultComboBoxModel(arrayOf("System Default", "English", "简体中文")))
    private val hostTextField = JBTextField()
    private val frontendHostTextField = JBTextField()
    private val showBubbleMessagesCheckBox = JCheckBox(getI18nString("toggle.bubble.messages"))
    private val inlineCodeCompletionCheckBox = JCheckBox(getI18nString("inline.completion.label"))
    private val onlineWebCheckBox = JCheckBox(getI18nString("online.web.label"))

    // Custom button to allow color customization
    class ColorButton : JButton() {
        private val logger = Logger.getInstance(ColorButton::class.java)

        init {
            isOpaque = true
            // Use reflection to access the private 'contentAreaFilled' field
            try {
                val field = AbstractButton::class.java.getDeclaredField("contentAreaFilled")
                field.isAccessible = true
                field.set(this, false)
            } catch (e: Exception) {
                logger.warn("Failed to access 'contentAreaFilled' field", e)
            }
        }
    }

    // Highlight color button
    private val highlightColorButton = ColorButton().apply {
        // Set initial background color from settings
        background = ColorUtil.stringToColor(ApplicationPluginSettings.getInstance().highlightBackgroundColorValue)
        border = BorderFactory.createLineBorder(JBColor.BLACK)

        preferredSize = Dimension(60, 25) // Smaller button width

        // Set tooltip for instruction
        toolTipText = getI18nString("highlight.color.instruction")

        // Add action listener for color selection
        addActionListener {
            val chosenColor = JColorChooser.showDialog(null, getI18nString("choose.highlight.color"), background)
            if (chosenColor != null) {
                background = chosenColor // Change button color to selected color
                repaint() // Ensure button is repainted to show color change
                notifyChanged() // Notify listeners about the change
            }
        }
    }

    // Reset color button
    private val resetColorButton = JButton(getI18nString("reset.color")).apply {
        addActionListener {
            highlightColorButton.background = JBColor.LIGHT_GRAY // Reset to default color
            highlightColorButton.repaint()
            notifyChanged()
        }
    }

    // Callback for notifying changes
    var onSettingsChanged: () -> Unit = {}

    // Data class to store original values
    private data class OriginalValues(
        val host: String,
        val frontendHost: String,
        val language: String,
        val locateAppShortcut: String,
        val showBubbleMessages: Boolean,
        val inlineCodeCompletion: Boolean,
        val onlineWeb: Boolean,
        val highlightBackgroundColorValue: String
    )

    // Property to store original values
    private lateinit var originalValues: OriginalValues

    /**
     * Getter and setter for the host text field.
     * This allows external access to the host text field value.
     */
    var hostText: String
        get() = hostTextField.text
        set(value) {
            hostTextField.text = value
        }

    /**
     * Creates and returns the main settings panel.
     * This panel includes all setting groups: Account, General Settings, and Privacy Statement.
     *
     * @return DialogPanel containing all settings UI components
     */
    fun getPanel(): DialogPanel {
        val mainPanel = DialogPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            fill = GridBagConstraints.HORIZONTAL
            weightx = 1.0
            insets = JBUI.insets(5)
        }

        gbc.gridy = 0
        mainPanel.add(createGroupPanel(getI18nString("account.title"), createAccountPanel()), gbc)

        gbc.gridy++
        mainPanel.add(createGroupPanel(getI18nString("general.settings.title"), createGeneralSettingsPanel()), gbc)

        gbc.gridy++
        mainPanel.add(createGroupPanel(getI18nString("locator.settings.title"), createLocatorSettingsPanel()), gbc)

        gbc.gridy++
        mainPanel.add(createGroupPanel(getI18nString("privacy.statement.title"), createPrivacyStatementPanel()), gbc)

        gbc.gridy++
        gbc.weighty = 1.0
        gbc.fill = GridBagConstraints.BOTH
        mainPanel.add(JPanel(), gbc)  // Add an empty panel to fill remaining space

        // Initialize UI state
        updateSettingsUI()

        // Store original values
        storeOriginalValues()

        // Add listeners to detect changes
        addChangeListeners()

        return mainPanel
    }

    /**
     * Stores the original values of the settings.
     * This is used to properly reset the settings if changes are canceled.
     */
    private fun storeOriginalValues() {
        val settings = ApplicationPluginSettings.getInstance()
        originalValues = OriginalValues(
            host = settings.host,
            frontendHost = settings.frontendHost,
            language = settings.language,
            locateAppShortcut = settings.locateAppShortcut,
            showBubbleMessages = settings.showBubbleMessages,
            inlineCodeCompletion = settings.inlineCodeCompletion,
            onlineWeb = settings.onlineWeb,
            highlightBackgroundColorValue = settings.highlightBackgroundColorValue
        )
    }

    /**
     * Creates a panel with a titled border to group related components.
     * This method is used to create visually distinct sections in the settings UI.
     *
     * @param title The title of the group
     * @param content The content component of the group
     * @return JPanel with the specified title and content
     */
    private fun createGroupPanel(title: String, content: JComponent): JPanel {
        val panel = JPanel(BorderLayout())
        panel.border = IdeBorderFactory.createTitledBorder(title, false)
        panel.add(content, BorderLayout.CENTER)
        return panel
    }

    /**
     * Creates the account settings panel.
     * This panel contains the username label, login button, and logout button in a single row.
     *
     * @return JPanel containing account-related UI components
     */
    private fun createAccountPanel(): JPanel {
        val accountPanel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.NONE
            insets = JBUI.insets(5, 15, 5, 5)  // Add left inset
        }

        // Create and configure the username label
        usernameLabel = JBLabel(getI18nString("account.not.logged.in")).apply {
            preferredSize = Dimension(200, preferredSize.height)
        }

        // Create and configure the login button
        loginButton = JButton(getI18nString("account.login")).apply {
            addActionListener {
                // Check and apply settings if modified
                if (!checkAndApplySettingsIfModified()) {
                    return@addActionListener // Exit if the user does not want to apply changes
                }

                validateAndLogin()
                updateSettingsUI()
            }
        }

        // Create and configure the logout button
        logoutButton = JButton(getI18nString("account.logout")).apply {
            addActionListener {
                // Check and apply settings if modified
                if (!checkAndApplySettingsIfModified()) {
                    return@addActionListener // Exit if the user does not want to apply changes
                }

                LoginHandler.getInstance(project).handleLogout()
                updateSettingsUI()
            }
        }

        // Add username label
        gbc.gridx = 0
        gbc.weightx = 0.0
        accountPanel.add(usernameLabel, gbc)

        // Add login button
        gbc.gridx = 1
        gbc.insets.left = 10 // Add some space between label and buttons
        accountPanel.add(loginButton, gbc)

        // Add logout button
        gbc.gridx = 2
        gbc.insets.left = 5 // Less space between buttons
        accountPanel.add(logoutButton, gbc)

        // Add empty panel to push components to the left
        gbc.gridx = 3
        gbc.weightx = 1.0
        accountPanel.add(JPanel(), gbc)

        return accountPanel
    }

    /**
     * Checks if the settings have been modified. If they have, prompts the user
     * whether to apply the changes. If the user chooses to apply, it calls the apply() method.
     * If the user chooses not to apply, it returns false.
     *
     * @return true if the changes were applied, false otherwise
     */
    private fun checkAndApplySettingsIfModified(): Boolean {
        // Check if settings are modified before proceeding
        if (isModified()) {
            val response = Messages.showYesNoDialog(
                project,
                getI18nString("message.settings.not.applied"),
                getI18nString("title.settings.changed"),
                Messages.getWarningIcon()
            )
            if (response == Messages.YES) {
                apply() // Apply the settings if the user chooses to
                return true // Indicate that changes were applied
            } else {
                return false // Indicate that changes were not applied
            }
        }
        return true // No modifications, proceed
    }

    /**
     * Validates the host input and initiates the login process if the host is valid.
     * This method is called when the login button is clicked. It performs the following actions:
     */
    private fun validateAndLogin() {
        val host = hostTextField.text.trim() // Trim the host input text
        when {
            host.isEmpty() -> { // Check if the host input is empty
                flashHostField(getI18nString("message.error.host.empty")) // Flash error message for empty host
            }

            !validateHost(host) -> { // Validate host input
                // Error message already handled in validateHost
            }

            else -> {
                LoginHandler.getInstance(project).handleLogin {
                    updateSettingsUI()
                }
////                val loginUrl = host + ConfigUtil.getProperty("url.path.login") // Construct the login URL
////                if (!HttpUtil.isUrlAccessible(loginUrl)) { // Check if the login URL is accessible
//                    flashHostField(getI18nString("message.error.host.unreachable")) // Flash error message for unreachable URL
//                } else {
//                    val dialog = LoginDialog(project) // Create a login dialog
//                    dialog.showAndGet() // Show the login dialog and proceed with login
//                }
            }
        }
    }


    /**
     * Flashes the host text field and displays a temporary error message.
     * This provides visual feedback for invalid input.
     *
     * @param errorMessage The error message to display
     */
    private fun flashHostField(errorMessage: String) {
        UIUtil.flashComponent(hostTextField)
        UIUtil.showTemporaryTooltip(hostTextField, errorMessage)
    }

    /**
     * Creates the general settings panel.
     * This panel includes settings for host, display language, and locate app shortcut.
     *
     * @return JPanel containing general UI components
     */
    private fun createGeneralSettingsPanel(): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.NONE
            insets = JBUI.insets(5, 15, 5, 5)  // Add left inset
        }

        // Show bubble messages
        panel.add(showBubbleMessagesCheckBox, gbc)
        // code completion
        panel.add(inlineCodeCompletionCheckBox, gbc)
        // online web
        panel.add(onlineWebCheckBox, gbc)

        // Host settings
        gbc.gridy = 1
        gbc.gridx = 0
        val host = ApplicationPluginSettings.getInstance().host
        hostTextField.text = host
        panel.add(JLabel(getI18nString("host.label")), gbc)
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        hostTextField.preferredSize = Dimension(300, hostTextField.preferredSize.height)
        panel.add(hostTextField, gbc)

        // Frontend Host settings
        gbc.gridy = 2
        gbc.gridx = 0
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        val frontendHost = ApplicationPluginSettings.getInstance().frontendHost
        frontendHostTextField.text = frontendHost
        panel.add(JLabel(getI18nString("frontend.host.label", "前端Host")), gbc)
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        frontendHostTextField.preferredSize = Dimension(300, frontendHostTextField.preferredSize.height)
        panel.add(frontendHostTextField, gbc)

        // Language settings
        gbc.gridy = 3
        gbc.gridx = 0
        gbc.fill = GridBagConstraints.NONE
        gbc.weightx = 0.0
        panel.add(JLabel(getI18nString("language.label")), gbc)
        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        languageComboBox.preferredSize = Dimension(300, languageComboBox.preferredSize.height)
        panel.add(languageComboBox, gbc)

        return panel
    }

    private fun createLocatorSettingsPanel(): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.NONE
            insets = JBUI.insets(5, 15, 5, 5)
        }

        gbc.gridx = 0
        gbc.gridy = 0
        panel.add(JBLabel(getI18nString("highlight.background.color")), gbc)

        gbc.gridx = 1
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        panel.add(highlightColorButton, gbc)

        gbc.gridx = 2
        gbc.fill = GridBagConstraints.NONE
        panel.add(resetColorButton, gbc)

        return panel
    }

    /**
     * Creates the privacy statement panel.
     * This panel contains clickable links to the Service Agreement and Privacy Policy.
     *
     * @return JPanel containing privacy-related UI components with hyperlink behavior
     */
    private fun createPrivacyStatementPanel(): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints().apply {
            anchor = GridBagConstraints.WEST
            fill = GridBagConstraints.NONE
            insets = JBUI.insets(5, 15, 5, 5)  // Add left inset to align with other panels
        }

        // Create the Service Agreement link
        val serviceAgreementLabel = JLabel("<html><a href=''>${getI18nString("service.agreement.label")}</a></html>")
        addHyperlinkBehavior(serviceAgreementLabel, "https://your-service-agreement-url")
        panel.add(serviceAgreementLabel, gbc)

        // Create the Privacy Policy link
        gbc.gridx = 1
        gbc.insets.left = 20  // Add space between links
        val privacyPolicyLabel = JLabel("<html><a href=''>${getI18nString("privacy.policy.label")}</a></html>")
        addHyperlinkBehavior(privacyPolicyLabel, "https://your-privacy-policy-url")
        panel.add(privacyPolicyLabel, gbc)

        // Add empty panel to push components to the left
        gbc.gridx = 2
        gbc.weightx = 1.0
        panel.add(JPanel(), gbc)

        return panel
    }

    /**
     * Adds hyperlink behavior to a JLabel.
     * This method sets the cursor to a hand cursor when hovering over the label
     * and opens the specified URL in the default browser when the label is clicked.
     *
     * @param label The JLabel to which hyperlink behavior will be added
     * @param url The URL to open when the label is clicked
     */
    private fun addHyperlinkBehavior(label: JLabel, url: String) {
        label.cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
        label.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                if (Desktop.isDesktopSupported()) {
                    try {
                        Desktop.getDesktop().browse(URI(url))
                    } catch (ex: Exception) {
                        // Handle exception (e.g., show error message)
                        ex.printStackTrace()
                    }
                }
            }
        })
    }

    /**
     * Updates the UI based on the current login state.
     * This method is called after successful login/logout operations to reflect the current user state.
     */
    private fun updateSettingsUI() {
        val settings = ApplicationPluginSettings.getInstance()
        if (settings.loggedIn) {
            usernameLabel.text = "${settings.username} (${settings.nickname})"
            loginButton.isVisible = false
            logoutButton.isVisible = true
        } else {
            usernameLabel.text = getI18nString("account.not.logged.in")
            loginButton.isVisible = true
            logoutButton.isVisible = false
        }
    }

    /**
     * Adds change listeners to all editable components.
     * This ensures that any changes in the UI components trigger the onSettingsChanged callback.
     */
    private fun addChangeListeners() {
        hostTextField.document.addDocumentListener(SimpleDocumentListener { notifyChanged() })
        languageComboBox.addActionListener { notifyChanged() }
        showBubbleMessagesCheckBox.addActionListener { notifyChanged() }
        inlineCodeCompletionCheckBox.addActionListener { notifyChanged() }
        onlineWebCheckBox.addActionListener { notifyChanged() }
    }

    /**
     * Notifies that settings have been changed.
     * This method calls the onSettingsChanged callback to inform listeners of the change.
     */
    private fun notifyChanged() {
        onSettingsChanged()
    }

    /**
     * Validates the given host URL against the defined pattern.
     * If the host is invalid, it displays an error message and returns false.
     *
     * @param host The host URL to validate
     * @return true if the host is valid, false otherwise
     */
    private fun validateHost(host: String): Boolean {
        if (!Constants.HOST_URL_PATTERN.matcher(host).matches()) {
            flashHostField(getI18nString("message.error.host.invalid")) // Flash error message for invalid host URL
            return false
        }
        return true
    }

    /**
     * Applies the current settings to the ApplicationPluginSettings instance.
     * This method is called when the user confirms the changes in the settings dialog.
     *
     * @throws ConfigurationException if there's an error in the configuration
     */
    @Throws(ConfigurationException::class)
    fun apply() {
        // Store original values before applying changes
        storeOriginalValues()

        // Temporary variables to hold new values
        val newHost = hostTextField.text
        val newFrontendHost = frontendHostTextField.text
        val newLanguage = languageComboBox.selectedItem as? String ?: "System Default"
        val newShowBubbleMessages = showBubbleMessagesCheckBox.isSelected
        val newInlineCodeCompletion = inlineCodeCompletionCheckBox.isSelected
        val onlineWeb = onlineWebCheckBox.isSelected

        // Validate host input
        if (!validateHost(newHost)) {
            throw ConfigurationException(getI18nString("message.error.host.invalid")) // Throw exception to prevent applying invalid settings
        }

        // Check if language has changed
        if (newLanguage != originalValues.language) {
            // Ensure the write operation is executed in a write-safe context
            ApplicationManager.getApplication().invokeLater({
                val message = getI18nString("message.language.change.restart")
                val title = getI18nString("title.settings.changed")
                Messages.showInfoMessage(project, message, title)
            }, ModalityState.defaultModalityState())
        }

        if (onlineWeb != originalValues.onlineWeb) {
            WebViewManager.getAllBrowsers(project)?.forEach {
                it.reload()
            }
        }

        // If we've reached this point, all operations were successful
        // Now we can safely update the settings
        val settings = ApplicationPluginSettings.getInstance()
        settings.previousHost = originalValues.host // Store previous host
        settings.host = newHost
        settings.previousFrontendHost = originalValues.frontendHost
        settings.frontendHost = newFrontendHost
        settings.language = newLanguage
        settings.showBubbleMessages = newShowBubbleMessages
        settings.inlineCodeCompletion = newInlineCodeCompletion
        settings.onlineWeb = onlineWeb
        settings.highlightBackgroundColorValue = ColorUtil.colorToString(highlightColorButton.background)

        // After successful application, update the original values
        storeOriginalValues()
    }

    /**
     * Checks if the current settings are modified compared to the original values.
     * This is used to determine if the Apply button should be enabled in the settings dialog.
     *
     * @return true if settings are modified, false otherwise
     */
    fun isModified(): Boolean {
        return originalValues.host != hostTextField.text ||
                originalValues.frontendHost != frontendHostTextField.text ||
                originalValues.language != (languageComboBox.selectedItem as? String ?: "System Default") ||
                originalValues.showBubbleMessages != showBubbleMessagesCheckBox.isSelected ||
                originalValues.inlineCodeCompletion !=  inlineCodeCompletionCheckBox.isSelected ||
                originalValues.onlineWeb != onlineWebCheckBox.isSelected ||
                originalValues.highlightBackgroundColorValue != ColorUtil.colorToString(highlightColorButton.background)

    }

    /**
     * Resets the UI components to the original values.
     * This method is called when the user cancels the changes in the settings dialog.
     */
    fun reset() {
        hostTextField.text = originalValues.host
        frontendHostTextField.text = originalValues.frontendHost
        languageComboBox.selectedItem = originalValues.language
        showBubbleMessagesCheckBox.isSelected = originalValues.showBubbleMessages
        inlineCodeCompletionCheckBox.isSelected = originalValues.inlineCodeCompletion
        onlineWebCheckBox.isSelected = originalValues.onlineWeb
        highlightColorButton.background = ColorUtil.stringToColor(originalValues.highlightBackgroundColorValue)
    }
}

/**
 * A simple document listener that calls a provided function when the document changes.
 * This is used to detect changes in text fields and trigger the onSettingsChanged callback.
 */
private class SimpleDocumentListener(val onChange: () -> Unit) : javax.swing.event.DocumentListener {
    override fun insertUpdate(e: javax.swing.event.DocumentEvent) = onChange()
    override fun removeUpdate(e: javax.swing.event.DocumentEvent) = onChange()
    override fun changedUpdate(e: javax.swing.event.DocumentEvent) = onChange()
}

/**
 * A simple key listener that calls a provided function when a key is released.
 * This is used to detect changes in key mapping fields and trigger the onSettingsChanged callback.
 */
private class SimpleKeyListener(val onChange: () -> Unit) : java.awt.event.KeyAdapter() {
    override fun keyReleased(e: java.awt.event.KeyEvent) = onChange()
}