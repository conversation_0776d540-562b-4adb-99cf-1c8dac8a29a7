@file:Suppress("UnstableApiUsage")

package com.think1024.tocodesign.ideaplugin.toolWindow

import com.intellij.CommonBundle
import com.intellij.configurationStore.StoreReloadManager
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffDialogHints
import com.intellij.diff.DiffManager
import com.intellij.diff.DiffRequestFactory
import com.intellij.diff.merge.MergeResult
import com.intellij.diff.merge.MergeUtil
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUtil
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.command.WriteCommandAction.writeCommandAction
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileTypes.FileTypeRegistry
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.progress.util.BackgroundTaskUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.vcs.VcsBundle
import com.intellij.openapi.vcs.VcsConfiguration
import com.intellij.openapi.vcs.changes.ui.*
import com.intellij.openapi.vcs.merge.MergeConflictsTreeTable
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.IdeFocusManager
import com.intellij.testFramework.LightVirtualFile
import com.intellij.ui.DoubleClickListener
import com.intellij.ui.TableSpeedSearch
import com.intellij.ui.UIBundle
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.treeStructure.treetable.ListTreeTableModelOnColumns
import com.intellij.ui.treeStructure.treetable.TreeTable
import com.intellij.ui.treeStructure.treetable.TreeTableModel
import com.intellij.util.EditSourceOnDoubleClickHandler
import com.intellij.util.concurrency.annotations.RequiresEdt
import com.intellij.util.containers.ContainerUtil
import com.intellij.util.containers.Convertor
import com.intellij.util.ui.ColumnInfo
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import com.intellij.util.ui.tree.TreeUtil
import com.think1024.tocodesign.ideaplugin.toco.IgnoredVirtualFile
import com.think1024.tocodesign.ideaplugin.toco.MergeFile
import com.think1024.tocodesign.ideaplugin.toco.MergeStatus
import com.think1024.tocodesign.ideaplugin.toco.getVirtualFile
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import org.jetbrains.annotations.NonNls
import java.awt.BorderLayout
import java.awt.GridLayout
import java.awt.event.ActionEvent
import java.awt.event.MouseEvent
import java.io.IOException
import javax.swing.*
import javax.swing.table.AbstractTableModel
import javax.swing.tree.DefaultMutableTreeNode
import javax.swing.tree.DefaultTreeModel
import javax.swing.tree.TreeNode
import javax.swing.tree.TreePath

enum class Resolution {
    AcceptedYours, AcceptedTheirs
}

fun TreeModelBuilder.insertMergeFilesIntoNode(files: Collection<MergeFile>, subtreeRoot: ChangesBrowserNode<*>) {
    for (file in files) {
        val virtualFile = file.getVirtualFile(myProject)
        insertChangeNode(virtualFile, subtreeRoot, ChangesBrowserNode.createFile(myProject, virtualFile))
    }
}

fun TreeModelBuilder.setMergeFiles(files: Collection<MergeFile>, root: ChangesBrowserNode<*>?): TreeModelBuilder {
    if (ContainerUtil.isEmpty(files)) return this
    insertMergeFilesIntoNode(files, root ?: createTagNode(null))
    return this
}

fun buildFromMergeFiles(
    project: Project,
    grouping: ChangesGroupingPolicyFactory,
    conflictFiles: Collection<MergeFile>,
    ignoredFiles: Collection<MergeFile>,
): Pair<DefaultTreeModel, ChangesBrowserNode<*>?> {
    val builder = TreeModelBuilder(project, grouping)
    if (conflictFiles.isEmpty() && ignoredFiles.isNotEmpty()) {
        return Pair<DefaultTreeModel, ChangesBrowserNode<*>?>(
            builder
            .setMergeFiles(ignoredFiles, null)
            .build(),
            null
        )
    }
    val ignoreRoot = builder.createTagNode(IgnoreFilesTag())
    builder.setMergeFiles(conflictFiles, null)
    if (ignoredFiles.isNotEmpty()) {
        builder.setMergeFiles(ignoredFiles, ignoreRoot)
    }
    return Pair<DefaultTreeModel, ChangesBrowserNode<*>?>(
        builder.build(),
        ignoreRoot
    )
}

class IgnoreFilesTag: ChangesBrowserNode.Tag {
    override fun toString(): String {
        return getI18nString("code.generate.resolve.recover.title")
    }
}

class StatusColumn : ColumnInfo<DefaultMutableTreeNode, String>(VcsBundle.message("vcs.command.name.status")) {
    override fun valueOf(node: DefaultMutableTreeNode): String? {
        val file = node.userObject as? VirtualFile
        if (file == null || file.isDirectory) {
            return ""
        }
        if (file is IgnoredVirtualFile) {
            return getI18nString("code.generate.resolve.recover.title")
        }
        return getI18nString("code.generate.resolve.conflict.title")
    }
}

open class TocoMultipleFileMergeDialog(
    private val project: Project,
    files: List<MergeFile>?,
    private val callback: (abort: Boolean) -> Unit
) : DialogWrapper(project) {
    private var allFiles = files?.toMutableList() ?: mutableListOf()
    private val conflictFiles = allFiles.filter { it.type == MergeStatus.UNMERGED }
    private val ignoredFiles = allFiles.filter { it.type == MergeStatus.IGNORED }
    private var unresolvedFiles = conflictFiles.map { it }.toMutableList()
    private var unrecoveredFiles = ignoredFiles.map { it }.toMutableList()
    private val processedFiles: MutableList<MergeFile> = mutableListOf()
    private lateinit var table: TreeTable
    private var ignoreRootPath: TreePath? = null
    private var acceptYoursButton: JButton? = null
    private var acceptTheirsButton: JButton? = null
    private var mergeButton: JButton? = null
    private var recoverButton: JButton? = null
    private val tableModel = ListTreeTableModelOnColumns(DefaultMutableTreeNode(), createColumns())

    private val descriptionLabel = JLabel(getI18nString("code.generate.resolve.loading"))

    private var groupByDirectory: Boolean
        get() {
            return  VcsConfiguration.getInstance(project).GROUP_MULTIFILE_MERGE_BY_DIRECTORY
        }
        set(value) {
            VcsConfiguration.getInstance(project).GROUP_MULTIFILE_MERGE_BY_DIRECTORY = value
        }

    private val mergeFileRenderer = object : ChangesBrowserNodeRenderer(project, { !groupByDirectory }, false) {
        override fun calcFocusedState() = UIUtil.isAncestor(<EMAIL>,
            IdeFocusManager.getInstance(project).focusOwner)
    }

    init {
        StoreReloadManager.getInstance(project).blockReloadingProjectOnExternalChanges()
        title =
            if (conflictFiles.isEmpty() && unrecoveredFiles.isNotEmpty()) getI18nString("code.generate.resolve.recover")
            else getI18nString("code.generate.resolve.conflict")

        mergeFileRenderer.font = UIUtil.getListFont()

        @Suppress("LeakingThis")
        init()

        updateTree(true)
        table.tree.selectionModel.addTreeSelectionListener { updateButtonState() }
        updateButtonState()
        TreeUtil.promiseSelectFirstLeaf(table.tree)
        object : DoubleClickListener() {
            override fun onDoubleClick(event: MouseEvent): Boolean {
                if (EditSourceOnDoubleClickHandler.isToggleEvent(table.tree, event)) return false

                val closestPath = table.tree.getClosestPathForLocation(event.x, event.y)
                if (closestPath != null) {
                    val selectedNode = closestPath.lastPathComponent
                    val nodeData = (selectedNode as? DefaultMutableTreeNode)?.userObject
                    if (nodeData != null) {
                        showMergeDialog(nodeData !is IgnoredVirtualFile)
                    }
                }
                return true
            }
        }.installOn(table.tree)

        TableSpeedSearch.installOn(table, Convertor { (it as? VirtualFile)?.name })

        val modalityState = ModalityState.stateForComponent(descriptionLabel)
        BackgroundTaskUtil.executeOnPooledThread(disposable) {
            val description =
                if (conflictFiles.isEmpty() && ignoredFiles.isNotEmpty()) getI18nString("code.generate.resolve.recover.description")
                else getI18nString("code.generate.resolve.conflict.description")
            runInEdt(modalityState) {
                descriptionLabel.text = description
            }
        }
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 添加描述标签
        panel.add(descriptionLabel, BorderLayout.NORTH)
        
        // 创建主要内容区域
        val mainPanel = JPanel(BorderLayout())
        
        // 创建树表格
        table = MergeConflictsTreeTable(tableModel).apply {
            setTreeCellRenderer(mergeFileRenderer)
            rowHeight = mergeFileRenderer.preferredSize.height
            preferredScrollableViewportSize = JBUI.size(600, 300)
        }
        mainPanel.add(JBScrollPane(table), BorderLayout.CENTER)
        
        // 创建按钮面板
        val buttonPanel = JPanel(GridLayout(4, 1, 0, 5))

        if (conflictFiles.isNotEmpty()) {
            acceptYoursButton = JButton(VcsBundle.message("multiple.file.merge.accept.yours")).apply {
                addActionListener { acceptRevision(Resolution.AcceptedYours, true) }
            }
            buttonPanel.add(acceptYoursButton)

            acceptTheirsButton = JButton(VcsBundle.message("multiple.file.merge.accept.theirs")).apply {
                addActionListener { acceptRevision(Resolution.AcceptedTheirs, true) }
            }
            buttonPanel.add(acceptTheirsButton)

            val mergeAction = object : AbstractAction() {
                override fun actionPerformed(e: ActionEvent) {
                    showMergeDialog(true)
                }
            }
            mergeAction.putValue(DEFAULT_ACTION, java.lang.Boolean.TRUE)
            mergeButton = createJButtonForAction(mergeAction).apply {
                text = VcsBundle.message("multiple.file.merge.merge")
            }
            buttonPanel.add(mergeButton)
        }

        if (ignoredFiles.isNotEmpty()) {
            val recoverAction = object : AbstractAction() {
                override fun actionPerformed(e: ActionEvent) {
                    acceptRevision(Resolution.AcceptedTheirs, false)
                }
            }
            recoverAction.putValue(DEFAULT_ACTION, java.lang.Boolean.TRUE)
            recoverButton = createJButtonForAction(recoverAction).apply {
                text = getI18nString("code.generate.resolve.recover.button")
            }
            buttonPanel.add(recoverButton)
        }

        val buttonWrapper = JPanel(BorderLayout())
        buttonWrapper.add(buttonPanel, BorderLayout.NORTH)
        mainPanel.add(buttonWrapper, BorderLayout.EAST)
        
        panel.add(mainPanel, BorderLayout.CENTER)
        
        // 添加分组选项
        val groupByDirectoryCheckBox = JCheckBox(VcsBundle.message("multiple.file.merge.group.by.directory.checkbox"), groupByDirectory)
        groupByDirectoryCheckBox.addActionListener { toggleGroupByDirectory(groupByDirectoryCheckBox.isSelected) }
        panel.add(groupByDirectoryCheckBox, BorderLayout.SOUTH)
        
        return panel
    }

    private fun createColumns(): Array<ColumnInfo<*, *>> {
        val columns = ArrayList<ColumnInfo<*, *>>()
        columns.add(object : ColumnInfo<DefaultMutableTreeNode, Any>(VcsBundle.message("multiple.file.merge.column.name")) {
            override fun valueOf(node: DefaultMutableTreeNode) = node.userObject
            override fun getColumnClass(): Class<*> = TreeTableModel::class.java
        })

        columns.add(StatusColumn())

        return columns.toTypedArray()
    }

    private fun toggleGroupByDirectory(state: Boolean) {
        groupByDirectory = state
        var firstSelectedFile: MergeFile? = null
        val selectedFiles = getSelectedFiles()
        for (i in 0 .. selectedFiles.size - 1) {
            val files = selectedFiles[i]
            firstSelectedFile = files.firstOrNull()
            if (firstSelectedFile != null) {
                break
            }
        }
        updateTree()
        if (firstSelectedFile != null) {
            val node = TreeUtil.findNodeWithObject(tableModel.root as DefaultMutableTreeNode, firstSelectedFile)
            node?.let { TreeUtil.selectNode(table.tree, node) }
        }
    }

    private fun updateTree(init: Boolean = false) {
        val factory = when {
            groupByDirectory -> ChangesGroupingSupport.getFactory(ChangesGroupingSupport.DIRECTORY_GROUPING)
            else -> NoneChangesGroupingFactory
        }
        val isIgnoreExpanded = if (ignoreRootPath == null || init) false else table.tree.isExpanded(ignoreRootPath)
        val (model, ignoredRoot) = buildFromMergeFiles(project, factory, unresolvedFiles, unrecoveredFiles)
        tableModel.setRoot(model.root as TreeNode)
        ignoreRootPath = if (ignoredRoot != null) TreePath(model.root).pathByAddingChild(ignoredRoot) else null
        TreeUtil.expandAll(table.tree)
        if (!isIgnoreExpanded && ignoreRootPath != null) {
            TreeUtil.collapsePaths(table.tree, listOf(ignoreRootPath))
        }
        (table.model as? AbstractTableModel)?.fireTableDataChanged()
    }

    private fun updateButtonState() {
        val selectedFiles = getSelectedFiles()
        val conflicts = selectedFiles[0]
        val ignores = selectedFiles[1]
        val haveConflict = conflicts.any()
        val haveIgnores = ignores.any()

        acceptYoursButton?.isEnabled = haveConflict
        acceptTheirsButton?.isEnabled = haveConflict
        mergeButton?.isEnabled = haveConflict
        recoverButton?.isEnabled = haveIgnores
    }

    private fun getSelectedFiles(): List<List<MergeFile>> {
        val virtualFiles = VcsTreeModelData.selected(table.tree).userObjects(VirtualFile::class.java)
        val conflicts = virtualFiles.mapNotNull { virtualFile ->
            conflictFiles.find { mergeFile -> virtualFile.path.contains(mergeFile.mergedPath) }
        }
        val ignores = virtualFiles.mapNotNull { virtualFile ->
            ignoredFiles.find { mergeFile -> virtualFile.path.contains(mergeFile.mergedPath) }
        }
        return listOf(conflicts, ignores)
    }

    override fun createActions(): Array<Action> {
        cancelAction.putValue(Action.NAME, CommonBundle.getCloseButtonText())
        return arrayOf(cancelAction)
    }

    override fun dispose() {
        StoreReloadManager.getInstance(project).unblockReloadingProjectOnExternalChanges()
        super.dispose()
    }

    @NonNls
    override fun getDimensionServiceKey(): String = "TocoMultipleFileMergeDialog"

    @JvmSuppressWildcards
    protected open fun beforeResolve(files: Collection<MergeFile>): Boolean {
        return true
    }

    private fun acceptRevision(resolution: Resolution, isConflict: Boolean) {
        assert(resolution == Resolution.AcceptedYours || resolution == Resolution.AcceptedTheirs)

        FileDocumentManager.getInstance().saveAllDocuments()
        val index = if (isConflict) 0 else 1
        val files = getSelectedFiles()[index]

        ProgressManager.getInstance().run(object : Task.Modal(project,
            "${getI18nString("code.generate.resolve.conflict")}...", false) {
            override fun run(indicator: ProgressIndicator) {
                if (!beforeResolve(files)) {
                    return
                }

                try {
                    for (file in files) {
                        ApplicationManager.getApplication().invokeAndWait({
                            resolveFileViaContent(file, resolution)
                        }, indicator.modalityState)
                        checkMarkModifiedProject(file.getVirtualFile(project))
                        markFileProcessed(file, isConflict)
                    }
                }
                catch (e: Exception) {
                    LOG.warn(e)
                    ApplicationManager.getApplication().invokeAndWait({
                        Messages.showErrorDialog(contentPanel,
                            VcsBundle.message(
                                "multiple.file.merge.dialog.message.error.saving.merged.data",
                                e.message))
                    }, indicator.modalityState)
                }
            }
        })

        updateModelFromFiles()
    }

    @RequiresEdt
    private fun resolveFileViaContent(mergeFile: MergeFile, resolution: Resolution) {
        val file = mergeFile.getVirtualFile(project)
        if (!DiffUtil.makeWritable(project, file)) {
            throw IOException(UIBundle.message("file.is.read.only.message.text", file.presentableUrl))
        }

        val isCurrent = resolution == Resolution.AcceptedYours
        val message = if (isCurrent) VcsBundle.message("multiple.file.merge.dialog.command.name.accept.yours")
        else VcsBundle.message("multiple.file.merge.dialog.command.name.accept.theirs")

        writeCommandAction(project).withName(message).run<Exception> {
            if (isCurrent) {
                file.setBinaryContent(mergeFile.oursContent.toByteArray())
            }
            else {
                file.setBinaryContent(mergeFile.theirsContent.toByteArray())
            }
        }
    }

    private fun markFilesProcessed(files: List<MergeFile>, isConflict: Boolean) {
        val targetFiles = if (isConflict) unresolvedFiles else unrecoveredFiles
        targetFiles.removeAll(files)
        processedFiles.addAll(files)
    }

    private fun markFileProcessed(file: MergeFile, isConflict: Boolean) {
        markFilesProcessed(listOf(file), isConflict)
    }

    override fun doCancelAction() {
        super.doCancelAction()
        callback(true)
    }

    private fun updateModelFromFiles() {
        val targetFiles = if (conflictFiles.isNotEmpty()) unresolvedFiles else unrecoveredFiles
        if (targetFiles.isEmpty()) {
            super.doCancelAction()
            callback(false)
        }
        else {
            var selIndex = table.selectionModel.minSelectionIndex
            updateTree()
            if (selIndex >= table.rowCount) {
                selIndex = table.rowCount - 1
            }
            table.selectionModel.setSelectionInterval(selIndex, selIndex)
            table.requestFocusInWindow()
        }
    }

    private fun handleSingleFile(file: MergeFile, callback: (MergeResult) -> Unit) {
        if (file.type == MergeStatus.IGNORED) {
            val contentFactory: DiffContentFactory = DiffContentFactory.getInstance()
            val oldContent = contentFactory.create(file.oursContent)
            val newContent = contentFactory.create(file.theirsContent)

            val diffRequest = SimpleDiffRequest(
                getI18nString("code.generate.resolve.recover.title"),
                oldContent,
                newContent,
                getI18nString("code.generate.ours"),
                getI18nString("code.generate.theirs")
            )

            // Show the diff in a dialog
            DiffManager.getInstance().showDiff(project, diffRequest, DiffDialogHints.MODAL)
        } else {
            val type = FileTypeRegistry.getInstance().getFileTypeByFileName(file.mergedPath)
            var document: Document? = null
            ApplicationManager.getApplication().runReadAction {
                document = FileDocumentManager.getInstance().getDocument(LightVirtualFile("temp", type, ""))
            }
            if (document == null) {
                return
            }
            val doc = document!!
            val request = DiffRequestFactory.getInstance().createMergeRequest(
                project,
                type,
                doc,
                listOf(
                    file.oursContent,
                    file.baseContent,
                    file.theirsContent
                ),
                "${getI18nString("code.generate.resolve.conflict")}: ${file.mergedPath}",
                listOf(
                    getI18nString("code.generate.ours"),
                    getI18nString("code.generate.base"),
                    getI18nString("code.generate.theirs")
                ),
            ) { mergeResult ->
                if (mergeResult != MergeResult.CANCEL) {
                    file.mergedContent = doc.text
                }
                callback(mergeResult)
            }
            DiffManager.getInstance().showMerge(project, request)
        }
    }

    private fun showMergeDialog(isConflict: Boolean) {
        val index = if (isConflict) 0 else 1
        val files = getSelectedFiles()[index]
        if (files.isEmpty()) return
        if (!beforeResolve(files)) {
            return
        }

        for (file in files) {
            val callback = { result: MergeResult ->
                val virtualFile = file.getVirtualFile(project)
                if (!DiffUtil.makeWritable(project, virtualFile)) {
                    throw IOException(UIBundle.message("file.is.read.only.message.text", virtualFile.presentableUrl))
                }

                val message = when (result) {
                    MergeResult.LEFT -> VcsBundle.message("multiple.file.merge.dialog.command.name.accept.yours")
                    MergeResult.RIGHT -> VcsBundle.message("multiple.file.merge.dialog.command.name.accept.theirs")
                    MergeResult.RESOLVED -> VcsBundle.message("multiple.file.merge.merge")
                    MergeResult.CANCEL -> getI18nString("button.cancel")
                }

                writeCommandAction(project).withName(message).run<Exception> {
                    when (result) {
                        MergeResult.LEFT -> virtualFile.setBinaryContent(file.oursContent.toByteArray())
                        MergeResult.RIGHT -> virtualFile.setBinaryContent(file.theirsContent.toByteArray())
                        MergeResult.RESOLVED -> virtualFile.setBinaryContent(file.mergedContent.toByteArray())
                        MergeResult.CANCEL -> {}
                    }
                }
                checkMarkModifiedProject(virtualFile)

                if (result != MergeResult.CANCEL) {
                    ProgressManager.getInstance()
                        .runProcessWithProgressSynchronously({ markFileProcessed(file, isConflict) },
                            "${getI18nString("code.generate.resolve.conflict")}...", true,
                            project, contentPanel)
                }
            }

            handleSingleFile(file, callback)
        }
        updateModelFromFiles()
    }

    private fun checkMarkModifiedProject(file: VirtualFile) {
        MergeUtil.reportProjectFileChangeIfNeeded(project, file)
    }

    override fun getPreferredFocusedComponent(): JComponent? = table

    companion object {
        private val LOG = Logger.getInstance(TocoMultipleFileMergeDialog::class.java)
    }
}
