package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import org.apache.lucene.analysis.standard.StandardAnalyzer
import org.apache.lucene.document.Document
import org.apache.lucene.document.Field
import org.apache.lucene.document.TextField
import org.apache.lucene.index.IndexWriter
import org.apache.lucene.index.IndexWriterConfig
import org.apache.lucene.search.similarities.BM25Similarity
import org.apache.lucene.queryparser.classic.QueryParser
import org.apache.lucene.store.FSDirectory
import org.json.JSONObject
import java.nio.file.Paths
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.write
import java.util.concurrent.ConcurrentHashMap

@Service(Service.Level.PROJECT)
internal class CodeBaseBm25Index(project: Project) : CodeBaseIndex(project) {
    override val logger = Logger.getInstance(CodeBaseBm25Index::class.java)

    // BM25索引相关
    private var indexDirectory: FSDirectory? = null
    private val analyzer = StandardAnalyzer()
    private val indexWriter = createIndexWriter()
    private val codebaseChunkBuilder = CodebaseChunkBuilder(project)

    // 读写锁，用于保护索引操作
    private val indexLock = ReentrantReadWriteLock()

    // Buffer for file indices
    private val fileIndicesBuffer = ConcurrentHashMap<String, List<Document>>()
    private var isDisposed = false // 是否已经dispose
    private val BATCH_SIZE = 50 // 每次同步的文件数量

    private var isSyncing = false // 是否正在同步

    override fun startSyncing() {
        if (isSyncing) {
            return
        }

        isSyncing = true
        batchSyncIndex()
    }

    override fun updateFileIndex(relativePath: String, updateType: FileUpdateType, hash: String) {
        try {
            // 使用 CodebaseChunkBuilder 来分块
            val contentBlocks = codebaseChunkBuilder.extractContentBlocks(relativePath)

            if (contentBlocks.size <= 2) { // 代表这个文件没有手写方法
                if (updateType == FileUpdateType.UPDATE) {
                    deleteFileIndex(relativePath)
                }
                return
            }

            // 先移除 buffer 里同样 relativePath 的所有内容
            fileIndicesBuffer[relativePath] = emptyList()
            contentBlocks.forEach { block ->
                addToPendingDocs(block.toJSONObject(relativePath))
            }
        } catch (e: Exception) {
            logger.warn("Error updating file index", e)
        }
    }

    override fun deleteFileIndex(relativePath: String) {
        fileIndicesBuffer[relativePath] = emptyList()
    }

    override fun batchSyncIndex() {
        if (isDisposed) {
            logger.info("Skipping batch sync as service is disposed")
            return
        }

        if (fileIndicesBuffer.isEmpty()) {
            isSyncing = false
            logger.info("Index sync successfully completed")
            return
        }

        var batchIndices: Map<String, List<Document>> = emptyMap()

        // 取出BATCH_SIZE个文件进行同步
        batchIndices = fileIndicesBuffer.entries.take(BATCH_SIZE).associate { it.key to it.value }

        fileIndicesBuffer.keys.removeAll(batchIndices.keys) // 从缓冲区中移除已同步的文件
        if (batchIndices.isNotEmpty()) {
            logger.info("Batch syncing ${batchIndices.size} files")
        }

        // 这里同步调用sync index
        sync(batchIndices)

        Thread.sleep(100);
        batchSyncIndex()
    }

    fun sync(indices: Map<String, List<Document>>) {
        if (isDisposed) {
            logger.info("Skipping sync as service is disposed")
            return
        }

        try {
            indexLock.write {
                try {
                    // 1. 先把所有文件都删除一次，documents为空的就是要删除的文件，不为空的新文件也要先把老数据删除
                    indices.entries
                        .forEach { (filePath, _) ->
                            val query =
                                org.apache.lucene.search.TermQuery(org.apache.lucene.index.Term("filePath", filePath))
                            indexWriter.deleteDocuments(query)
                        }

                    // 2. 再添加所有新块
                    val documentsToAdd: List<Document> = indices.values.filter { it.isNotEmpty() }.flatten()
                    if (documentsToAdd.isNotEmpty()) {
                        indexWriter.addDocuments(documentsToAdd)
                    }

                    // Commit all changes
                    indexWriter.commit()

                    if (indices.isNotEmpty()) {
                        logger.info("Successfully synced batch of ${indices.size} files")
                    }
                } catch (e: Exception) {
                    // Rollback all changes if an error occurs
                    indexWriter.rollback()
                    throw e
                }
            }
        } catch (e: Exception) {
            logger.warn("Error syncing file index", e)
        }
    }

    private fun createIndexWriter(): IndexWriter {
        // jdk17编译在jre19-21的环境中不存在MemorySegmentIndexInputProvider类，所以需要disable掉这个优化再创建FSDirectory
        System.setProperty("org.apache.lucene.store.MMapDirectory.enableMemorySegments", "false");
        indexDirectory = FSDirectory.open(Paths.get(project.basePath, ".idea", "toco-index"))
        val config = IndexWriterConfig(analyzer).apply {
            setSimilarity(BM25Similarity())
            ramBufferSizeMB = 16.0 // 设置内存缓冲区大小
            setUseCompoundFile(true) // 对于文件系统索引，启用复合文件
            maxBufferedDocs = 1000 // 设置最大缓冲文档数
            // 设置合并策略
            mergePolicy = org.apache.lucene.index.TieredMergePolicy().apply {
                maxMergeAtOnce = 10 // 控制一次合并的最大段数
                maxMergedSegmentMB = 2048.0 // 控制合并后段的最大大小
                segmentsPerTier = 10.0 // 控制每层的段数
            }
        }
        return IndexWriter(indexDirectory, config)
    }

    override fun clearIndex() {
        try {
            isSyncing = false
            indexLock.write {
                indexWriter.deleteAll()
                indexWriter.commit()
                fileIndicesBuffer.clear()
            }
        } catch (e: Exception) {
            logger.warn("Error clearing index", e)
        }
    }

    override fun dispose() {
        try {
            isDisposed = true
            isSyncing = false

            // Clear the buffer
            fileIndicesBuffer.clear()

            indexWriter.close()
            indexDirectory?.close()
        } catch (e: Exception) {
            logger.warn("Error disposing index", e)
        }
    }

    private fun addToPendingDocs(block: JSONObject) {
        try {
            val doc = Document()
            doc.add(TextField("content", block.getString("raw"), Field.Store.YES))
            doc.add(TextField("filePath", block.getString("filePath"), Field.Store.YES))
            doc.add(TextField("position", block.get("position").toString(), Field.Store.YES))
            fileIndicesBuffer.compute(block.getString("filePath")) { _, docs ->
                val list = docs?.toMutableList() ?: mutableListOf()
                val newPosition = doc.get("position").toString()
                // 先移除已有相同position的doc，再添加新的doc
                list.removeAll { it.get("position").toString() == newPosition }
                list.add(doc)
                list
            }
        } catch (e: Exception) {
            logger.warn("Error adding code block to pending docs", e)
        }
    }

    private fun CodebaseChunkBuilder.ContentBlock.toJSONObject(filePath: String): JSONObject {
        return JSONObject().apply {
            put("raw", raw)
            put("filePath", filePath)
            put("position", JSONObject().apply {
                put("start", position.start)
                put("end", position.end)
            })
        }
    }

    override fun search(query: String): List<SearchResult> {
        var reader: org.apache.lucene.index.DirectoryReader? = null
        try {
            reader = org.apache.lucene.index.DirectoryReader.open(indexDirectory)
            indexLock.readLock().lock()
            val searcher = org.apache.lucene.search.IndexSearcher(reader)
            searcher.similarity = BM25Similarity()

            // Create a query that searches in the content field
            val queryParser = QueryParser("content", analyzer)
            val parsedQuery = queryParser.parse(query)

            // Search with a limit of 10 results
            val topDocs = searcher.search(parsedQuery, 15)

            // Group results by file path
            val results = mutableListOf<SearchResult>()

            topDocs.scoreDocs.forEach { scoreDoc ->
                val doc = searcher.storedFields().document(scoreDoc.doc)
                val filePath = doc.get("filePath")
                val positionStr = doc.get("position")
                val positionJson = JSONObject(positionStr)
                val start = positionJson.getInt("start")
                val end = positionJson.getInt("end")
                val content = doc.get("content")

                results.add(
                    SearchResult(
                        filePath = filePath,
                        score = scoreDoc.score,
                        start = start,
                        end = end,
                        raw = content
                    )
                )
            }

            return results
        } catch (e: Exception) {
            logger.warn("Error performing search with query: $query", e)
            return emptyList()
        } finally {
            indexLock.readLock().unlock()
            try {
                reader?.close()
            } catch (e: Exception) {
                logger.warn("Error closing index reader", e)
            }
        }
    }
} 