package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.IdeaConfig
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Service(Service.Level.PROJECT)
class ConfigService(private val project: Project) {

    data class ConfigStructure(
        val theme: String,
        val projectId: String, // 高亮后的文件名
    )

    suspend fun getConfig(project: Project): ConfigStructure = suspendCancellableCoroutine { cont ->
        DumbService.getInstance(project).runWhenSmart {
            try {
                val theme = IdeaConfig.getCurrentTheme() as String
                val projectId = ProjectPluginSettings.getInstance(project).projectId as String
                val configStructure = ConfigStructure(
                    theme = theme,
                    projectId = projectId
                )

                cont.resume(configStructure) // 返回数据类实例
            } catch (e: Exception) {
                cont.resumeWithException(e)
            }
        }
    }
}
