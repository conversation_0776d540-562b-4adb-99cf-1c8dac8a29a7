package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.openapi.vfs.newvfs.events.VFileMoveEvent
import com.intellij.openapi.vfs.newvfs.events.VFilePropertyChangeEvent
import com.intellij.psi.PsiFile
import java.nio.file.Paths
import java.security.MessageDigest

/**
 * 代码库工具类
 */
class CodeBaseUtils {
    companion object {
        /**
         * 计算字符串内容的SHA-256哈希值
         *
         * @param content 需要计算哈希的字符串内容
         * @return 内容的SHA-256哈希值（16进制字符串）
         */
        fun calculateContentHash(content: String): String {
            val bytes = content.toByteArray()
            val md = MessageDigest.getInstance("SHA-256")
            val digest = md.digest(bytes)
            return digest.fold("") { str, byte -> str + "%02x".format(byte) }
        }

        /**
         * 计算文件相对于工程基础路径的相对路径
         *
         * @param basePath 工程基础路径
         * @param filePath 文件绝对路径
         * @return 文件相对于工程基础路径的相对路径
         */
        fun calculateRelativePath(basePath: String, filePath: String): String {
            return try {
                Paths.get(basePath).relativize(Paths.get(filePath)).toString()
            } catch (e: Exception) {
                // 如果无法计算相对路径，返回原始路径
                filePath
            }
        }

        fun getFileContent(startLine: Int, endLine: Int, file: VirtualFile?): String {
            if (file == null) {
                return ""
            }

            val rawContent = String(file.contentsToByteArray())
            val lines = rawContent.lines()
            return lines.subList(startLine, endLine).joinToString("\n")
        }
    }
}