package com.think1024.tocodesign.ideaplugin.services

import com.intellij.notification.NotificationAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project
import com.intellij.util.concurrency.AppExecutorUtil
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.CombinedPluginSettingsConfigurable
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.CookieManager
import com.think1024.tocodesign.ideaplugin.utils.HttpUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * Service class responsible for managing user-related operations such as updating user info, logging out,
 * and checking login status. This service interacts with the server API to perform these operations
 * and updates the application settings accordingly.
 */
@Service
class UserService {
    data class Organization(
        val id: Long,
        val name: String,
        val regionId: Long
    )

    private val logger = Logger.getInstance(UserService::class.java)
    private val settings = ApplicationPluginSettings.getInstance()
    private val notificationService = NotificationService.instance
    private var loginCheckTask: ScheduledFuture<*>? = null

    /**
     * Fetches user information from the server.
     * @return A Pair containing a Boolean (success status) and a JSONObject (user info) or null.
     */
    private fun fetchUserInfo(): Pair<Boolean, JSONObject?> {
        try {
            val (response) = TocoService.get("url.path.userinfo")
            if (response != null && response is JSONObject) {
                return Pair(true, response)
            }
        } catch (e: Exception) {
            logger.warn("Failed to fetch user info", e)
        }
        return Pair(false, null)
    }

    private fun fetchUserOrgs(): Pair<Boolean, List<Organization?>?> {
        try {
            val (response) = TocoService.post("url.path.orgs", "{ qto: {} }")
            if (response != null && response is JSONArray) {
                val orgsArray = response as JSONArray
                val orgsList = mutableListOf<Organization>()
                for (i in 0 until orgsArray.length()) {
                    val orgJson = orgsArray.getJSONObject(i)
                    val orgData = orgJson.getJSONObject("organization")
                    orgsList.add(Organization(
                        id = orgData.getLong("id"),
                        name = orgData.getString("name"),
                        regionId = orgData.getLong("regionId")
                    ))
                }
                return Pair(true, orgsList)
            }
        } catch (e: Exception) {
            logger.warn("Failed to fetch orgs", e)
        }
        return Pair(false, null)
    }

    /**
     * Updates the user information by fetching it from the server.
     * If successful, it updates the user's login status, username, and nickname in the application settings.
     * If an error occurs, it displays an error notification.
     */
    fun updateUserInfo() {
        val (success, userInfo) = fetchUserInfo()
        if (success && userInfo != null && userInfo.has("id")) {
            val userId = userInfo.getString("id")

            val username = if (userInfo.has("username")) {
                userInfo.getString("username")
            } else {
                ""
            }

            val nickname = if (userInfo.has("nickname")) {
                userInfo.getString("nickname")
            } else {
                ""
            }

            val preferredUsername = if (userInfo.has("preferredUsername")) {
                userInfo.getString("preferredUsername")
            } else {
                ""
            }

            val isPm = if (userInfo.has("isPm")) {
                userInfo.getString("isPm")
            } else {
                ""
            }

            val avatar = if (userInfo.has("avatar")) {
                userInfo.getString("avatar")
            } else {
                ""
            }

            // Update application settings with user information
            settings.loggedIn = true
            settings.userId = userId
            settings.username = username
            settings.nickname = nickname
            settings.preferredUsername = preferredUsername
            settings.isPm = isPm
            settings.avatar = avatar
        } else {
            // Display error notification if the update fails
            notificationService.notifyError(
                getI18nString("account.login"),
                getI18nString("notification.login.failed"),
            )
        }
    }

    fun updateOrgs(): List<Organization?>? {
        val (_, orgs) = fetchUserOrgs()
        return orgs
    }

    /**
     * Logs out the current user by making a POST request to the server.
     */
    fun logout() {
        val logger = Logger.getInstance("UserLogout")

        try {
            val host = settings.host
            val logoutPath = ConfigUtil.getProperty("url.path.logout")
            val cookies = settings.cookies

            // Send logout request to the server
            val response = HttpUtil.post(host + logoutPath, HttpUtil.parseCookies(cookies), "")
            val jsonResponse = JSONObject(response)

            // Check if logout was successful
            if (jsonResponse.getInt("code") == 200 && jsonResponse.getBoolean("data")) {
                logger.info("User successfully logged out")
            } else {
                logger.warn("Logout request was unsuccessful. Server response: $response")
            }
        } catch (e: Exception) {
            logger.warn("Failed to logout", e)
        } finally {
            // Clear user data
            clearUserData()
        }
    }

    /**
     * Clears all user-related data from the application settings and cookies.
     */
    fun getUserData(): ApplicationPluginSettings {
        return settings.getState()
    }

    /**
     * Clears all user-related data from the application settings and cookies.
     */
    fun clearUserData() {
        // Clear specific cookie (U_TOKEN) for the host
        CookieManager.clearCookiesForHost(settings.host, CookieManager.U_TOKEN)
        CookieManager.clearCookiesForHost(settings.frontendHost, CookieManager.U_TOKEN)

        // Clear user information from application settings
        clearApplicationUserData()
    }

    /**
     * Clears all user-related data from the application settings.
     */
    private fun clearApplicationUserData() {
        settings.loggedIn = false
        settings.username = ""
        settings.userId = ""
        settings.nickname = ""
        settings.cookies = ""

        // Immediately trigger persistence to ensure data is saved to XML on the EDT
        ApplicationManager.getApplication().invokeLater {
            settings.getState() // Call this to trigger the saving of current state
        }
    }

    /**
     * Checks the current login status by fetching user info from the server.
     * @return true if the user is logged in and the server responds successfully, false otherwise.
     */
    fun checkLoginStatus(): Boolean {
        if (!settings.loggedIn) {
            return false
        }
        val (success, _) = fetchUserInfo()
        return success
    }

    /**
     * Starts a periodic task to check the user's login status.
     * If the user is not logged in, it triggers a notification.
     * @param project The current project instance.
     */
    fun startLoginCheckTask(project: Project) {
        stopLoginCheckTask() // Ensure only one task is running

        // Perform an immediate login check
        if (!checkLoginStatus()) {
//            notifyLoginExpired(project) // 这个提示先关了 好像不需要了
        }

        // Set up periodic check task
        loginCheckTask = AppExecutorUtil.getAppScheduledExecutorService().scheduleWithFixedDelay({
            if (!checkLoginStatus()) {
//                notifyLoginExpired(project) // 这个提示先关了 好像不需要了
            }
        }, 30, 30, TimeUnit.MINUTES) // Initial delay of 30 minutes, then every 30 minutes
    }

    /**
     * Stops the periodic login check task if it's running.
     */
    fun stopLoginCheckTask() {
        loginCheckTask?.cancel(false)
        loginCheckTask = null
    }

    /**
     * Notifies the user that their login has expired and provides an action to open application settings.
     * This method creates a warning notification with an action to open the settings dialog.
     *
     * @param project The current project instance.
     */
    private fun notifyLoginExpired(project: Project) {
        // Retrieve localized strings for the notification title and content
        val title = getI18nString("notification.login.expired.title")
        val content = getI18nString("notification.login.expired")

        // Create an action to open the settings dialog
        val openSettingsAction = NotificationAction.create(getI18nString("action.open.settings")) { _, notification ->
            // Use invokeLater to ensure UI operations are performed on the EDT
            ApplicationManager.getApplication().invokeLater {
                try {
                    // Open the settings dialog and navigate to the plugin's settings
                    ShowSettingsUtil.getInstance().showSettingsDialog(
                        project,
                        CombinedPluginSettingsConfigurable::class.java
                    ) { configurable ->
                        // Select the application tab in the settings dialog
                        configurable?.selectApplicationTab()
                    }
                } catch (e: Exception) {
                    logger.warn("Failed to open settings dialog", e)
                }
            }
            // Expire the notification after the action is performed
            notification.expire()
        }

        // Use invokeLater to ensure the notification is shown on the EDT
        ApplicationManager.getApplication().invokeLater {
            // Display the warning notification with the open settings action
            notificationService.notifyWarning(
                title,
                content,
                null,
                NotificationService.NotificationGroup.TOCO,
                0,  // Use default expiration time
                openSettingsAction
            )
        }
    }


    companion object {
        /**
         * Gets the singleton instance of UserService.
         * @return The UserService instance.
         */
        fun getInstance(): UserService = ApplicationManager.getApplication().getService(UserService::class.java)
    }
}
