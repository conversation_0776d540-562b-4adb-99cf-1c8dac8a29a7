package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.icons.AllIcons
import com.intellij.openapi.util.IconLoader
import com.intellij.util.ui.ImageUtil
import java.awt.Image
import javax.swing.Icon
import javax.swing.ImageIcon

/**
 * Object containing icon definitions for the locator functionality.
 * Provides both default and custom icons for locator feature.
 */
object LocatorIcons {
    /**
     * The scale factor for the icon.
     * 0.75f means the icon will be scaled to 75% of its original size.
     */
    private const val ICON_SCALE = 0.75f

    /**
     * The default locator icon.
     * This icon is loaded from the IDE's built-in icon set and scaled proportionally.
     */
    @JvmField
    val DEFAULT_LOCATOR_ICON: Icon = scaleIcon(AllIcons.General.Locate, ICON_SCALE)

    /**
     * A custom locator icon.
     * This icon is loaded from the plugin's resources.
     */
    @JvmField
    val CUSTOM_LOCATOR_ICON: Icon = IconLoader.getIcon("/icons/toco.svg", LocatorIcons::class.java)

    /**
     * The currently active locator icon.
     * By default, it uses the IDE's built-in locate icon.
     * This can be changed to use the custom icon if needed.
     */
    @JvmField
    var ACTIVE_LOCATOR_ICON: Icon = DEFAULT_LOCATOR_ICON

    /**
     * Sets the active locator icon to the custom icon.
     */
    fun useCustomIcon() {
        ACTIVE_LOCATOR_ICON = CUSTOM_LOCATOR_ICON
    }

    /**
     * Resets the active locator icon to the default icon.
     */
    fun useDefaultIcon() {
        ACTIVE_LOCATOR_ICON = DEFAULT_LOCATOR_ICON
    }

    /**
     * Scales the given icon by the specified scale factor.
     *
     * @param icon The icon to scale.
     * @param scale The scale factor to apply.
     * @return A scaled version of the input icon.
     */
    private fun scaleIcon(icon: Icon, scale: Float): Icon {
        val image = requireNotNull(IconLoader.toImage(icon)) { "Failed to convert icon to image" }
        val bufferedImage = ImageUtil.toBufferedImage(image)
        val scaledWidth = (bufferedImage.width * scale).toInt()
        val scaledHeight = (bufferedImage.height * scale).toInt()
        val scaledImage = bufferedImage.getScaledInstance(scaledWidth, scaledHeight, Image.SCALE_SMOOTH)
        return ImageIcon(scaledImage)
    }
}