package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.codeInsight.daemon.LineMarkerInfo
import com.intellij.codeInsight.daemon.LineMarkerProvider
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.project.DumbAware
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiMethod
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString

/**
 * A line marker provider that adds or removes locator icons to the gutter for Java classes and methods.
 *
 * This provider examines Java files and checks each class and method within them
 * to determine if they have a corresponding locator request. If a request exists,
 * it adds a locator icon to the gutter next to the class or method name.
 * If a request doesn't exist for a previously marked element, it removes the icon.
 *
 * This implementation includes optimizations to handle multiple invocations,
 * improve performance in IDE environments, and ensure correct icon placement.
 */
class LocatorLineMarkerProvider : LineMarkerProvider, DumbAware {
    private val logger = Logger.getInstance(LocatorLineMarkerProvider::class.java)

    /**
     * Provides line marker info for the given element.
     *
     * This method is called for each element in a file. It processes only
     * PsiClass and PsiMethod elements, checking if they have a corresponding
     * locator request.
     *
     * @param element The PSI element to check for locator requests.
     * @return LineMarkerInfo if a locator icon should be added, null otherwise.
     */
    override fun getLineMarkerInfo(element: PsiElement): LineMarkerInfo<*>? {
        if (ProjectPluginSettings.getInstance(element.project).projectId == null) {
            return null
        }
        // Check if the element is valid before processing
        if (!element.isValid) {
            // Log a warning if the element is invalid
            logger.warn("The provided PsiElement is not valid: ${element::class.java.simpleName}")
            return null // Return null if validation fails
        }

        // Process only classes and methods for line markers
        return when (element) {
            is PsiClass, is PsiMethod -> processElement(element) // Process for locator icons
            else -> null // Return null for other types of elements
        }
    }

    /**
     * Processes a single PSI element (either a class or a method) to determine if a locator request exists.
     *
     * @param element The PSI element to process, expected to be either a PsiClass or PsiMethod.
     * @return LineMarkerInfo if locator information exists and an icon should be added; null otherwise.
     */
    private fun processElement(element: PsiElement): LineMarkerInfo<PsiElement>? {
        // Determine the anchor for the line marker based on element type
        val anchor = when (element) {
            is PsiClass -> element.nameIdentifier
            is PsiMethod -> element.nameIdentifier
            else -> null
        } ?: return null // Return null if the element is neither a class nor a method

        // Build locator information for the element
        val locatorBuilder = LocatorBuilder(element.project)
        val locatorInfo = try {
            locatorBuilder.buildFromPsiElement(element)
        } catch (e: Exception) {
            logger.warn("Could not build locator", e)
        }

        null
        // Return line marker info if locator info is valid
        return if (locatorInfo != null) {
            createLocatorLineMarkerInfo(element, anchor)
        } else {
            null
        }
    }

    /**
     * Creates a LineMarkerInfo object for the specified PSI element.
     *
     * This method defines the icon, its behavior, and its appearance in the gutter.
     * The icon is placed next to the element's name identifier for better visibility.
     *
     * @param element The PSI element to which the locator icon should be added.
     * @param anchor The name identifier of the element, used for correct icon placement.
     * @return LineMarkerInfo object containing all necessary information for the line marker.
     */
    private fun createLocatorLineMarkerInfo(element: PsiElement, anchor: PsiElement): LineMarkerInfo<PsiElement> {
        return LineMarkerInfo(
            anchor,
            anchor.textRange,
            LocatorIcons.CUSTOM_LOCATOR_ICON,
            { getI18nString("open.tocodesign.tab") },
            { _, elt -> LocatorService.getInstance(element.project).openTocoWebViewEditor(element) },
            GutterIconRenderer.Alignment.CENTER,
            { getI18nString("open.tocodesign.tab") }
        )
    }
}
