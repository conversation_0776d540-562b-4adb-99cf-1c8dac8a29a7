package com.think1024.tocodesign.ideaplugin.services.locator

/**
 * Extension function for LocatorBuilder.LocatorInfo to convert it to a Map.
 * This function is useful for serialization or data transfer purposes.
 *
 * @receiver LocatorBuilder.LocatorInfo The object to be converted
 * @return Map<String, Any> A map representation of the LocatorInfo object
 */
fun LocatorBuilder.LocatorInfo.toMap(): Map<String, Any> {
    // Create a map with all properties of LocatorInfo
    return mapOf(
        "id" to id,           // Unique identifier for the locator
        "type" to type,       // Type of the locator (e.g., "bto", "dto", "api")
        "projectId" to projectId,   // Identifier of the project containing this locator
        "moduleId" to moduleId,     // Identifier of the module containing this locator
        "moduleName" to moduleName, // Name of the module containing this locator
        "name" to name        // Name of the entity this locator points to
    ).filterValues { it != "" }  // Remove any entries with empty string values
    // This filtering step ensures that only non-empty values are included in the final map,
    // which can be useful for reducing data size or avoiding null/empty checks later on.
}
