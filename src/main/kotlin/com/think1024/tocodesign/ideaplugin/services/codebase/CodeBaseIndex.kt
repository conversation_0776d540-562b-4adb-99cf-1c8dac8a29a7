package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile

/**
 * 代码索引基类，提供索引基础操作接口
 */
abstract class CodeBaseIndex(protected val project: Project) {
    /**
     * 搜索结果数据类
     */
    data class SearchResult(
        val filePath: String,
        val score: Float,
        val start: Int,
        val end: Int,
        val raw: String
    )

    protected open val logger = Logger.getInstance(this.javaClass)

    /**
     * 开始扫描文件
     */
    abstract fun startSyncing()

    /**
     * 更新文件索引(缓存在本地，批量sync)
     */
    abstract fun updateFileIndex(relativePath: String, updateType: FileUpdateType, hash: String)

    /**
     * 删除文件索引(缓存在本地，批量sync)
     */
    abstract fun deleteFileIndex(filePath: String)

    /**
     * 批量同步索引
     */
    abstract fun batchSyncIndex()

    /**
     * 清除所有索引
     */
    abstract fun clearIndex()

    /**
     * 释放资源
     */
    abstract fun dispose()

    /**
     * 执行搜索
     */
    abstract fun search(query: String): List<SearchResult>

    /**
     * 文件更新类型
     */
    enum class FileUpdateType(val value: String) {
        ADD("add"), UPDATE("update"), DELETE("delete")
    }
}