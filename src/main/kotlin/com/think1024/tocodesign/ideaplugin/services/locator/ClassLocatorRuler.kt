package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiJavaFile
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction

/**
 * Implementation of ILocatorRuler for handling class-level AutoGenerated annotations.
 * This class provides functionality to locate and navigate to specific classes in Java files
 * based on AutoGenerated annotations.
 *
 * @property project The IntelliJ IDEA project context.
 */
class ClassLocatorRuler(override val project: Project) : ILocatorRuler {
    /**
     * Logger instance for this class.
     */
    override val logger = Logger.getInstance(ClassLocatorRuler::class.java)

    /**
     * Mapping of type to corresponding file path.
     * This mapping is used to determine the directory structure for different types of files.
     * The key represents the type, and the value is a string containing submodule path and file path separated by '|'.
     *
     * Each entry in this mapping serves as a reference for the project structure,
     * enabling the ClassLocatorRuler to efficiently locate files based on their type.
     * The types here include various design patterns such as entity, business object (bo),
     * data transfer object (dto), value object (vo), and others relevant to the application's architecture.
     */
    override val typeToPathMapping = mapOf(
        "entity" to "persist|persist.dos",
        "bo" to "manager|manager.bo",
        "bto" to "service|service.bto",
        "dto" to "manager|manager.dto",
        "vo" to "entrance.web|entrance.web.vo",
        "qto" to "persist|persist.qto",
        "eo" to "persist|persist.eo",
        "enum" to "common|common.enums",
        "mo" to "service|service.mq.mo",
        "dmo" to "manager|manager.mo",
        "flow_node" to "service|service.flow.node", // flow: node
        "flow" to "service|service.flow.context", // flow: context
        "flow_list" to "service|service", // flow list: service
        "flow_list" to "service|service.flow" // flow list: config
    )

    /**
     * Matches the PsiFile against the provided locator information.
     * This method performs the following checks:
     * 1. Verifies if the file is a Java file and contains at least one class.
     * 2. Checks if the first class has the required AutoGenerated annotation.
     * 3. Extracts UUID information from the annotation.
     * 4. Compares the extracted information with the provided locator info.
     *
     * @param psiFile The PSI file to check.
     * @param locatorInfo The LocatorInfo to match against.
     * @return LocatorResult if the PsiFile contains a matching class, null otherwise.
     */
    override fun matches(psiFile: PsiFile, locatorInfo: LocatorBuilder.LocatorInfo): ILocatorRuler.LocatorResult? {
        // Ensure the file is a Java file and get the first class in the file
        val psiClass = (psiFile as? PsiJavaFile)?.classes?.firstOrNull() ?: return null

        // Perform annotation retrieval within a read action
        val annotation = runReadAction {
            psiClass.annotations.find { it.qualifiedName == locatorClassAnnotation || it.qualifiedName == defaultAnnotation }
        } ?: return null

        // Extract the UUID information from the annotation
        val locatorIndex = extractUuidInfo(annotation)

        // Perform the actual matching
        if (locatorIndex.searchKey.equals(locatorInfo.searchKey, ignoreCase = true)) {
            // Return a LocatorResult with the file, class, and no specific method
            return ILocatorRuler.LocatorResult(psiFile.virtualFile, psiClass, null)
        }

        return null
    }

    /**
     * Opens the target file and navigates to the target class.
     * This method opens the file in the editor, locates the class with the AutoGenerated annotation,
     * and positions the cursor at the beginning of that class's name, scrolling it to the center of the window.
     *
     * @param locatorResult The LocatorResult containing the target file and associated PSI elements.
     * @return True if the file was successfully opened and the navigation was performed, false otherwise.
     */
    override fun openAndNavigateToTarget(locatorResult: ILocatorRuler.LocatorResult): Boolean {
        val editor = openFileInEditor(locatorResult.virtualFile) ?: return false

        locatorResult.psiClass?.let { psiClass ->
            val position = calculateClassPosition(psiClass, editor)
            navigateToPosition(editor, position)
            return true
        }

        return false
    }

    /**
     * Opens the specified file in the editor.
     *
     * @param virtualFile The virtual file to open.
     * @return The Editor instance if successful, null otherwise.
     */
    private fun openFileInEditor(virtualFile: VirtualFile): Editor? {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val editors = fileEditorManager.openFile(virtualFile, true)
        return (editors.find { it is TextEditor } as? TextEditor)?.editor
    }

    /**
     * Calculates the logical position of the class name.
     *
     * @param psiClass The PsiClass for which to calculate the position.
     * @param editor The Editor instance.
     * @return The LogicalPosition of the class name.
     */
    private fun calculateClassPosition(psiClass: PsiClass, editor: Editor): LogicalPosition {
        val classNameOffset = getClassNameOffset(psiClass)
        val document = editor.document
        val classLineNumber = document.getLineNumber(classNameOffset)
        val classColumnNumber = classNameOffset - document.getLineStartOffset(classLineNumber)
        return LogicalPosition(classLineNumber, classColumnNumber)
    }

    /**
     * Navigates to the specified position in the editor.
     *
     * @param editor The Editor instance.
     * @param position The LogicalPosition to navigate to.
     */
    private fun navigateToPosition(editor: Editor, position: LogicalPosition) {
        editor.caretModel.moveToLogicalPosition(position)
        editor.scrollingModel.scrollTo(position, ScrollType.CENTER)
    }

    /**
     * Calculates the offset of the class name.
     *
     * @param psiClass The PsiClass for which to calculate the name offset.
     * @return The calculated offset for the class name.
     */
    private fun getClassNameOffset(psiClass: PsiClass): Int {
        val className = psiClass.nameIdentifier
        return className?.textRange?.startOffset ?: psiClass.textOffset
    }
}
