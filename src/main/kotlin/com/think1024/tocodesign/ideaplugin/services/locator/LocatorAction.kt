package com.think1024.tocodesign.ideaplugin.services.locator

import java.util.*

/**
 * Enumeration of possible actions for the locator.
 * This enum provides a centralized place to define and manage
 * all valid actions that can be performed by the locator.
 */
enum class LocatorAction {
    /**
     * Represents the action to open a resource.
     * This is typically used when the locator needs to
     * open a file, document, or other type of resource.
     */
    OPEN;

    /**
     * Returns the lowercase string representation of the action.
     *
     * @return The name of the action in lowercase.
     */
    override fun toString(): String {
        return name.lowercase(Locale.getDefault())
    }

    companion object {
        /**
         * Attempts to parse a string into a LocatorAction.
         *
         * @param value The string to parse.
         * @return The corresponding LocatorAction if the string matches (case-insensitive),
         *         or null if no match is found.
         */
        fun fromString(value: String): LocatorAction? {
            return values().find { it.name.equals(value, ignoreCase = true) }
        }
    }
}