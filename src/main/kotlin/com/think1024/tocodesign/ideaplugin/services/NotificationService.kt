package com.think1024.tocodesign.ideaplugin.services

import com.intellij.notification.*
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import java.util.*

/**
 * Service for managing and displaying notifications in the IntelliJ IDEA environment.
 * This service provides methods to show information, warning, and error notifications
 * with customizable content, actions, and auto-expiration.
 */
@Service
class NotificationService {
    // Logger for this class
    private val logger = Logger.getInstance(NotificationService::class.java)

    /**
     * Enum class representing predefined notification groups.
     * This allows for type-safe and controlled use of group IDs.
     */
    enum class NotificationGroup(val id: String) {
        TOCO("Toco"),
    }

    fun getNotificationType(antMessageType: String): NotificationType {
        return when(antMessageType) {
            "error" -> NotificationType.ERROR
            "warning" -> NotificationType.WARNING
            else -> NotificationType.INFORMATION
        }
    }

    /**
     * Displays a notification with the given content and type.
     *
     * @param title The title of the notification.
     * @param content The content of the notification.
     * @param project The current project to associate the notification with, can be null.
     *                When null, the notification is not associated with any specific project.
     * @param type The type of the notification (INFORMATION, WARNING, ERROR).
     * @param group The notification group (use predefined NotificationGroup enum).
     * @param autoExpireSeconds Number of seconds after which the notification should auto-expire (default is 5, < 0 for no expiration).
     * @param actions Optional list of actions to add to the notification.
     * @return The created Notification object, or null if notifications are disabled or already displayed.
     */
    fun notify(
        title: String,
        content: String,
        project: Project? = null,
        type: NotificationType,
        group: NotificationGroup = NotificationGroup.TOCO,
        autoExpireSeconds: Int = 5,
        vararg actions: NotificationAction
    ): Notification? {
        // Check if bubble messages are enabled in application settings
        if (!ApplicationPluginSettings.getInstance().showBubbleMessages) {
            return null
        }

        // Check if bubble messages are enabled for the specific project, if project is not null
        project?.let {
            val projectSettings = ProjectPluginSettings.getInstance(it)
            projectSettings.projectId?.let { projectId ->
                if (projectSettings.bubbleMessagesMap[projectId] == false) {
                    return null
                }
            } ?: logger.warn("projectId is null, unable to retrieve message setting.")
        }

        // Create the notification
        val notification = createNotification(group.id, title, content, type)
        actions.forEach { notification.addAction(it) }

        // Notify the notification
        Notifications.Bus.notify(notification)

        if (autoExpireSeconds > 0) {
            // Schedule the notification to expire after the specified time
            Timer().schedule(object : TimerTask() {
                override fun run() {
                    // Ensure the expiration is executed on the Event Dispatch Thread
                    ApplicationManager.getApplication().invokeLater {
                        notification.expire()
                    }
                }
            }, autoExpireSeconds * 1000L)
        }

        return notification
    }

    /**
     * Creates a notification using the specified parameters.
     *
     * @param groupId The ID of the notification group.
     * @param title The title of the notification.
     * @param content The content or message of the notification.
     * @param type The type of the notification, determining its importance and visibility.
     * @return The created Notification object.
     */
    private fun createNotification(
        groupId: String,
        title: String,
        content: String,
        type: NotificationType
    ): Notification {
        return if (isNotificationGroupManagerSupported(groupId)) {
            // If the NotificationGroupManager supports this groupId, use it to create the notification
            NotificationGroupManager.getInstance()
                .getNotificationGroup(groupId)
                .createNotification(title, content, type)
        } else {
            // Otherwise, create a Notification directly
            Notification(groupId, title, content, type)
        }
    }

    /**
     * Checks if the NotificationGroupManager supports the specified groupId.
     *
     * @param groupId The ID of the notification group to check.
     * @return True if the NotificationGroupManager can handle the specified groupId, false otherwise.
     */
    private fun isNotificationGroupManagerSupported(groupId: String): Boolean {
        return try {
            val notificationGroup = NotificationGroupManager.getInstance().getNotificationGroup(groupId)

            // Null check for notificationGroup
            if (notificationGroup == null) {
                logger.warn("Notification group '$groupId' is not registered.")
                return false
            }

            // Try to access the createNotification method to verify support
            notificationGroup::class.java.getMethod(
                "createNotification",
                String::class.java, String::class.java, NotificationType::class.java
            )
            true
        } catch (_: NoSuchMethodException) {
            // If the method does not exist, the groupId is not supported
            false
        } catch (_: NoClassDefFoundError) {
            // If the class definition is not found, the groupId is not supported
            false
        } catch (e: IllegalArgumentException) {
            // Handle any illegal arguments provided to the method
            logger.warn("IllegalArgumentException for groupId '$groupId': ${e.message}")
            false
        } catch (e: Exception) {
            // Catch all other exceptions
            logger.warn("Unexpected exception for groupId '$groupId': ${e.message}", e)
            false
        }
    }

    /**
     * Displays an information notification.
     *
     * @param title The title of the notification
     * @param content The content of the notification
     * @param project The current project to associate the notification with, can be null.
     *                When null, the notification is not associated with any specific project.
     * @param group The notification group (use predefined NotificationGroup enum)
     * @param autoExpireSeconds Number of seconds after which the notification should auto-expire (default is 5)
     * @param actions Optional list of actions to add to the notification
     * @return The created Notification object
     */
    fun notifyInfo(
        title: String,
        content: String,
        project: Project? = null,
        group: NotificationGroup = NotificationGroup.TOCO,
        autoExpireSeconds: Int = 5,
        vararg actions: NotificationAction
    ) = notify(title, content, project, NotificationType.INFORMATION, group, autoExpireSeconds, *actions)

    /**
     * Displays a warning notification.
     *
     * @param title The title of the notification
     * @param content The content of the notification
     * @param project The current project to associate the notification with, can be null.
     *                When null, the notification is not associated with any specific project.
     * @param group The notification group (use predefined NotificationGroup enum)
     * @param autoExpireSeconds Number of seconds after which the notification should auto-expire (default is 10)
     * @param actions Optional list of actions to add to the notification
     * @return The created Notification object
     */
    fun notifyWarning(
        title: String,
        content: String,
        project: Project? = null,
        group: NotificationGroup = NotificationGroup.TOCO,
        autoExpireSeconds: Int = 10,
        vararg actions: NotificationAction
    ) = notify(title, content, project, NotificationType.WARNING, group, autoExpireSeconds, *actions)

    /**
     * Displays an error notification.
     *
     * @param title The title of the notification
     * @param content The content of the notification
     * @param project The current project to associate the notification with, can be null.
     *                When null, the notification is not associated with any specific project.
     * @param group The notification group (use predefined NotificationGroup enum)
     * @param autoExpireSeconds Number of seconds after which the notification should auto-expire (default is 20)
     * @param actions Optional list of actions to add to the notification
     * @return The created Notification object
     */
    fun notifyError(
        title: String,
        content: String,
        project: Project? = null,
        group: NotificationGroup = NotificationGroup.TOCO,
        autoExpireSeconds: Int = 20,
        vararg actions: NotificationAction
    ) = notify(title, content, project, NotificationType.ERROR, group, autoExpireSeconds, *actions)

    companion object {
        /**
         * Singleton instance of the NotificationService.
         * This allows easy access to the service throughout the plugin.
         */
        val instance: NotificationService by lazy { NotificationService() }
    }
}
