package com.think1024.tocodesign.ideaplugin.services.locator

import com.google.gson.JsonObject
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiMethod
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.getStringOrNull

/**
 * LocatorBuilder class for constructing locator request and response objects.
 * This class provides methods to create standardized objects for locator communication
 * between the IntelliJ IDEA plugin and external services or components.
 *
 * @property project The IntelliJ IDEA project in which the locator is operating.
 */
class LocatorBuilder(private val project: Project) {
    companion object {
        private val logger = Logger.getInstance(LocatorBuilder::class.java)
    }

    /**
     * An enumeration representing the sub-locator types for the locator operation.
     */
    enum class SubLocatorType(val value: String) {
        CLASS("CLASS"),
        METHOD("METHOD");

        companion object {
            // Method to get SubLocatorType from string
            fun fromString(type: String): SubLocatorType? {
                return SubLocatorType.entries.find { it.value.equals(type, ignoreCase = true) }
            }
        }
    }

    /**
     * Data class to encapsulate locator information.
     * This class holds all necessary details for a locator operation.
     *
     * @property id The unique identifier of the element to locate.
     * @property name The name of the element to locate.
     * @property type The type of element to locate (e.g., "bto", "dto").
     * @property searchKey The key used for searching the element.
     * @property subLocatorType The sub-type of the locator (e.g., "CLASS", "METHOD").
     * @property projectId The unique identifier of the project.
     * @property moduleId The identifier of the module within the project.
     * @property moduleName The name of the module within the project.
     */
    data class LocatorInfo(
        val id: String,
        val name: String,
        val type: String,
        val searchKey: String,
        val subLocatorType: String,
        val projectId: String,
        val moduleId: String,
        val moduleName: String,
    ) {
        companion object {
            /**
             * Creates a LocatorInfo instance from a map of key-value pairs.
             *
             * @param map A map containing the locator information.
             * @return A new LocatorInfo instance populated with data from the map.
             */
            fun fromMap(map: JsonObject): LocatorInfo {
                return LocatorInfo(
                    id = map.getStringOrNull("id") ?: "",
                    name = map.getStringOrNull("name") ?: "",
                    type = map.getStringOrNull("type") ?: "",
                    searchKey = map.getStringOrNull("searchKey") ?: "",
                    subLocatorType = map.getStringOrNull("subLocatorType") ?: SubLocatorType.CLASS.value,
                    projectId = map.getStringOrNull("projectId") ?: "",
                    moduleId = map.getStringOrNull("moduleId") ?: "",
                    moduleName = map.getStringOrNull("moduleName") ?: "",
                )
            }
        }
    }

    /**
     * Data class for storing highlight information in a locator operation.
     * This class encapsulates all necessary details related to highlights
     * that are used during the locator process.
     *
     * @property id The unique identifier for the highlight. This is used to distinguish each highlight instance.
     * @property type The type of the highlight, which could define the nature or category of the highlight.
     * @property projectId The unique identifier of the project associated with the highlight. This links the highlight to a specific project context.
     * @property path The file path related to the highlight, specifying which file within the project is highlighted.
     * @property checksum The checksum of the file to ensure data integrity. This is used to verify that the file has not been altered.
     * @property location A list of highlight locations associated with this highlight. Each location provides details about where the highlight occurs within the file.
     */
    data class HighlightInfo(
        val id: String,
        val type: String,
        val projectId: String,
        val path: String,
        val checksum: String,
        val location: List<HighlightLocation>
    ) {
        companion object {
            /**
             * Creates a HighlightInfo instance from a map of key-value pairs.
             *
             * @param map A map containing the highlight information.
             * @return A new HighlightInfo instance populated with data from the map, or null if required fields are missing.
             * The function extracts each required field from the map, ensuring the presence of mandatory fields before constructing the object.
             */
            fun fromMap(map: JsonObject): HighlightInfo? {
                val id = map.getStringOrNull("id") ?: ""
                val type = map.getStringOrNull("type") ?: ""
                val projectId = map.getStringOrNull("projectId") ?: ""
                val path = map.getStringOrNull("path") ?: ""
                val checksum = map.getStringOrNull("checksum") ?: ""

                val locationList = map.getAsJsonArray("location").toList().mapNotNull { loc ->
                    val map = loc.asJsonObject
                    val author = map.getStringOrNull("author") ?: return@mapNotNull null
                    val count = map["count"].asInt
                    val lineRanges = map.getStringOrNull("line_ranges") ?: return@mapNotNull null
                    HighlightLocation(author, count, lineRanges)
                }

                return HighlightInfo(id, type, projectId, path, checksum, locationList)
            }
        }
    }

    /**
     * Data class representing the details of a highlight location.
     * This includes information about the author, the number of occurrences,
     * and the line ranges where the highlight is applied.
     *
     * @property author The author responsible for the highlight. This indicates who initiated or created the highlight.
     * @property count The number of occurrences of the highlight, representing how many times the highlight is applied within the file.
     * @property lineRanges A string representing the line ranges where the highlight occurs. This specifies the exact lines within the file that are highlighted.
     */
    data class HighlightLocation(
        val author: String,
        val count: Int,
        val lineRanges: String
    )

    /**
     * Data class representing a range of lines in the editor.
     * This captures the starting and ending line numbers for a specific highlight.
     *
     * @property startLineNo The starting line number of the range (1-based index).
     * @property endLineNo The ending line number of the range (1-based index), marking the conclusion of the highlight.
     */
    data class LineRange(
        val startLineNo: Int,
        val endLineNo: Int
    )

    /**
     * Lazy-initialized instance of ClassLocatorRuler for handling class-specific locator operations.
     */
    private val classLocatorRuler by lazy { ClassLocatorRuler(project) }

    /**
     * Lazy-initialized instance of MethodLocatorRuler for handling method-specific locator operations.
     */
    private val methodLocatorRuler by lazy { MethodLocatorRuler(project) }

    /**
     * Constructs a LocatorInfo object with the given parameters and project-specific information.
     * Returns null if any of the required fields (id, type, name, moduleId, moduleName) are empty.
     *
     * @param locatorIndex The LocatorIndex object containing the element information.
     * @param moduleId The identifier of the module containing the element.
     * @param moduleName The name of the module containing the element.
     * @return A LocatorInfo object containing the provided information and additional project details, or null if any required field is empty.
     */
    fun buildLocatorInfo(
        locatorIndex: LocatorIndex,
        moduleId: String,
        moduleName: String?
    ): LocatorInfo? {
        // Check if any of the required fields are empty
        if (locatorIndex.id.isEmpty() || locatorIndex.type.isEmpty() || locatorIndex.name.isNullOrEmpty()
            || moduleId.isEmpty() || moduleName.isNullOrEmpty() || locatorIndex.searchKey.isEmpty()
        ) {
            // Log a warning message if any required field is empty
            logger.debug(
                "One or more required fields are empty in buildLocatorInfo: " +
                        "id=${locatorIndex.id}, type=${locatorIndex.type}, name=${locatorIndex.name}, searchKey=${locatorIndex.searchKey}, " +
                        "moduleId=$moduleId, moduleName=$moduleName"
            )
            return null
        }

        // Retrieve the project ID from the project settings
        val projectId = ProjectPluginSettings.getInstance(project).projectId ?: ""

        // Check if the project ID is empty
        if (projectId.isEmpty()) {
            logger.warn("Project ID is empty in buildLocatorInfo")
            return null
        }

        // Construct and return the LocatorInfo object with all the required information
        return LocatorInfo(
            id = locatorIndex.id,
            name = locatorIndex.name,
            type = locatorIndex.type,
            searchKey = locatorIndex.searchKey,
            subLocatorType = locatorIndex.subLocatorType ?: SubLocatorType.CLASS.value,
            projectId = projectId,
            moduleId = moduleId,
            moduleName = moduleName,
        )
    }

    /**
     * Extension function to convert a LocatorInfo object to a map for JSON serialization.
     *
     * @return A map representation of the LocatorInfo object.
     */
    fun LocatorInfo.toMap(): Map<String, Any> {
        return mapOf(
            "id" to id,
            "type" to type,
            "projectId" to projectId,
            "moduleId" to moduleId,
        )
    }

    /**
     * Builds a locator request object from a PsiElement with AutoGenerated annotation.
     * This method checks only the current element and does not traverse up the parent hierarchy.
     * It delegates the request building to specific locator rulers based on the element type.
     *
     * @param element PsiElement to extract information from (must be PsiClass or PsiMethod).
     * @return LocatorInfo representing the locator request, or null if the element type is unsupported.
     */
    fun buildFromPsiElement(element: PsiElement): LocatorInfo? {
        // Check if the element is valid before processing
        if (!element.isValid) {
            logger.warn(getI18nString("invalid.psi.element", element::class.java.simpleName))
            return null
        }

        return when (element) {
            is PsiClass -> classLocatorRuler.buildRequest(element, project)
            is PsiMethod -> methodLocatorRuler.buildRequest(element, project)
            else -> {
                logger.warn("Unsupported element type: ${element::class.java.simpleName}")
                null
            }
        }
    }
}
