package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * Service class responsible for managing the index of locator elements with an optimized caching strategy.
 * Provides thread-safe operations and enhances performance through batch processing and regular cleanup.
 *
 * @param project The project to which this index manager is attached.
 */
@Service(Service.Level.PROJECT)
class LocatorIndexManager(private val project: Project) {
    private val logger = Logger.getInstance(LocatorIndexManager::class.java)
    private val indexCache: MutableMap<String, WeakReference<String>> = ConcurrentHashMap()
    private val maxSize = 1000 // Maximum size of the cache

    init {
        startCleanupTask(60000) // Start cleanup task with a 60-second interval
    }

    /**
     * Adds or updates an index entry for a given search key.
     *
     * @param searchKey The key to be indexed.
     * @param filePath The file path associated with the search key.
     */
    fun addOrUpdateIndex(searchKey: String, filePath: String) {
        putInCache(searchKey, filePath)
        logger.info("Index updated for key: $searchKey")
    }

    /**
     * Adds or updates multiple index entries in a batch operation.
     *
     * @param entries A map of search keys to their corresponding file paths.
     */
    fun addOrUpdateIndexes(entries: Map<String, String>) {
        entries.forEach { (key, value) -> putInCache(key, value) }
        logger.info("Batch index update with ${entries.size} entries")
    }

    /**
     * Removes an index entry for a given search key.
     *
     * @param searchKey The key to be removed from the index.
     */
    fun removeIndex(searchKey: String) {
        indexCache.remove(searchKey)
        logger.info("Index removed for key: $searchKey")
    }

    /**
     * Retrieves the file path associated with a given search key.
     *
     * @param searchKey The key to search for in the index.
     * @return The file path associated with the search key, or null if not found.
     */
    fun getFilePath(searchKey: String): String? {
        return getFromCache(searchKey)
    }

    /**
     * Clears the entire index cache.
     * Useful for resetting the state or freeing resources.
     */
    fun clearIndexCache() {
        try {
            indexCache.clear()
            logger.info("Index cache cleared")
        } catch (e: Exception) {
            logger.warn("Failed to clear index cache", e)
        }
    }

    /**
     * Puts an entry in the cache, evicting the oldest entry if the cache size exceeds the maximum.
     *
     * @param key The search key to be indexed.
     * @param value The file path to be associated with the search key.
     */
    private fun putInCache(key: String, value: String) {
        if (indexCache.size >= maxSize) {
            indexCache.keys.iterator().let { if (it.hasNext()) indexCache.remove(it.next()) }
        }
        indexCache[key] = WeakReference(value)
    }

    /**
     * Retrieves a value from the cache.
     *
     * @param key The search key to look up.
     * @return The value associated with the key, or null if not found or cleared.
     */
    private fun getFromCache(key: String): String? {
        return indexCache[key]?.get()
    }

    /**
     * Starts a cleanup task to regularly remove outdated entries from the cache.
     *
     * @param interval The interval in milliseconds between cleanups.
     */
    private fun startCleanupTask(interval: Long) {
        val executor = Executors.newSingleThreadScheduledExecutor()
        executor.scheduleAtFixedRate({
            indexCache.entries.removeIf { it.value.get() == null }
//            logger.info("Regular cleanup executed")
        }, interval, interval, TimeUnit.MILLISECONDS)
    }

    companion object {
        /**
         * Retrieves the instance of LocatorIndexManager for the given project.
         *
         * @param project The project for which to retrieve the service instance.
         * @return The instance of LocatorIndexManager.
         */
        fun getInstance(project: Project): LocatorIndexManager = project.getService(LocatorIndexManager::class.java)
    }
}
