package com.think1024.tocodesign.ideaplugin.services

import com.intellij.diff.comparison.ComparisonManager
import com.intellij.diff.comparison.ComparisonPolicy
import com.intellij.diff.fragments.LineFragment
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.event.EditorMouseEvent
import com.intellij.openapi.editor.event.EditorMouseListener
import com.intellij.openapi.editor.event.VisibleAreaEvent
import com.intellij.openapi.editor.event.VisibleAreaListener
import com.intellij.openapi.editor.markup.*
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.ui.popup.PopupStep
import com.intellij.openapi.ui.popup.util.BaseListPopupStep
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.util.TextRange
import com.intellij.ui.JBColor
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import java.awt.*
import java.awt.event.ActionEvent
import java.awt.event.ActionListener
import java.awt.event.ComponentAdapter
import java.awt.event.ComponentEvent
import javax.swing.*
import javax.swing.border.EmptyBorder
import javax.swing.SwingUtilities

@Service(Service.Level.PROJECT)
class InlineDiffService(private val project: Project) {
    private val logger = Logger.getInstance(InlineDiffService::class.java)

    private val activeHighlighters = mutableMapOf<String, List<RangeHighlighter>>()
    private val inlineElements = mutableMapOf<String, MutableList<InlineElement>>()
    private val activeInlays = mutableMapOf<String, MutableList<com.intellij.openapi.editor.Inlay<*>>>()
    private val actionButtonInlays = mutableMapOf<String, MutableList<com.intellij.openapi.editor.Inlay<*>>>()

    // 悬浮面板管理
    private val floatingPanels = mutableMapOf<String, JPanel>()
    private val floatingPanelListeners = mutableMapOf<String, ComponentAdapter>()
    private val floatingPanelScrollListeners = mutableMapOf<String, VisibleAreaListener>()

    // 类似GitHub Copilot的颜色方案
    private val ADDED_BACKGROUND = JBColor(Color(0, 100, 0, 30), Color(0, 100, 0, 40))
    private val DELETED_BACKGROUND = JBColor(Color(100, 0, 0, 30), Color(100, 0, 0, 40))
    private val ADDED_BORDER = JBColor(Color(0, 150, 0, 80), Color(0, 150, 0, 100))

    private val ACCEPT_BACKGROUND = JBColor(Color(0, 120, 0), Color(0, 150, 0))
    private val DISCARD_BACKGROUND = JBColor(Color(120, 0, 0), Color(150, 0, 0))

    /**
     * 在编辑器中显示内联diff - 类似GitHub Copilot的代码审查功能
     */
    fun showInlineDiff(relativeFilePath: String, originalContent: String): Boolean {
        // 确保在 EDT 线程中执行所有 UI 操作
        if (!com.intellij.openapi.application.ApplicationManager.getApplication().isDispatchThread) {
            // 如果不在 EDT 线程，切换到 EDT 线程执行
            com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                performShowInlineDiff(relativeFilePath, originalContent)
            }
            return true
        } else {
            // 已经在 EDT 线程中，直接执行
            return performShowInlineDiff(relativeFilePath, originalContent)
        }
    }

    /**
     * 在 EDT 线程中执行显示内联diff的操作
     */
    private fun performShowInlineDiff(relativeFilePath: String, originalContent: String): Boolean {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val virtualFile = FileUtil.getVirtualFile(relativeFilePath, project)
            ?: return false

        // 获取当前编辑器
        val editor = run {
            // 检查文件是否在当前活跃标签
            val isActiveTab = fileEditorManager.selectedFiles.contains(virtualFile)

            if (!isActiveTab) {
                // 文件已打开但不是当前活跃的，激活它
                fileEditorManager.openFile(virtualFile, true)
            }

            // 获取当前编辑器（现在应该是激活状态了）
            val fileEditor = fileEditorManager.getSelectedEditor(virtualFile)
            if (fileEditor is com.intellij.openapi.fileEditor.TextEditor) {
                fileEditor.editor
            } else null
        } ?: return false

        // 确保在 EDT 线程中执行 UI 操作
        if (!com.intellij.openapi.application.ApplicationManager.getApplication().isDispatchThread) {
            // 如果不在 EDT 线程，切换到 EDT 线程执行
            com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                performInlineDiff(editor, relativeFilePath, originalContent)
            }
            return true
        } else {
            // 已经在 EDT 线程中，直接执行
            return performInlineDiff(editor, relativeFilePath, originalContent)
        }
    }

    /**
     * 执行内联diff的核心逻辑
     */
    private fun performInlineDiff(editor: Editor, relativeFilePath: String, originalContent: String): Boolean {
        logger.info("Performing inline diff for file: $relativeFilePath,  original content : ${originalContent}")

        val document = editor.document
        val currentContent = document.text

        // 清除之前的高亮
        clearInlineDiff(relativeFilePath)

        return try {
            // 计算差异
            val comparisonManager = ComparisonManager.getInstance()
            val fragments = comparisonManager.compareLines(
                originalContent,
                currentContent,
                ComparisonPolicy.DEFAULT,
                com.intellij.openapi.progress.DumbProgressIndicator.INSTANCE
            )

            val highlighters = mutableListOf<RangeHighlighter>()
            val elements = mutableListOf<InlineElement>()

            // 为每个差异片段添加高亮
            fragments.forEach { fragment ->
                val result = addCodeReviewHighlight(editor, fragment, originalContent, relativeFilePath)
                highlighters.addAll(result.first)
                elements.addAll(result.second)
            }

            activeHighlighters[relativeFilePath] = highlighters
            inlineElements[relativeFilePath] = elements

            // 如果有差异，显示悬浮面板
            if (fragments.isNotEmpty()) {
                showFloatingPanel(relativeFilePath, originalContent)
            }

            true

        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 为差异片段添加类似GitHub Copilot的代码审查高亮
     */
    private fun addCodeReviewHighlight(
        editor: Editor,
        fragment: LineFragment,
        originalContent: String,
        relativeFilePath: String
    ): Pair<List<RangeHighlighter>, List<InlineElement>> {
        val document = editor.document
        val markupModel = editor.markupModel
        val highlighters = mutableListOf<RangeHighlighter>()
        val elements = mutableListOf<InlineElement>()

        // 确保 activeInlays 中有对应文件的列表
        val inlays = activeInlays.getOrPut(relativeFilePath) { mutableListOf() }
        val actionInlays = actionButtonInlays.getOrPut(relativeFilePath) { mutableListOf() }

        val diffType = getDiffType(fragment)
        val originalLines = originalContent.lines()

        when (diffType) {
            DiffType.ADDED -> {
                // 新增的内容用绿色背景标记，添加左边框效果
                val startLine = fragment.startLine2
                val endLine = fragment.endLine2

                if (startLine < document.lineCount) {
                    val startOffset = document.getLineStartOffset(startLine)
                    val endOffset = if (endLine < document.lineCount) {
                        document.getLineEndOffset(endLine - 1)
                    } else {
                        document.textLength
                    }

                    // 为整个新增块添加背景高亮
                    val addedAttributes = TextAttributes()
                    addedAttributes.backgroundColor = ADDED_BACKGROUND

                    val highlighter = markupModel.addRangeHighlighter(
                        startOffset,
                        endOffset,
                        HighlighterLayer.SELECTION - 1,
                        addedAttributes,
                        HighlighterTargetArea.LINES_IN_RANGE
                    )

                    // 添加左边框渲染器
                    highlighter.customRenderer = LeftBorderRenderer(ADDED_BORDER)

                    highlighters.add(highlighter)

                    // 在新增块的第一行右侧添加操作按钮
                    val firstLineEndOffset = if (startLine < document.lineCount) {
                        document.getLineEndOffset(startLine)
                    } else {
                        document.textLength
                    }
                    addActionButtons(editor, firstLineEndOffset, fragment, originalContent, diffType, relativeFilePath, actionInlays)
                }
            }

            DiffType.DELETED -> {
                // 删除的内容：使用 Inlay 虚拟显示，不修改实际文档
                val insertPosition = fragment.startLine2
                val originalStartLine = fragment.startLine1
                val originalEndLine = fragment.endLine1

                if (originalStartLine < originalLines.size) {
                    val deletedLines = originalLines.subList(
                        originalStartLine,
                        minOf(originalEndLine, originalLines.size)
                    )

                    if (deletedLines.isNotEmpty()) {
                        // 使用 Inlay 在指定位置虚拟显示删除的内容
                        val insertOffset = if (insertPosition < document.lineCount) {
                            document.getLineStartOffset(insertPosition)
                        } else {
                            document.textLength
                        }

                        // 创建删除内容的 Inlay 渲染器
                        val deletedContent = deletedLines.joinToString("\n")
                        val inlayRenderer = DeletedContentInlayRenderer(
                            deletedContent,
                            this@InlineDiffService
                        )

                        // 添加块级 Inlay
                        val inlayManager = editor.inlayModel
                        val inlay = inlayManager.addBlockElement(
                            insertOffset,
                            true,
                            true,
                            0,
                            inlayRenderer
                        )

                        if (inlay != null) {
                            // 将 Inlay 添加到追踪列表中
                            inlays.add(inlay)

                            // 创建虚拟高亮器
                            val virtualHighlighter = markupModel.addRangeHighlighter(
                                insertOffset,
                                insertOffset,
                                HighlighterLayer.ADDITIONAL_SYNTAX,
                                null,
                                HighlighterTargetArea.EXACT_RANGE
                            )

                            highlighters.add(virtualHighlighter)

                            // 在删除内容的第一行右侧添加操作按钮（使用插入位置的行尾）
                            val firstLineEndOffset = if (insertPosition < document.lineCount) {
                                document.getLineEndOffset(insertPosition)
                            } else {
                                document.textLength
                            }
                            addActionButtons(editor, firstLineEndOffset, fragment, originalContent, diffType, relativeFilePath, actionInlays)
                        }

                        // 创建内联元素信息
                        val inlineElement = InlineElement(
                            position = insertPosition,
                            content = deletedLines,
                            type = DiffType.DELETED,
                            fragment = fragment
                        )
                        elements.add(inlineElement)
                    }
                }
            }

            DiffType.MODIFIED -> {
                // 修改的内容：使用 Inlay 显示删除的内容，然后标记新增的内容
                val startLine = fragment.startLine2
                val endLine = fragment.endLine2
                val originalStartLine = fragment.startLine1
                val originalEndLine = fragment.endLine1

                if (startLine < document.lineCount && originalStartLine < originalLines.size) {
                    val deletedLines = originalLines.subList(
                        originalStartLine,
                        minOf(originalEndLine, originalLines.size)
                    )

                    if (deletedLines.isNotEmpty()) {
                        // 1. 使用 Inlay 在新内容上方显示删除的内容
                        val insertOffset = document.getLineStartOffset(startLine)
                        val deletedContent = deletedLines.joinToString("\n")
                        val inlayRenderer = DeletedContentInlayRenderer(
                            deletedContent,
                            this@InlineDiffService
                        )

                        val inlayManager = editor.inlayModel
                        val inlay = inlayManager.addBlockElement(
                            insertOffset,
                            true,
                            true,
                            0,
                            inlayRenderer
                        )

                        if (inlay != null) {
                            // 将 Inlay 添加到追踪列表中
                            inlays.add(inlay)

                            // 创建虚拟高亮器
                            val virtualHighlighter = markupModel.addRangeHighlighter(
                                insertOffset,
                                insertOffset,
                                HighlighterLayer.ADDITIONAL_SYNTAX + 1,
                                null,
                                HighlighterTargetArea.EXACT_RANGE
                            )

                            highlighters.add(virtualHighlighter)
                        }

                        // 2. 为修改后的内容（新增部分）添加绿色背景高亮和左边框
                        val startOffset = document.getLineStartOffset(startLine)
                        val endOffset = if (endLine < document.lineCount) {
                            document.getLineEndOffset(endLine - 1)
                        } else {
                            document.textLength
                        }

                        val addedAttributes = TextAttributes()
                        addedAttributes.backgroundColor = ADDED_BACKGROUND

                        val addedHighlighter = markupModel.addRangeHighlighter(
                            startOffset,
                            endOffset,
                            HighlighterLayer.SELECTION - 1,
                            addedAttributes,
                            HighlighterTargetArea.LINES_IN_RANGE
                        )

                        addedHighlighter.customRenderer = LeftBorderRenderer(ADDED_BORDER)

                        highlighters.add(addedHighlighter)

                        // 在修改块的第一行右侧添加操作按钮
                        val firstLineEndOffset = document.getLineEndOffset(startLine)
                        addActionButtons(editor, firstLineEndOffset, fragment, originalContent, diffType, relativeFilePath, actionInlays)

                        // 添加内联元素信息
                        val inlineElement = InlineElement(
                            position = startLine,
                            content = deletedLines,
                            type = DiffType.MODIFIED,
                            fragment = fragment
                        )
                        elements.add(inlineElement)
                    }
                }
            }
        }

        return Pair(highlighters, elements)
    }

    /**
     * 添加操作按钮（Accept和Discard）到diff块的右侧
     */
    private fun addActionButtons(
        editor: Editor,
        offset: Int,
        fragment: LineFragment,
        originalContent: String,
        diffType: DiffType,
        relativeFilePath: String,
        actionInlays: MutableList<com.intellij.openapi.editor.Inlay<*>>
    ) {
        val inlayManager = editor.inlayModel
        val actionRenderer = DiffActionButtonRenderer(
            editor,
            fragment,
            originalContent,
            diffType,
            this@InlineDiffService,
            relativeFilePath
        )

        val actionInlay = inlayManager.addInlineElement(
            offset,
            true,
            actionRenderer
        )

        if (actionInlay != null) {
            actionInlays.add(actionInlay)
        }
    }

    /**
     * 左边框渲染器 - 在高亮区域的左侧绘制彩色边框
     */
    private class LeftBorderRenderer(private val borderColor: Color) : CustomHighlighterRenderer {

        override fun paint(
            editor: Editor,
            highlighter: RangeHighlighter,
            g: Graphics
        ) {
            val g2d = g as Graphics2D
            val document = editor.document
            val scrollingModel = editor.scrollingModel

            // 获取高亮范围
            val startOffset = highlighter.startOffset
            val endOffset = highlighter.endOffset

            // 获取起始和结束行
            val startLine = document.getLineNumber(startOffset)
            val endLine = document.getLineNumber(endOffset)

            // 获取编辑器可见区域
            val visibleArea = scrollingModel.visibleArea
            val lineHeight = editor.lineHeight

            // 计算整个高亮区域的起始和结束Y坐标
            val startLineOffset = document.getLineStartOffset(startLine)
            val endLineOffset = if (endLine < document.lineCount) {
                document.getLineEndOffset(endLine)
            } else {
                document.textLength
            }

            val startY = editor.logicalPositionToXY(
                editor.offsetToLogicalPosition(startLineOffset)
            ).y

            val endY = editor.logicalPositionToXY(
                editor.offsetToLogicalPosition(endLineOffset)
            ).y + lineHeight

            // 检查是否在可见区域内
            if (startY <= visibleArea.y + visibleArea.height && endY >= visibleArea.y) {
                // 计算实际绘制的Y坐标范围（与可见区域的交集）
                val actualStartY = maxOf(startY, visibleArea.y)
                val actualEndY = minOf(endY, visibleArea.y + visibleArea.height)

                // 绘制连续的左边框线
                g2d.color = borderColor
                g2d.stroke = BasicStroke(3f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)

                // 在编辑器内容区域的最左侧绘制边框
                val leftMargin = editor.contentComponent.insets.left
                val x = leftMargin + 2 // 稍微向右偏移2像素

                // 绘制一条连续的垂直线
                g2d.drawLine(x, actualStartY, x, actualEndY)
            }
        }
    }

    /**
     * 获取diff类型
     */
    private fun getDiffType(fragment: LineFragment): DiffType {
        return when {
            fragment.startLine1 == fragment.endLine1 -> DiffType.ADDED
            fragment.startLine2 == fragment.endLine2 -> DiffType.DELETED
            else -> DiffType.MODIFIED
        }
    }

    /**
     * 清除指定文件的内联diff
     */
    fun clearInlineDiff(filePath: String) {
        activeHighlighters[filePath]?.forEach { highlighter ->
            highlighter.dispose()
        }
        activeHighlighters.remove(filePath)
        inlineElements.remove(filePath)

        // 清除 Inlay 元素
        activeInlays[filePath]?.forEach { inlay ->
            inlay.dispose()
        }
        activeInlays.remove(filePath)

        // 清除操作按钮 Inlay 元素
        actionButtonInlays[filePath]?.forEach { inlay ->
            inlay.dispose()
        }
        actionButtonInlays.remove(filePath)

        // 移除悬浮面板
        floatingPanels[filePath]?.let { panel ->
            // 停止监听器
            floatingPanelListeners[filePath]?.let { listener ->
                try {
                    panel.removeComponentListener(listener)
                } catch (e: Exception) {
                    logger.warn("Failed to remove component listener for $filePath", e)
                }
            }
            floatingPanelScrollListeners[filePath]?.let { listener ->
                try {
                    val editor = getEditorForFile(filePath)
                    // 检查编辑器是否仍然有效且未被销毁
                    if (editor != null && !editor.isDisposed) {
                        val scrollingModel = editor.scrollingModel
                        // 只有当滚动模型有效时才尝试移除监听器
                        scrollingModel?.removeVisibleAreaListener(listener)
                    }
                } catch (e: Exception) {
                    // 捕获并记录异常，但不中断清理流程
                    logger.warn("Failed to remove visible area listener for $filePath", e)
                }
            }

            // 从父容器中移除面板
            panel.parent?.remove(panel)

            // 清理面板内容
            panel.removeAll()
            panel.revalidate()
            panel.repaint()

            // 刷新父容器
            panel.parent?.revalidate()
            panel.parent?.repaint()
        }
        floatingPanels.remove(filePath)
        floatingPanelListeners.remove(filePath)
        floatingPanelScrollListeners.remove(filePath)
    }

    /**
     * 创建并显示悬浮面板
     */
    fun showFloatingPanel(relativeFilePath: String, originalContent: String) {
        val editor = getEditorForFile(relativeFilePath) ?: return

        // 如果已经有面板，先移除
        floatingPanels[relativeFilePath]?.let { existingPanel ->
            existingPanel.parent?.remove(existingPanel)
        }

        // 移除之前的监听器
        floatingPanelScrollListeners[relativeFilePath]?.let { listener ->
            editor.scrollingModel.removeVisibleAreaListener(listener)
        }

        // 创建悬浮面板
        val floatingPanel = createFloatingPanel(relativeFilePath, originalContent, editor)

        // 将面板添加到编辑器组件
        val editorComponent = editor.contentComponent
        editorComponent.add(floatingPanel)

        // 设置面板位置
        updateFloatingPanelPosition(floatingPanel, editor)

        // 添加组件监听器以动态调整位置
        val componentListener = object : ComponentAdapter() {
            override fun componentResized(e: ComponentEvent?) {
                updateFloatingPanelPosition(floatingPanel, editor)
            }
        }

        // 添加滚动监听器，确保面板始终固定在底部
        val scrollListener = object : VisibleAreaListener {
            override fun visibleAreaChanged(e: VisibleAreaEvent) {
                updateFloatingPanelPosition(floatingPanel, editor)
            }
        }

        editorComponent.addComponentListener(componentListener)
        editor.scrollingModel.addVisibleAreaListener(scrollListener)

        floatingPanelListeners[relativeFilePath] = componentListener
        floatingPanelScrollListeners[relativeFilePath] = scrollListener
        floatingPanels[relativeFilePath] = floatingPanel

        // 刷新显示
        editorComponent.revalidate()
        editorComponent.repaint()
    }

    /**
     * 创建悬浮面板UI
     */
    private fun createFloatingPanel(relativeFilePath: String, originalContent: String, editor: Editor): JPanel {
        val panel = JPanel(FlowLayout(FlowLayout.CENTER, 10, 8))
        panel.isOpaque = true
        panel.background = JBColor(Color(248, 248, 248, 240), Color(60, 63, 65, 240))
        panel.border = BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(JBColor(Color(200, 200, 200), Color(80, 80, 80)), 1),
            EmptyBorder(5, 15, 5, 15)
        )

        // Accept All 按钮
        val acceptAllButton = JButton("Accept All")
        acceptAllButton.background = ACCEPT_BACKGROUND
        acceptAllButton.foreground = Color.WHITE
        acceptAllButton.isOpaque = true
        acceptAllButton.isBorderPainted = false
        acceptAllButton.isContentAreaFilled = true
        acceptAllButton.isFocusPainted = false
        acceptAllButton.preferredSize = Dimension(90, 26)
        acceptAllButton.font = acceptAllButton.font.deriveFont(12f)

        // 强制设置UI为基础按钮UI，避免被系统Look and Feel覆盖
        acceptAllButton.setUI(javax.swing.plaf.basic.BasicButtonUI())

        acceptAllButton.addActionListener {
            handleAcceptAll(relativeFilePath)
        }

        // Discard All 按钮
        val discardAllButton = JButton("Discard All")
        discardAllButton.background = DISCARD_BACKGROUND
        discardAllButton.foreground = Color.WHITE
        discardAllButton.isOpaque = true
        discardAllButton.isBorderPainted = false
        discardAllButton.isContentAreaFilled = true
        discardAllButton.isFocusPainted = false
        discardAllButton.preferredSize = Dimension(90, 26)
        discardAllButton.font = discardAllButton.font.deriveFont(12f)
        // 强制设置UI为基础按钮UI，避免被系统Look and Feel覆盖
        discardAllButton.setUI(javax.swing.plaf.basic.BasicButtonUI())

        discardAllButton.addActionListener {
            handleDiscardAll(relativeFilePath, originalContent)
        }

        panel.add(acceptAllButton)
        panel.add(discardAllButton)

        return panel
    }

    /**
     * 更新悬浮面板位置 - 固定在编辑器可见区域底部悬浮
     */
    private fun updateFloatingPanelPosition(panel: JPanel, editor: Editor) {
        val visibleArea = editor.scrollingModel.visibleArea
        val editorComponent = editor.contentComponent

        // 计算面板大小
        val panelSize = panel.preferredSize

        // 计算面板位置：相对于可见区域的底部居中，距离底部margin 20px
        // 使用可见区域的坐标系，让面板悬浮在可见内容之上
        val panelX = visibleArea.x + (visibleArea.width - panelSize.width) / 2
        val panelY = visibleArea.y + visibleArea.height - panelSize.height - 20

        panel.setBounds(panelX, panelY, panelSize.width, panelSize.height)

        // 确保面板在最上层显示
        panel.parent?.setComponentZOrder(panel, 0)
    }

    /**
     * 处理Accept All操作
     */
    private fun handleAcceptAll(relativeFilePath: String) {
        // 清除所有diff高亮，相当于接受所有变更
        clearInlineDiff(relativeFilePath)
    }

    /**
     * 处理Discard All操作
     */
    private fun handleDiscardAll(relativeFilePath: String, originalContent: String) {
        val editor = getEditorForFile(relativeFilePath) ?: return

        // 将文档内容恢复为原始内容
        WriteCommandAction.runWriteCommandAction(project) {
            val document = editor.document
            document.setText(originalContent)
        }

        // 清除所有diff高亮
        clearInlineDiff(relativeFilePath)
    }

    /**
     * 内联元素数据类
     */
    data class InlineElement(
        val position: Int,
        val content: List<String>,
        val type: DiffType,
        val fragment: LineFragment
    )

    /**
     * Diff类型枚举
     */
    enum class DiffType {
        ADDED, DELETED, MODIFIED
    }

    /**
     * 删除内容的Inlay渲染器 - 显示删除的内容而不修改文档
     */
    private class DeletedContentInlayRenderer(
        private val deletedContent: String,
        private val service: InlineDiffService
    ) : EditorCustomElementRenderer {

        override fun calcWidthInPixels(inlay: com.intellij.openapi.editor.Inlay<*>): Int {
            val editor = inlay.editor
            // 返回编辑器的可见宽度，让背景色占满整行
            return editor.scrollingModel.visibleArea.width
        }

        override fun calcHeightInPixels(inlay: com.intellij.openapi.editor.Inlay<*>): Int {
            val editor = inlay.editor
            val font = editor.colorsScheme.getFont(com.intellij.openapi.editor.colors.EditorFontType.PLAIN)
            val fontMetrics = editor.contentComponent.getFontMetrics(font)
            val lineHeight = fontMetrics.height + 2

            // 分割删除的内容为多行（不过滤空行，保持原有结构）
            val lines = deletedContent.split('\n')

            // 计算总高度：只计算内容高度 + 边距（去掉头部）
            val contentHeight = lineHeight * lines.size
            return maxOf(contentHeight + 5, 20) // 至少20像素高度，减少边距
        }

        override fun paint(
            inlay: com.intellij.openapi.editor.Inlay<*>,
            g: Graphics,
            targetRegion: Rectangle,
            textAttributes: TextAttributes
        ) {
            val g2d = g as Graphics2D
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

            val editor = inlay.editor
            val font = editor.colorsScheme.getFont(com.intellij.openapi.editor.colors.EditorFontType.PLAIN)
            g2d.font = font.deriveFont(Font.ITALIC, font.size2D * 0.9f)

            val fontMetrics = g2d.fontMetrics
            val lineHeight = fontMetrics.height + 2

            // 分割删除的内容为多行（不过滤空行，保持原有结构）
            val lines = deletedContent.split('\n')

            // 绘制背景 - 占满整行宽度
            g2d.color = service.DELETED_BACKGROUND
            g2d.fillRect(targetRegion.x, targetRegion.y, targetRegion.width, targetRegion.height)

            // 直接绘制删除的内容（去掉头部标题和分割线）
            g2d.color = JBColor(Color(140, 0, 0), Color(180, 80, 80))
            g2d.font = font.deriveFont(Font.ITALIC, font.size2D * 0.85f)
            val contentFontMetrics = g2d.fontMetrics

            for (i in lines.indices) {
                val line = lines[i] // 保持原有的空行和缩进
                val yOffset = targetRegion.y + contentFontMetrics.ascent + 5 + i * lineHeight
                if (yOffset < targetRegion.y + targetRegion.height - 5) { // 确保不超出边界
                    g2d.drawString("  $line", targetRegion.x + 15, yOffset)
                }
            }

            // 在左侧绘制删除标记
            g2d.color = JBColor(Color(200, 0, 0), Color(220, 100, 100))
            g2d.stroke = BasicStroke(3f)
            g2d.drawLine(targetRegion.x + 2, targetRegion.y + 2, targetRegion.x + 2, targetRegion.y + targetRegion.height - 2)
        }
    }

    /**
     * Diff操作按钮渲染器 - 在每个diff块右侧显示Accept和Discard按钮
     */
    private class DiffActionButtonRenderer(
        private val editor: Editor,
        private val fragment: LineFragment,
        private val originalContent: String,
        private val diffType: DiffType,
        private val service: InlineDiffService,
        private val relativeFilePath: String
    ) : EditorCustomElementRenderer {

        private val buttonWidth = 50
        private val buttonHeight = 20
        private val buttonSpacing = 5
        private val totalWidth = buttonWidth * 2 + buttonSpacing + 15

        private var acceptButtonBounds: Rectangle? = null
        private var discardButtonBounds: Rectangle? = null

        // 缓存计算结果，避免重复计算
        private var cachedButtonX: Int = 0
        private var cachedVisibleWidth: Int = 0
        private var lastVisibleArea: Rectangle? = null

        init {
            // 添加鼠标监听器
            editor.addEditorMouseListener(object : EditorMouseListener {
                override fun mouseClicked(event: EditorMouseEvent) {
                    handleMouseClick(event)
                }
            })
        }

        override fun calcWidthInPixels(inlay: com.intellij.openapi.editor.Inlay<*>): Int {
            // 返回按钮实际需要的宽度
            return totalWidth
        }

        override fun calcHeightInPixels(inlay: com.intellij.openapi.editor.Inlay<*>): Int {
            return buttonHeight + 6
        }

        override fun paint(
            inlay: com.intellij.openapi.editor.Inlay<*>,
            g: Graphics,
            targetRegion: Rectangle,
            textAttributes: TextAttributes
        ) {
            val g2d = g as Graphics2D
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

            // 获取编辑器可见区域
            val visibleArea = editor.scrollingModel.visibleArea
            val editorComponent = editor.contentComponent

            // 只有在可见区域发生变化时才重新计算按钮位置
            val buttonsWidth = buttonWidth * 2 + buttonSpacing
            val rightMargin = 18

            if (lastVisibleArea != visibleArea || cachedVisibleWidth != visibleArea.width) {
                // 计算按钮应该显示的位置 - 在当前可见区域的右侧
                val visibleRight = visibleArea.x + visibleArea.width
                val buttonStartX = visibleRight - buttonsWidth - rightMargin

                // 确保按钮不会超出编辑器组件的边界
                val componentRight = editorComponent.width
                cachedButtonX = minOf(buttonStartX, componentRight - buttonsWidth - rightMargin)
                cachedVisibleWidth = visibleArea.width
                lastVisibleArea = Rectangle(visibleArea)
            }

            // 将绝对坐标转换为相对于targetRegion的坐标
            val relativeStartX = cachedButtonX - targetRegion.x
            val startY = targetRegion.y + 3

            // 只有在位置真正发生变化时才更新按钮边界
            val newAcceptBounds = Rectangle(relativeStartX, 3, buttonWidth, buttonHeight)
            val newDiscardBounds = Rectangle(relativeStartX + buttonWidth + buttonSpacing, 3, buttonWidth, buttonHeight)

            if (acceptButtonBounds != newAcceptBounds) {
                acceptButtonBounds = newAcceptBounds
            }
            if (discardButtonBounds != newDiscardBounds) {
                discardButtonBounds = newDiscardBounds
            }

            // Accept 按钮（使用勾号图标）
            val acceptButtonX = targetRegion.x + relativeStartX
            val acceptButtonY = startY

            // 内部类
            g2d.color = service.ACCEPT_BACKGROUND
            g2d.fillRoundRect(acceptButtonX, acceptButtonY, buttonWidth, buttonHeight, 5, 5)
            g2d.color = Color.WHITE
            g2d.stroke = BasicStroke(2f)

            // 绘制勾号图标
            val checkX = acceptButtonX + buttonWidth / 2
            val checkY = acceptButtonY + buttonHeight / 2
            g2d.drawLine(checkX - 4, checkY, checkX - 1, checkY + 3)
            g2d.drawLine(checkX - 1, checkY + 3, checkX + 4, checkY - 2)

            // Discard 按钮（使用叉号图标）
            val discardButtonX = targetRegion.x + relativeStartX + buttonWidth + buttonSpacing
            val discardButtonY = startY

            g2d.color = service.DISCARD_BACKGROUND
            g2d.fillRoundRect(discardButtonX, discardButtonY, buttonWidth, buttonHeight, 5, 5)
            g2d.color = Color.WHITE
            g2d.stroke = BasicStroke(2f)

            // 绘制叉号图标
            val crossX = discardButtonX + buttonWidth / 2
            val crossY = discardButtonY + buttonHeight / 2
            g2d.drawLine(crossX - 4, crossY - 4, crossX + 4, crossY + 4)
            g2d.drawLine(crossX - 4, crossY + 4, crossX + 4, crossY - 4)
        }

        private fun handleMouseClick(event: EditorMouseEvent) {
            val mouseEvent = event.mouseEvent
            val point = mouseEvent.point

            // 获取当前 inlay 的位置信息
            val inlay = editor.inlayModel.getInlineElementsInRange(0, editor.document.textLength)
                .find { it.renderer == this }

            if (inlay != null) {
                // 将编辑器绝对坐标转换为相对于 inlay 的坐标
                val inlayBounds = inlay.bounds
                if (inlayBounds != null) {
                    val relativeX = point.x - inlayBounds.x
                    val relativeY = point.y - inlayBounds.y
                    val relativePoint = Point(relativeX, relativeY)

                    // 检查点击是否在按钮范围内
                    acceptButtonBounds?.let { bounds ->
                        if (bounds.contains(relativePoint)) {
                            handleAcceptClick()
                            return
                        }
                    }

                    discardButtonBounds?.let { bounds ->
                        if (bounds.contains(relativePoint)) {
                            handleDiscardClick()
                            return
                        }
                    }
                }
            }
        }

        private fun handleAcceptClick() {
            SwingUtilities.invokeLater {
                acceptChange()
            }
        }

        private fun handleDiscardClick() {
            SwingUtilities.invokeLater {
                discardChange()
            }
        }

        private fun acceptChange() {
            // 移除此diff的高亮和按钮
            service.removeDiffHighlight(relativeFilePath, fragment)
        }

        private fun discardChange() {
            when (diffType) {
                DiffType.ADDED -> rejectAddition()
                DiffType.DELETED -> restoreDeleted()
                DiffType.MODIFIED -> restoreOriginal()
            }
            // 移除此diff的高亮和按钮
            service.removeDiffHighlight(relativeFilePath, fragment)
        }

        private fun rejectAddition() {
            WriteCommandAction.runWriteCommandAction(service.project) {
                val document = editor.document
                val startLine = fragment.startLine2
                val endLine = fragment.endLine2

                if (startLine < document.lineCount) {
                    val startOffset = document.getLineStartOffset(startLine)
                    val endOffset = if (endLine < document.lineCount) {
                        document.getLineEndOffset(endLine - 1)
                    } else {
                        document.textLength
                    }
                    document.deleteString(startOffset, endOffset)
                }
            }
        }

        private fun restoreDeleted() {
            val originalLines = originalContent.lines()
            val originalStartLine = fragment.startLine1
            val originalEndLine = fragment.endLine1

            if (originalStartLine < originalLines.size) {
                val restoredText = originalLines.subList(
                    originalStartLine,
                    minOf(originalEndLine, originalLines.size)
                ).joinToString("\n") + "\n"

                WriteCommandAction.runWriteCommandAction(service.project) {
                    val document = editor.document
                    val insertOffset = if (fragment.startLine2 < document.lineCount) {
                        document.getLineStartOffset(fragment.startLine2)
                    } else {
                        document.textLength
                    }
                    document.insertString(insertOffset, restoredText)
                }
            }
        }

        private fun restoreOriginal() {
            val originalLines = originalContent.lines()
            val originalStartLine = fragment.startLine1
            val originalEndLine = fragment.endLine1

            if (originalStartLine < originalLines.size) {
                val originalText = originalLines.subList(
                    originalStartLine,
                    minOf(originalEndLine, originalLines.size)
                ).joinToString("\n")

                WriteCommandAction.runWriteCommandAction(service.project) {
                    val document = editor.document
                    val startOffset = document.getLineStartOffset(fragment.startLine2)
                    val endOffset = if (fragment.endLine2 < document.lineCount) {
                        document.getLineEndOffset(fragment.endLine2 - 1)
                    } else {
                        document.textLength
                    }
                    document.replaceString(startOffset, endOffset, originalText)
                }
            }
        }
    }

    /**
     * 移除特定fragment的diff高亮
     */
    fun removeDiffHighlight(filePath: String, fragment: LineFragment) {
        val editor = getEditorForFile(filePath) ?: return

        // 移除相关的高亮器
        activeHighlighters[filePath]?.let { highlighters ->
            val toRemove = highlighters.filter { highlighter ->
                // 根据不同的diff类型使用不同的匹配策略
                when (getDiffType(fragment)) {
                    DiffType.DELETED -> {
                        // 对于删除类型，检查高亮器是否在删除位置
                        val expectedOffset = if (fragment.startLine2 < editor.document.lineCount) {
                            editor.document.getLineStartOffset(fragment.startLine2)
                        } else {
                            editor.document.textLength
                        }
                        highlighter.startOffset == expectedOffset
                    }
                    DiffType.ADDED -> {
                        // 对于新增类型，检查行号范围
                        val highlighterStartLine = editor.document.getLineNumber(highlighter.startOffset)
                        highlighterStartLine >= fragment.startLine2 && highlighterStartLine < fragment.endLine2
                    }
                    DiffType.MODIFIED -> {
                        // 对于修改类型，检查行号范围
                        val highlighterStartLine = editor.document.getLineNumber(highlighter.startOffset)
                        highlighterStartLine >= fragment.startLine2 && highlighterStartLine < fragment.endLine2
                    }
                }
            }
            toRemove.forEach { it.dispose() }

            val remaining = highlighters - toRemove.toSet()
            if (remaining.isEmpty()) {
                activeHighlighters.remove(filePath)
            } else {
                activeHighlighters[filePath] = remaining
            }
        }

        // 移除相关的Inlay元素
        activeInlays[filePath]?.removeIf { inlay ->
            val shouldRemove = when (getDiffType(fragment)) {
                DiffType.DELETED -> {
                    // 对于删除类型，检查inlay是否在删除位置
                    val expectedOffset = if (fragment.startLine2 < editor.document.lineCount) {
                        editor.document.getLineStartOffset(fragment.startLine2)
                    } else {
                        editor.document.textLength
                    }
                    inlay.offset == expectedOffset
                }
                DiffType.ADDED -> {
                    // 对于新增类型，检查inlay是否在新增范围内
                    val inlayLine = editor.document.getLineNumber(inlay.offset)
                    inlayLine >= fragment.startLine2 && inlayLine < fragment.endLine2
                }
                DiffType.MODIFIED -> {
                    // 对于修改类型，检查inlay是否在修改范围内
                    val inlayLine = editor.document.getLineNumber(inlay.offset)
                    inlayLine >= fragment.startLine2 && inlayLine < fragment.endLine2
                }
            }
            if (shouldRemove) {
                inlay.dispose()
            }
            shouldRemove
        }

        // 移除相关的操作按钮
        actionButtonInlays[filePath]?.removeIf { inlay ->
            val shouldRemove = when (getDiffType(fragment)) {
                DiffType.DELETED -> {
                    // 对于删除类型，检查按钮是否在删除位置
                    val expectedOffset = if (fragment.startLine2 < editor.document.lineCount) {
                        editor.document.getLineStartOffset(fragment.startLine2)
                    } else {
                        editor.document.textLength
                    }
                    inlay.offset == expectedOffset
                }
                DiffType.ADDED -> {
                    // 对于新增类型，检查按钮是否在新增范围内
                    val inlayLine = editor.document.getLineNumber(inlay.offset)
                    inlayLine >= fragment.startLine2 && inlayLine < fragment.endLine2
                }
                DiffType.MODIFIED -> {
                    // 对于修改类型，检查按钮是否在修改范围内
                    val inlayLine = editor.document.getLineNumber(inlay.offset)
                    inlayLine >= fragment.startLine2 && inlayLine < fragment.endLine2
                }
            }
            if (shouldRemove) {
                inlay.dispose()
            }
            shouldRemove
        }
    }

    // 添加一个辅助方法来获取editor实例
    private fun getEditorForFile(filePath: String): Editor? {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val virtualFile = FileUtil.getVirtualFile(filePath, project)
            ?: return null

        return fileEditorManager.getSelectedEditor(virtualFile)?.let { fileEditor ->
            if (fileEditor is com.intellij.openapi.fileEditor.TextEditor) {
                fileEditor.editor
            } else null
        }
    }
}
