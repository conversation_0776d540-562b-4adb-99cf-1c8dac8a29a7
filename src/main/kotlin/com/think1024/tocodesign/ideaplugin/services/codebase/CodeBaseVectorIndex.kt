package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.events.CodeBaseSyncStatusListener
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.HttpUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.set

@Service(Service.Level.PROJECT)
internal class CodeBaseVectorIndex(project: Project) : CodeBaseIndex(project) {
    override val logger = Logger.getInstance(CodeBaseVectorIndex::class.java)

    private val projectSettings = ProjectPluginSettings.Companion.getInstance(project)
    private val applicationSettings = ApplicationPluginSettings.getInstance()
    private val codebaseChunkBuilder = CodebaseChunkBuilder(project)

    // Buffer for file indices
    private val fileIndicesBuffer = ConcurrentHashMap<String, FileIndexMeta>()
    private var notSyncedFiles = 0 // 还未同步文件数
    private var currentSyncingFiles: List<String> = emptyList() // 当前正在同步的文件列表
    private var isSyncing = false // 是否正在同步
    private var isAborted = false // 是否已经dispose、clearIndex
    private val BATCH_SIZE = 10 // 每次同步的文件数量

    /**
     * 文件索引元数据数据类
     *
     * @property userId 用户ID
     * @property mac 唯一标识电脑的MAC地址
     * @property projectBasePath 唯一标识本地工程路径
     * @property tocoProjectId toco项目ID
     * @property filePath 文件路径
     * @property type 当前文件的更新状态 ('update'|'delete'|'add')
     * @property content 文件内容块列表
     * @property contentHash 文件内容的SHA-256哈希值
     */
    data class FileIndexMeta(
        val userId: String,
        val mac: String,
        val projectBasePath: String,
        val tocoProjectId: String,
        val filePath: String,
        val type: FileUpdateType,
        val content: List<CodebaseChunkBuilder.ContentBlock>? = null,
        val contentHash: String
    ) {
        /**
         * 将FileIndexMeta转换为Map（用于JSON序列化）
         */
        fun toMap(): Map<String, Any?> {
            val result = mutableMapOf<String, Any?>(
                "userId" to userId,
                "mac" to mac,
                "projectBasePath" to projectBasePath,
                "tocoProjectId" to tocoProjectId,
                "filePath" to filePath,
                "contentHash" to contentHash,
                "type" to type.value
            )

            content?.let {
                result["content"] = it.map { block -> block.toMap() }
            }

            return result
        }
    }

    internal fun validCheck(): Boolean {
        return !projectSettings.projectId.isNullOrEmpty()
                && applicationSettings.deviceId.isNotEmpty()
                && applicationSettings.userId.isNotEmpty()
    }

    private fun syncingStateChanged(isSyncing: Boolean) {
        this.isSyncing = isSyncing
        project.messageBus.syncPublisher(CodeBaseSyncStatusListener.TOPIC)
            .syncingStateChanged(isSyncing)
    }

    private fun notSyncedFilesChanged(count: Int) {
        notSyncedFiles = count
        project.messageBus.syncPublisher(CodeBaseSyncStatusListener.TOPIC)
            .notSyncedFilesChanged(count)
    }

    private fun currentFileListChanged(files: List<String>) {
        currentSyncingFiles = files
        project.messageBus.syncPublisher(CodeBaseSyncStatusListener.TOPIC)
            .currentFileListChanged(files)
    }

    override fun startSyncing() {
        if (isSyncing) {
            return
        }

        isAborted = false
        currentFileListChanged(emptyList())

        if (!validCheck()) {
            logger.warn("Cannot sync indices: Invalid settings")
            return
        }

        syncingStateChanged(true)
        notSyncedFilesChanged(fileIndicesBuffer.size)
        batchSyncIndex()
    }

    override fun updateFileIndex(relativePath: String, updateType: FileUpdateType, hash: String) {
        val fileIndexMeta = buildFromFile(relativePath, updateType, hash)

        fileIndexMeta?.content?.size?.let {
            if (it <= 2) {
                // 代表这个文件没有手写方法
                if (updateType == FileUpdateType.UPDATE) {
                    // 如果是更新，说明手写方法被删光了，要调用删除index
                    deleteFileIndex(relativePath)
                }
                return
            }
        }

        if (fileIndexMeta != null) {
            fileIndicesBuffer[fileIndexMeta.filePath] = fileIndexMeta
            logger.info("Added file to buffer: ${fileIndexMeta.filePath} with type: $updateType")
        }
    }

    override fun deleteFileIndex(filePath: String) {
        val fileIndexMeta = buildFromDeletedFile(filePath)
        if (fileIndexMeta != null) {
            fileIndicesBuffer[fileIndexMeta.filePath] = fileIndexMeta
            logger.info("Added deleted file to buffer: ${fileIndexMeta.filePath}")
        }
    }

    override fun batchSyncIndex() {
        if (isAborted) {
            logger.info("Skipping batch sync as service is disposed")
            return
        }

        if (fileIndicesBuffer.isEmpty()) {
            syncingStateChanged(false)
            currentFileListChanged(emptyList())
            logger.info("Index sync successfully completed")
            return
        }

        // 取出BATCH_SIZE个文件进行同步
        val batchIndices: Map<String, FileIndexMeta> =
            fileIndicesBuffer.entries.take(BATCH_SIZE).associate { it.key to it.value }

        fileIndicesBuffer.keys.removeAll(batchIndices.keys) // 从缓冲区中移除已同步的文件
        if (batchIndices.isNotEmpty()) {
            logger.info("Batch syncing ${batchIndices.size} files")
        }
        // 这里同步调用接口sync index
        runBlocking {
            sync(batchIndices)
        }

        Thread.sleep(100)
        batchSyncIndex()
    }

    suspend fun sync(indices: Map<String, FileIndexMeta>) {
        withContext(Dispatchers.IO) {

            if (isAborted) {
                logger.info("Skipping sync as service is disposed")
                fileIndicesBuffer.putAll(indices) // 将索引放回缓冲区
                return@withContext
            }

            val maxRetries = 5
            var currentRetry = 0
            var success = false

            while (!success && currentRetry < maxRetries && !isAborted) {
                try {
                    // Update current syncing files
                    currentFileListChanged(indices.keys.toList())

                    val host = applicationSettings.host
                    val path = "/api/ai/rag/code/rag"
                    val cookies = applicationSettings.cookies

                    val indicesList = indices.values.map { it.toMap() }
                    val requestBody = org.json.JSONArray(indicesList).toString()

                    logger.info("Syncing batch of ${indicesList.size} files to server (Attempt ${currentRetry + 1}/$maxRetries)")

                    val response = HttpUtil.post(host + path, HttpUtil.parseCookies(cookies), requestBody)
                    val jsonResponse = JSONObject(response)

                    if (jsonResponse.getInt("code") == 200) {
                        if (indicesList.isNotEmpty()) {
                            logger.info("Successfully synced batch of ${indicesList.size} files")
                        }
                        success = true
                    } else {
                        throw Exception("Failed to sync indices. Response: $response")
                    }
                } catch (e: Exception) {
                    if (isAborted) {
                        logger.info("Aborting sync as service is disposed")
                        fileIndicesBuffer.putAll(indices) // 将索引放回缓冲区
                        return@withContext
                    }
                    logger.warn("Failed to sync indices (Attempt ${currentRetry + 1}/$maxRetries)", e)
                    if (currentRetry < maxRetries - 1) {
                        val backoffTime = when (currentRetry) {
                            0 -> 1000L  // 1 second
                            1 -> 5000L  // 5 seconds
                            2 -> 10000L // 10 seconds
                            3 -> 30000L // 30 seconds
                            else -> 0L
                        }
                        logger.info("Retrying in ${backoffTime}ms...")
                        Thread.sleep(backoffTime)
                    }
                } finally {
                    currentRetry++
                }
            }

            if (!isAborted) {
                notSyncedFilesChanged(fileIndicesBuffer.size)
            }
            if (!success) {
                logger.warn("Failed to sync indices after $maxRetries attempts")
            }
        }
    }

    override fun clearIndex() {
        if (!validCheck()) {
            logger.warn("Cannot delete server indices: Invalid settings")
            return
        }

        try {
            val host = applicationSettings.host
            val path = "/api/ai/rag/code/drop"
            val cookies = applicationSettings.cookies

            val requestBody = JSONObject().apply {
                put("userId", applicationSettings.userId)
                put("mac", applicationSettings.deviceId)
                put("projectBasePath", project.basePath)
                put("tocoProjectId", projectSettings.projectId)
            }.toString()

            logger.info("Deleting server indices for project: ${project.name}")

            val response = HttpUtil.post(host + path, HttpUtil.parseCookies(cookies), requestBody)
            val jsonResponse = JSONObject(response)

            if (jsonResponse.getInt("code") != 200) {
                logger.warn("Failed to delete server indices. Response: $response")
            } else {
                fileIndicesBuffer.clear()
                isAborted = true
                syncingStateChanged(false)
                currentFileListChanged(emptyList())
                notSyncedFilesChanged(Int.MAX_VALUE) // Reset not synced files count
                logger.info("Successfully deleted server indices for project: ${project.name}")
            }
        } catch (e: Exception) {
            logger.warn("Failed to delete server indices", e)
        }
    }

    override fun dispose() {
        try {
            // Stop any ongoing operations
            isAborted = true
            syncingStateChanged(false)
            currentFileListChanged(emptyList())
            // Clear the buffer
            fileIndicesBuffer.clear()
            logger.info("CodeBaseVectorIndex disposed for project: ${project.name}")
        } catch (e: Exception) {
            logger.warn("Failed to dispose CodeBaseVectorIndex", e)
        }
    }

    private fun buildFromFile(relativePath: String, updateType: FileUpdateType, hash: String): FileIndexMeta? {
        val tocoProjectId = projectSettings.projectId ?: ""
        val userId = applicationSettings.userId
        val projectBasePath = project.basePath ?: ""
        val macAddress = applicationSettings.deviceId

        val contentBlocks = codebaseChunkBuilder.extractContentBlocks(
            relativePath
        )

        return FileIndexMeta(
            userId = userId,
            mac = macAddress,
            projectBasePath = projectBasePath,
            tocoProjectId = tocoProjectId,
            filePath = relativePath,
            type = updateType,
            content = contentBlocks,
            contentHash = hash
        )
    }

    private fun buildFromDeletedFile(filePath: String): FileIndexMeta? {
        val tocoProjectId = projectSettings.projectId
        val userId = applicationSettings.userId
        val projectBasePath = project.basePath ?: ""
        val relativePath = CodeBaseUtils.calculateRelativePath(projectBasePath, filePath)
        val macAddress = applicationSettings.deviceId

        return FileIndexMeta(
            userId = userId,
            mac = macAddress,
            projectBasePath = projectBasePath,
            tocoProjectId = tocoProjectId ?: "",
            filePath = relativePath,
            type = FileUpdateType.DELETE,
            content = null,
            contentHash = ""
        )
    }

    override fun search(query: String): List<SearchResult> {
        if (!validCheck()) {
            logger.warn("Cannot perform search: Invalid settings")
            return emptyList()
        }

        try {
            val host = applicationSettings.host
            val path = "/api/ai/rag/code/search"
            val cookies = applicationSettings.cookies

            val requestBody = JSONObject().apply {
                put("userId", applicationSettings.userId)
                put("mac", applicationSettings.deviceId)
                put("projectBasePath", project.basePath)
                put("tocoProjectId", projectSettings.projectId)
                put("query", query)
            }.toString()

            logger.info("Performing vector search for query: $query")

            val response = HttpUtil.post(host + path, HttpUtil.parseCookies(cookies), requestBody)
            val jsonResponse = JSONObject(response)

            if (jsonResponse.getInt("code") != 200) {
                logger.warn("Failed to perform vector search. Response: $response")
                return emptyList()
            }

            val results = jsonResponse.getJSONArray("data")
            val searchResults = mutableListOf<SearchResult>()

            for (i in 0 until results.length()) {
                val result = results.getJSONObject(i)
                val filePath = result.getString("filePath")
                val position = result.getJSONObject("position")
                val contentHash = result.getString("contentHash")
                val score = result.optDouble("score", 1.0).toFloat()

                // Get the actual file from the project
                val basePath = project.basePath ?: continue
                val virtualFile =
                    com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath("$basePath/$filePath")
                if (virtualFile == null) {
                    logger.warn("File not found: $filePath")
                    continue
                }

                val rawContent = String(virtualFile.contentsToByteArray())
                val currentHash = CodeBaseUtils.calculateContentHash(rawContent)
                if (currentHash != contentHash) {
                    logger.warn("Content hash mismatch for file: $filePath")
                    continue
                }

                val startLine = position.getInt("start")
                val endLine = position.getInt("end")

                val content = buildString {
                    append(result.getString("header"))
                    append("\n")
                    append(CodeBaseUtils.getFileContent(startLine, endLine, virtualFile))
                }
                searchResults.add(
                    SearchResult(
                        filePath = filePath,
                        score = score,
                        start = startLine,
                        end = endLine,
                        raw = content
                    )
                )
            }

            return searchResults
        } catch (e: Exception) {
            logger.warn("Error performing vector search", e)
            return emptyList()
        }
    }

    fun pauseSync() {
        isAborted = true
        syncingStateChanged(false)
        logger.info("Syncing paused")
    }

    fun getIsSyncing(): Boolean {
        return isSyncing
    }

    fun getCurrentSyncingFiles(): List<String> {
        return currentSyncingFiles
    }

    fun getNotSynedFiles(): Int {
        return fileIndicesBuffer.size
    }
}