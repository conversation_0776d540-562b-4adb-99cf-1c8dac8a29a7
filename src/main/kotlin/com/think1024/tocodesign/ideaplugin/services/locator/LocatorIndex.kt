package com.think1024.tocodesign.ideaplugin.services.locator

/**
 * Data class representing the locator index information.
 *
 * @property id The unique identifier for the locator.
 * @property type The type of the locator.
 * @property subType The sub-type of the locator, can be null.
 * @property name The name associated with the locator, can be null.
 * @property searchKey The search key associated with the locator.
 * @property subLocatorType The sub-locator type associated with the locator, can be null.
 */
data class LocatorIndex(
    val id: String,
    val type: String,
    val subType: String?,
    val name: String?,
    val searchKey: String,
    val subLocatorType: String?
)