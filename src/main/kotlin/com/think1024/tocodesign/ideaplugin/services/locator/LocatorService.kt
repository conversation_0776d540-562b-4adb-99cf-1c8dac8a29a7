package com.think1024.tocodesign.ideaplugin.services.locator

import com.google.gson.JsonObject
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorBuilder.LocatorInfo
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncher
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.getStringOrNull
import java.net.URL
import java.util.*


/**
 * LocatorService is responsible for managing locator-related operations in the IntelliJ IDEA plugin.
 * It handles communication with the Socket.IO server, processes locator requests and responses,
 * and coordinates the opening of specific elements in the desktop application.
 *
 * This service is scoped at the PROJECT level, meaning each open project in IDEA will have its own instance.
 *
 * @property project The IntelliJ IDEA project associated with this service instance.
 */
@Service(Service.Level.PROJECT)
class LocatorService(private val project: Project) {
    // Logger for this class
    private val logger = Logger.getInstance(LocatorService::class.java)

    private val highlightLocatorService = project.getService(HighlightLocatorService::class.java)

    // Locator rulers for different element types
    private val classLocatorRuler = ClassLocatorRuler(project)
    private val methodLocatorRuler = MethodLocatorRuler(project)

    // Builder for creating locator information
    private val locatorBuilder = LocatorBuilder(project)

    private fun convertUrl(option: LocatorInfo): String {
        val type = option.type.lowercase()
        var host = ApplicationPluginSettings.getInstance().frontendHost
        return when (type) {
            "vo" -> "$host/${option.projectId}/${option.moduleId}/model/vo?uuid=${option.id}"
            "bo" -> "$host/${option.projectId}/${option.moduleId}/model/er?bo=${option.id}"
            "bto" -> "$host/${option.projectId}/${option.moduleId}/service/write?uuid=${option.id}"
            "dto" -> "$host/${option.projectId}/${option.moduleId}/model/dto?uuid=${option.id}"
            "entity" -> "$host/${option.projectId}/${option.moduleId}/model/er?uuid=${option.id}"
            "rpc" -> "$host/${option.projectId}/${option.moduleId}/rpc?uuid=${option.id}"
            "api" -> "$host/${option.projectId}/${option.moduleId}/api?uuid=${option.id}"
            "qto" -> "$host/${option.projectId}/${option.moduleId}/service/read?uuid=${option.id}"
            "eo" -> "$host/${option.projectId}/${option.moduleId}/model/eo?uuid=${option.id}"
            "enum" -> "$host/${option.projectId}/${option.moduleId}/model/enum?uuid=${option.id}"
            "mo" -> "$host/${option.projectId}/${option.moduleId}/service/msg?tab=normal&&uuid=${option.id}"
            "dmo" -> "$host/${option.projectId}/${option.moduleId}/service/msg?tab=domain&&uuid=${option.id}"
            "flow_node" -> "$host/${option.projectId}/${option.moduleId}/service/flow?isNode=true&uuid=${option.id}"
            "flow" -> "$host/${option.projectId}/${option.moduleId}/service/flow?uuid=${option.id}"
            "flow_list" -> "$host/${option.projectId}/${option.moduleId}/service/flow"
            else -> ""
        }
    }

    /**
     * Opens the specified PsiElement in the toco editor
     * This method constructs a locator request for the given element and sends it via the Socket.IO connection.
     *
     * @param element The PSI element to be opened in the toco editor.
     */
    fun openTocoWebViewEditor(element: PsiElement) {
        val payload = locatorBuilder.buildFromPsiElement(element)
        payload?.let {
            val name = if (payload.type == "rpc" || payload.type == "api") {
                payload.name
            } else {
                payload.name.let {
                    it.split("(?=[A-Z])".toRegex()).filter { it.isNotEmpty() }
                        .joinToString("_") { s -> s.lowercase() }
                }
            }
            val url = convertUrl(payload)
            val type = URL(url).path.split("/").last()
            TocoWebViewLauncher.openPage(project, url, "[${payload.moduleName}] $name", type)
        }
    }

    /**
     * Handles incoming socket messages related to locator actions.
     * Based on the action specified in the message, this function delegates
     * the processing to the appropriate handler method.
     *
     * @param message The socket request message containing the action and any relevant data.
     *
     * The method processes actions as follows:
     * - If the action is LOCATE, it calls `handleLocateMessage` to process file location requests.
     * - If the action is HIGHLIGHT, it delegates to `highlightLocatorService.handleHighlightMessage`
     *   to manage highlight requests.
     * - For any other action, it sends a system error response back to the client,
     *   indicating an invalid message body.
     *
     * This function ensures that only recognized actions are processed,
     * maintaining robustness against unexpected input.
     */
    fun handleLocatorMessage(message: JsonObject, action: String, response: (result: String?) -> Unit) {
        when (action) {
            "locate" -> handleLocateMessage(message, response)
            "highlight" -> highlightLocatorService.handleHighlightMessage(message, response)
            else -> { response(getI18nString("locator.invalid.message.body")) }
        }
    }

    /**
     * Handles incoming locator messages from the SocketClient.
     *
     * This method processes a locator request message, validates the locator information,
     * determines the appropriate locator ruler based on the type, and attempts to locate
     * and navigate to the target file. If any error occurs during this process, an appropriate
     * error response is sent back.
     *
     * @param message The SocketRequestMessage containing the locator information.
     */
    private fun handleLocateMessage(message: JsonObject?, response: (result: String?) -> Unit) {
        // Check if the extracted locator information is null
        if (message == null) {
            // body无效，回调前端显示错误
            logger.warn("Invalid message body")

            // Send an error response if the locator information is missing
            response(getI18nString("locator.invalid.message.body"))
            return
        }

        // Convert the map to a LocatorInfo object for further processing
        val locatorInfo = LocatorBuilder.LocatorInfo.fromMap(message)
        val locatorType = locatorInfo.type.lowercase(Locale.getDefault())
        val subLocatorType = locatorInfo.subLocatorType.uppercase(Locale.getDefault())

        // Determine the appropriate locator ruler based on the locator type
        val locatorRuler: ILocatorRuler = when {
            // If both rulers support the same locatorType, use subLocatorType to determine the correct ruler
            classLocatorRuler.typeToPathMapping.containsKey(locatorType) &&
                    methodLocatorRuler.typeToPathMapping.containsKey(locatorType) -> {
                when (subLocatorType) {
                    LocatorBuilder.SubLocatorType.CLASS.value -> classLocatorRuler // Use class locator ruler if subLocatorType is CLASS
                    LocatorBuilder.SubLocatorType.METHOD.value -> methodLocatorRuler // Use method locator ruler if subLocatorType is METHOD
                    else -> {
                        logger.warn("Unsupported sub locator type: $subLocatorType")
                        response(getI18nString("locator.unsupported.sub.type", subLocatorType))
                        return
                    }
                }
            }

            // If only classLocatorRuler supports the locatorType
            classLocatorRuler.typeToPathMapping.containsKey(locatorType) -> classLocatorRuler

            // If only methodLocatorRuler supports the locatorType
            methodLocatorRuler.typeToPathMapping.containsKey(locatorType) -> methodLocatorRuler

            // If neither ruler supports the locatorType
            else -> {
                logger.warn("Unsupported locator type: $locatorType")

                // Send an error response if the locator type is unsupported
                response(getI18nString("locator.unsupported.type", locatorType))
                return
            }
        }

        try {
            // Attempt to locate the file and navigate to the target using the determined locator ruler
            locatorRuler.locateFileAndNavigateToTarget(locatorInfo)
            response(null)
        } catch (e: IllegalArgumentException) {
            // Handle error when a required field is missing
            logger.warn("Failed to locate file for type $locatorType due to missing required field", e)
            response(getI18nString("locator.missing.field"))
            return
        } catch (e: LocatorException) {
            // Handle failure when attempting to open and navigate to the target file
            logger.warn("Failed to open and navigate to target file for type $locatorType", e)
            response(getI18nString("locator.failed.to.open"))
            return
        } catch (_: Exception) {
            // Handle any unexpected errors that may arise during the process
            response(getI18nString("locator.failed.to.open"))
            return
        }
    }

    companion object {
        /**
         * Retrieves the LocatorService instance for the specified project.
         * This method provides a convenient way to access the LocatorService for a given project.
         *
         * @param project The project for which to obtain the service instance.
         * @return The LocatorService instance associated with the given project.
         */
        fun getInstance(project: Project): LocatorService = project.getService(LocatorService::class.java)
    }
}
