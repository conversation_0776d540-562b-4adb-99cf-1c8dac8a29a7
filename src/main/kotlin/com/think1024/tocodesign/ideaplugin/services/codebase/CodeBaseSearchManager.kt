package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.concurrency.AppExecutorUtil
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.HttpUtil
import org.json.JSONObject
import java.util.Stack
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import com.intellij.openapi.application.ApplicationManager
import org.json.JSONArray
import com.intellij.openapi.components.service
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.think1024.tocodesign.ideaplugin.events.CodeBaseSyncStatusListener
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseIndex.FileUpdateType
import com.intellij.openapi.vfs.newvfs.events.VFileMoveEvent
import com.intellij.openapi.vfs.newvfs.events.VFilePropertyChangeEvent

@Service(Service.Level.PROJECT)
class CodeBaseSearchManager(private val project: Project) {
    private val logger = Logger.getInstance(CodeBaseSearchManager::class.java)
    private val vectorIndex = project.service<CodeBaseVectorIndex>()
    private val bm25Index = project.service<CodeBaseBm25Index>()
    private val projectSettings = ProjectPluginSettings.Companion.getInstance(project)
    private val applicationSettings = ApplicationPluginSettings.getInstance()
    private val fileHashCacheMap = ConcurrentHashMap<String, String>()
    private var periodicSyncJob: Job? = null
    private var isScanningCodebase = false

    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob()) // 定期同步filehash cache
    private val parallelScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private var fileChangeListenerConnection: com.intellij.openapi.Disposable? = null
    private var totalFiles: Int = 0

    sealed class PendingFileEvent(val relativePath: String) {
        class AddOrUpdate(relativePath: String, val updateType: FileUpdateType) : PendingFileEvent(relativePath)
        class Delete(relativePath: String) : PendingFileEvent(relativePath)
    }


    // 同步时间点
    private val SYNC_TIMES = listOf(
        LocalTime.of(12, 30), // 中午12:30
        LocalTime.of(2, 30)   // 凌晨2:30
    )

    /**
     * 初始化
     */
    init {
        // 监听用户ID变化
        ApplicationManager.getApplication().messageBus.connect().subscribe(
            ApplicationPluginSettings.USER_ID_CHANGED_TOPIC,
            object : ApplicationPluginSettings.UserIdChangeListener {
                override fun userIdChanged(newUserId: String?) {
                    if (newUserId != null && newUserId.isNotEmpty()) {
                        initializeServices()
                    }
                }
            }
        )

        // 只有在有用户ID时才初始化
        if (applicationSettings.userId.isNotEmpty()) {
            initializeServices()
        }
    }

    /**
     * 初始化所有服务
     */
    private fun initializeServices() {
        // 确保只有在项目索引完成、处于"智能模式"时才执行括号内的代码，防止在索引未完成时访问 PSI 相关内容导致异常。
        DumbService.getInstance(project).smartInvokeLater {
            startScanCodeBase() // 启动一次全量扫描
        }

        // 安全地重新注册文件变化监听器
        fileChangeListenerConnection?.dispose()
        registerFileChangeListener()

        // 安全地重新启动定期同步fileHashCache任务
        periodicSyncJob?.cancel()
        startPeriodicSyncFileHashCache()
    }

    /**
     * 注册文件变化监听器
     */
    private fun registerFileChangeListener() {
        // 合并所有待处理事件，保证顺序
        val pendingEvents = java.util.concurrent.ConcurrentLinkedQueue<PendingFileEvent>()
        val processLock = Any()
        var processingJob: Job? = null

        fileChangeListenerConnection = project.messageBus.connect().apply {
            subscribe(VirtualFileManager.VFS_CHANGES, object : BulkFileListener {
                override fun after(events: List<VFileEvent>) {
                    val basePath = project.basePath ?: return
                    var changed = false
                    events.forEach { event ->
                        val file = event.file
                        val path = event.path
                        if (file == null) return@forEach
                        if (!isFilePathValid(path)) return@forEach

                        // 目录,需要递归处理下面所有的文件
                        if (file.isDirectory) {
                            // 目录删除
                            if (event is com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent) {
                                val relativeDirPath = CodeBaseUtils.calculateRelativePath(basePath, path)
                                val filesToDelete = fileHashCacheMap.keys.filter { it.startsWith("$relativeDirPath/") }

                                if (filesToDelete.isNotEmpty()) {
                                    changed = true
                                    filesToDelete.forEach { filePath ->
                                        pendingEvents.add(PendingFileEvent.Delete(filePath))
                                        totalFilesChanged(totalFiles - 1)
                                        logger.info("[VFS] Deleted file (in dir): $filePath")
                                    }
                                }
                            }
                            // 目录新建
                            else if (event is com.intellij.openapi.vfs.newvfs.events.VFileCreateEvent) {
                                fun addJavaFilesInDir(dir: VirtualFile) {
                                    dir.children.forEach { child ->
                                        if (child.isDirectory) {
                                            addJavaFilesInDir(child)
                                        } else if (child.extension == "java") {
                                            val relativePath = CodeBaseUtils.calculateRelativePath(basePath, child.path)
                                            try {
                                                pendingEvents.add(
                                                    PendingFileEvent.AddOrUpdate(
                                                        relativePath,
                                                        FileUpdateType.ADD
                                                    )
                                                )
                                                totalFilesChanged(totalFiles + 1)
                                                logger.info("[VFS] Created file (dir): $relativePath")
                                            } catch (e: Exception) {
                                                logger.warn("[VFS] Error reading created file (dir): $relativePath", e)
                                            }
                                        }
                                    }
                                }
                                addJavaFilesInDir(file)
                            }
                            return@forEach
                        }

                        // 文件
                        if (!path.endsWith(".java")) return@forEach

                        // 处理删除事件
                        if (event is com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent) {
                            val relativePath = CodeBaseUtils.calculateRelativePath(basePath, path)
                            pendingEvents.add(PendingFileEvent.Delete(relativePath))
                            totalFilesChanged(totalFiles - 1)
                            changed = true
                            logger.info("[VFS] Deleted file: $relativePath")
                        }
                        // 处理新建事件
                        else if (event is com.intellij.openapi.vfs.newvfs.events.VFileCreateEvent) {
                            val relativePath = CodeBaseUtils.calculateRelativePath(basePath, file!!.path)
                            try {
                                totalFilesChanged(totalFiles + 1)
                                pendingEvents.add(PendingFileEvent.AddOrUpdate(relativePath, FileUpdateType.ADD))
                                changed = true
                                logger.info("[VFS] Created file: $relativePath")
                            } catch (e: Exception) {
                                logger.warn("[VFS] Error reading created file: $relativePath", e)
                            }
                        }
                        // 处理移动/重命名事件
                        else if (event is VFileMoveEvent || (event is VFilePropertyChangeEvent && event.propertyName == VirtualFile.PROP_NAME)) {
                            val oldPath = when (event) {
                                is VFileMoveEvent -> event.oldPath
                                is VFilePropertyChangeEvent -> event.file.parent?.path + "/" + event.oldValue
                                else -> null
                            }
                            if (oldPath != null) {
                                val oldRelativePath = CodeBaseUtils.calculateRelativePath(basePath, oldPath)
                                val newRelativePath = CodeBaseUtils.calculateRelativePath(basePath, file!!.path)
                                pendingEvents.add(PendingFileEvent.Delete(oldRelativePath))
                                try {
                                    pendingEvents.add(PendingFileEvent.AddOrUpdate(newRelativePath, FileUpdateType.ADD))
                                    changed = true
                                    logger.info("[VFS] Moved/Renamed file: $oldRelativePath -> $newRelativePath")
                                } catch (e: Exception) {
                                    logger.warn("[VFS] Error reading moved/renamed file: $newRelativePath", e)
                                }
                            }
                        }
                        // 处理内容变更（修改）
                        else if (event.isFromRefresh.not()) {
                            val relativePath = CodeBaseUtils.calculateRelativePath(basePath, file!!.path)
                            try {
                                val content = String(file.contentsToByteArray())
                                val fileHash = CodeBaseUtils.calculateContentHash(content)
                                val updateType = determineFileUpdateOrAdd(relativePath, fileHash)
                                if (updateType != null) {
                                    pendingEvents.add(PendingFileEvent.AddOrUpdate(relativePath, updateType))
                                    changed = true
                                    logger.info("[VFS] Updated file: $relativePath")
                                }
                            } catch (e: Exception) {
                                logger.warn("[VFS] Error reading updated file: $relativePath", e)
                            }
                        }
                    }
                    if (changed) {
                        synchronized(processLock) {
                            if (processingJob == null || processingJob?.isCompleted == true) {
                                processingJob = parallelScope.launch {
                                    delay(5000) // 延迟5秒
                                    if (DumbService.getInstance(project).isDumb) {
                                        DumbService.getInstance(project).smartInvokeLater {
                                            processPendingEvents(pendingEvents)
                                            startSyncing()
                                        }
                                    } else {
                                        processPendingEvents(pendingEvents)
                                        startSyncing()
                                    }
                                }
                            }
                        }
                    }
                }
            })
        }
    }

    /**
     * 启动扫描代码库
     */
    fun startScanCodeBase() {
        if (isScanningCodebase) {
            logger.info("Codebase scan already in progress for ${project.name}, skipping...")
            return
        }
        // 将任务提交到应用级线程池，避免阻塞主线程。
        AppExecutorUtil.getAppExecutorService().submit {
            try {
                val projectBasePath = project.basePath
                val projectRoot = projectBasePath?.let { LocalFileSystem.getInstance().findFileByPath(it) }

                if (projectRoot == null) {
                    logger.error("No project root found")
                    return@submit
                }

                val scannedFiles = mutableSetOf<String>()

                // Initialize file hash cache before scanning
                runBlocking {
                    logger.info("Initializing file hash cache...")
                    initializeFileHashCache()
                }

                logger.info("Starting initial codebase scan ${project.name}...")
                isScanningCodebase = true

                // Second pass: process files
                scanFilesByDir(projectRoot, scannedFiles)

                totalFilesChanged(scannedFiles.size)

                // 检测并处理删除的文件
                detectAndHandleDeletedFiles(scannedFiles)
            } catch (e: Exception) {
                logger.error("Error during initial project scan for ${project.name}", e)
            } finally {
                isScanningCodebase = false
                startSyncing()
            }
        }
    }

    /**
     * 扫描指定根目录下的Java文件
     */
    fun scanFilesByDir(
        rootDirectory: VirtualFile,
        scannedFiles: MutableSet<String>
    ) {
        logger.info("Scanning directory: ${rootDirectory.path}")

        val directoriesToScan = Stack<VirtualFile>()
        directoriesToScan.push(rootDirectory)
        val scannedPaths = mutableSetOf<String>()
        var processedFilesCount = 0

        while (directoriesToScan.isNotEmpty()) {
            if (!isScanningCodebase) {
                return
            }

            val currentDirectory = directoriesToScan.pop()

            if (!scannedPaths.add(currentDirectory.path)) {
                continue
            }

            currentDirectory.children.forEach { child ->
                when {
                    child.isDirectory -> directoriesToScan.push(child)
                    child.extension == "java" -> {
                        logger.info("Scanning file: ${child.name}")
                        val relativePath = CodeBaseUtils.calculateRelativePath(
                            project.basePath ?: "",
                            child.path
                        )
                        scannedFiles.add(relativePath)

                        try {
                            // 直接读取文件内容
                            val content = String(child.contentsToByteArray())
                            val fileHash = CodeBaseUtils.calculateContentHash(content)

                            val updateType = determineFileUpdateOrAdd(relativePath, fileHash)
                            if (updateType != null) {
                                updateFileIndex(relativePath, updateType, fileHash)
                                processedFilesCount++
                            }
                        } catch (e: Exception) {
                            logger.error("Error processing file ${child.path}", e)
                        }
                    }
                }
            }
        }
    }

    private fun isFilePathValid(path: String): Boolean {

        if (path.indexOf("/.idea/") >= 0 || path.indexOf("/.git/") >= 0) {
            // 忽略.idea和.git目录下的文件
            return false
        }

        // /service/base/**.java 文件不需要索引
        val regex = Regex(""".*/service/base/[^/]+\.java$""")
        return !regex.matches(path)
    }

    private fun updateFileIndex(
        relativePath: String,
        updateType: FileUpdateType,
        fileHash: String
    ) {
        if (!isFilePathValid(relativePath)) {
            return
        }

        // 更新缓存
        fileHashCacheMap[relativePath] = fileHash

        vectorIndex.updateFileIndex(relativePath, updateType, fileHash)
        bm25Index.updateFileIndex(relativePath, updateType, fileHash)
    }

    /**
     * 检测并处理删除的文件
     */
    private fun detectAndHandleDeletedFiles(scannedFiles: Set<String>) {
        val deletedFiles = fileHashCacheMap.keys.filter { it !in scannedFiles }

        if (deletedFiles.isNotEmpty()) {
            logger.info("Found ${deletedFiles.size} deleted files")
            deletedFiles.forEach { filePath ->
                logger.info("Deleted file: $filePath")
                deleteFileIndex(filePath)
            }
        }
    }

    /**
     * 判断单个文件是新增还是更新
     */
    private fun determineFileUpdateOrAdd(filePath: String, newHash: String): FileUpdateType? {
        val oldHash = fileHashCacheMap[filePath]
        return when {
            oldHash == null -> FileUpdateType.ADD
            oldHash != newHash -> FileUpdateType.UPDATE
            else -> null
        }
    }

    private fun deleteFileIndex(filePath: String) {
        if (!isFilePathValid(filePath)) {
            return
        }
        // 从缓存中移除
        fileHashCacheMap.remove(filePath)
        vectorIndex.deleteFileIndex(filePath)
        bm25Index.deleteFileIndex(filePath)
    }

    /**
     * 通知开始scanning
     */
    private fun startSyncing() {
        parallelScope.launch {
            launch { vectorIndex.startSyncing() }
            launch { bm25Index.startSyncing() }
        }
    }

    /**
     * 清理所有索引
     */
    fun clearIndex() {
        try {
            isScanningCodebase = false

            fileHashCacheMap.clear()

            bm25Index.clearIndex()
            vectorIndex.clearIndex()

        } catch (e: Exception) {
            logger.error("Error clearing indices", e)
        }
    }

    /**
     * 启动定期同步任务
     */
    private fun startPeriodicSyncFileHashCache() {
        periodicSyncJob = coroutineScope.launch {
            while (isActive && !project.isDisposed) {
                try {
                    // 等待项目加载完成
                    DumbService.getInstance(project).waitForSmartMode()

                    // 计算到下一个同步时间点的延迟
                    val delayMillis = calculateDelayToNextSync()
                    logger.info("Next sync scheduled in ${delayMillis / 1000 / 60} minutes")

                    // 等待到下一个同步时间点
                    delay(delayMillis)

                    // 执行同步
                    logger.info("Starting scheduled file hash sync")
                    initializeFileHashCache()
                    logger.info("Completed scheduled file hash sync")
                } catch (e: Exception) {
                    logger.error("Error during scheduled file hash sync", e)
                    // 如果发生错误，等待1小时后重试
                    delay(3600000)
                }
            }
        }
    }

    /**
     * 计算到下一个同步时间点的延迟（毫秒）
     */
    private fun calculateDelayToNextSync(): Long {
        val now = LocalDateTime.now()
        val currentTime = now.toLocalTime()

        // 找到下一个同步时间点
        val nextSyncTime = SYNC_TIMES
            .map { syncTime ->
                var nextTime = LocalDateTime.of(now.toLocalDate(), syncTime)
                // 如果时间已经过了，设置为明天
                if (currentTime.isAfter(syncTime)) {
                    nextTime = nextTime.plusDays(1)
                }
                nextTime
            }
            .minByOrNull { it.toInstant(java.time.ZoneOffset.UTC).toEpochMilli() }
            ?: throw IllegalStateException("No sync times configured")

        return ChronoUnit.MILLIS.between(now, nextSyncTime)
    }

    /**
     * 初始化文件哈希缓存
     */
    private suspend fun initializeFileHashCache() {
        withContext(Dispatchers.IO) {
            try {
                fileHashCacheMap.clear()

                val host = applicationSettings.host
                val path = "/api/ai/rag/code/get-file-path-and-hash"
                val cookies = applicationSettings.cookies

                val requestBody = JSONObject().apply {
                    put("userId", applicationSettings.userId)
                    put("mac", applicationSettings.deviceId)
                    put("projectBasePath", project.basePath)
                    put("tocoProjectId", projectSettings.projectId)
                }.toString()

                logger.info("Fetching file hashes from server")

                val response = HttpUtil.post(host + path, HttpUtil.parseCookies(cookies), requestBody)
                val jsonResponse = JSONObject(response)

                if (jsonResponse.getInt("code") == 200) {
                    val data = jsonResponse.getJSONArray("data")
                    val hashMap = mutableMapOf<String, String>()

                    for (i in 0 until data.length()) {
                        val item = data.getJSONObject(i)
                        val filePath = item.getString("filePath")
                        val contentHash = item.getString("contentHash")
                        hashMap[filePath] = contentHash
                    }

                    withContext(Dispatchers.Default) {
                        fileHashCacheMap.putAll(hashMap)
                    }
                    logger.info("Successfully initialized fileHashCacheMap with ${hashMap.size} entries")
                } else {
                    logger.error("Failed to initialize fileHashCacheMap. Response: $response")
                }
            } catch (e: Exception) {
                logger.error("Error initializing fileHashCacheMap", e)
            }
        }
    }

    /**
     * 释放资源
     */
    fun dispose() {
        try {
            coroutineScope.cancel() // Cancel all coroutines
            parallelScope.cancel() // Cancel parallel operations
            if (periodicSyncJob != null && !periodicSyncJob!!.isCancelled) {
                periodicSyncJob?.cancel() // Explicitly cancel the periodic sync job
            }
            fileChangeListenerConnection?.dispose() // Dispose the file change listener connection
            fileHashCacheMap.clear()
            vectorIndex.dispose()
            bm25Index.dispose()
        } catch (e: Exception) {
            logger.warn("Error disposing CodeBaseSearchManager", e)
        }
    }

    /**
     * 执行搜索
     */
    fun search(query: String): List<CodeBaseIndex.SearchResult> {
        // 执行向量搜索
        val vectorResults = vectorIndex.search(query)

        // 执行BM25搜索
        val bm25Results = bm25Index.search(query)

        // 去重 vectorResults + bm25Results
        val uniqueMap = LinkedHashMap<String, CodeBaseIndex.SearchResult>()
        (vectorResults + bm25Results).forEach { result ->
            val key = "${result.filePath}:${result.start}:${result.end}"
            if (!uniqueMap.containsKey(key)) {
                uniqueMap[key] = result
            }
        }
        val dedupedResults = uniqueMap.values.toList()

        // 执行llm重排序
        val res = rerankResults(query, dedupedResults)
        return res
    }

    /**
     * 对搜索结果进行重排序
     * @param query 搜索查询
     * @param results 需要重排序的结果列表
     * @return 重排序后的结果列表
     */
    private fun rerankResults(
        query: String,
        results: List<CodeBaseIndex.SearchResult>
    ): List<CodeBaseIndex.SearchResult> {
        try {
            val host = applicationSettings.host
            val path = "/lm/api/rerank"
            val cookies = applicationSettings.cookies

            // 准备重排序请求数据
            val requestBody = JSONObject().apply {
                put("query", query)
                put("top_n", results.size)
                put("documents", JSONArray().apply {
                    results.forEach { result ->
                        put(result.raw)
                    }
                })
            }.toString()

            // 发送重排序请求
            val response = HttpUtil.post(host + path, HttpUtil.parseCookies(cookies), requestBody)
            val jsonResponse = JSONObject(response)

            if (jsonResponse.getInt("code") != 200) {
                logger.warn("Failed to perform reranking. Response: $response")
                return results.take(20)
            }

            // 获取重排序后的结果
            val rerankedData = jsonResponse.getJSONArray("data")

            // 创建索引到分数的映射
            val indexToScore = mutableMapOf<Int, Float>()
            for (i in 0 until rerankedData.length()) {
                val item = rerankedData.getJSONObject(i)
                val index = item.getInt("index")
                val score = item.getDouble("relevance_score").toFloat()
                indexToScore[index] = score
            }

            // 根据重排序分数重新排序结果
            val rerankResults = results.mapIndexed { index, result ->
                val newScore = indexToScore[index] ?: 0f
                result.copy(score = newScore)
            }.sortedByDescending { it.score }
                .take(20)

            return rerankResults
        } catch (e: Exception) {
            logger.error("Error performing reranking", e)
            return results.take(20)
        }
    }

    private fun totalFilesChanged(count: Int) {
        totalFiles = count
        project.messageBus.syncPublisher(CodeBaseSyncStatusListener.TOPIC)
            .totalFilesChanged(count)
    }

    fun getTotalFiles(): Int {
        return totalFiles
    }

    private fun processPendingEvents(pendingEvents: java.util.concurrent.ConcurrentLinkedQueue<PendingFileEvent>) {
        while (pendingEvents.isNotEmpty()) {
            when (val evt = pendingEvents.poll()) {
                is PendingFileEvent.Delete -> {
                    deleteFileIndex(evt.relativePath)
                }

                is PendingFileEvent.AddOrUpdate -> {
                    val absPath = project.basePath?.let { it + "/" + evt.relativePath }
                    val vFile = absPath?.let { LocalFileSystem.getInstance().findFileByPath(it) }
                    if (vFile != null && vFile.exists()) {
                        try {
                            val content = String(vFile.contentsToByteArray())
                            val fileHash = CodeBaseUtils.calculateContentHash(content)
                            updateFileIndex(
                                evt.relativePath,
                                evt.updateType,
                                fileHash
                            )
                        } catch (e: Exception) {
                            logger.warn("[VFS] Error updating file: ${evt.relativePath}", e)
                        }
                    }
                }

                null -> {}
            }
        }
    }
}