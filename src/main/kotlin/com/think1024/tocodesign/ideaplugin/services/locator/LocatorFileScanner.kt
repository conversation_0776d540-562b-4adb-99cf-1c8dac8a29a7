package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiJavaFile
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction
import java.util.concurrent.ConcurrentHashMap

/**
 * Service class responsible for scanning Java files and indexing their contents.
 * Utilizes caching to avoid redundant scans and optimizes index updates.
 *
 * @param project The project to which this scanner is attached.
 */
class LocatorFileScanner(private val project: Project) {
    private val logger = Logger.getInstance(LocatorFileScanner::class.java)

    private val locatorBuilder = LocatorBuilder(project)
    private val indexManager = LocatorIndexManager.getInstance(project)
    private val scannedFilesCache = ConcurrentHashMap<String, Long>()

    /**
     * Scans a given PsiFile if it is a Java file and indexes its contents.
     * Caches the file's modification stamp to prevent redundant scanning.
     *
     * @param psiFile The PsiFile to be scanned.
     */
    fun scanFile(psiFile: PsiFile) {
        // Return early if the file is not a Java file
        if (psiFile !is PsiJavaFile) return

        val virtualFile = psiFile.virtualFile
        if (!virtualFile.isValid) {
            logger.warn("The virtual file is not valid: $virtualFile")
            return
        }

        val filePath = virtualFile.path

        // Skip scanning if the file has not been modified since the last scan
        val currentStamp = virtualFile.modificationStamp
        if (scannedFilesCache[filePath] == currentStamp) return

        // Check if the file length matches expected length
        if (virtualFile.length != psiFile.textLength.toLong()) {
//            logger.warn("File length mismatch for $filePath. Expected: ${psiFile.textLength}, Actual: ${virtualFile.length}")
            return
        }

        // Map to collect locator indices for batch update
        val locatorIndices = mutableMapOf<String, String>()

        // Perform the indexing within a read action
        try {
            runReadAction {
                // Check if the file is still valid before scanning
                if (!virtualFile.isValid || virtualFile.modificationStamp != currentStamp) {
                    logger.warn("The virtual file is no longer valid or has been modified: $filePath")
                    return@runReadAction
                }

                // Iterate over all classes and methods in the Java file to index them
                psiFile.classes.forEach { psiClass ->
                    try {
                        scanElement(psiClass, filePath, locatorIndices)
                        psiClass.methods.forEach { method ->
                            scanElement(method, filePath, locatorIndices)
                        }
                    } catch (e: Exception) {
                        logger.warn("Error scanning class or methods in file $filePath", e)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Error scanning file $filePath", e)
            return // Return early if an error occurs
        }

        // Batch update indices in the index manager if any indices were collected
        try {
            if (locatorIndices.isNotEmpty()) {
                indexManager.addOrUpdateIndexes(locatorIndices)
                logger.info("Batch added/updated indices for file $filePath")
            }
        } catch (e: Exception) {
            logger.warn("Error updating indices for file $filePath", e)
        }

        // Update cache with the current modification stamp
        scannedFilesCache[filePath] = currentStamp
    }

    /**
     * Scans a given PsiElement to build and collect the locator index.
     * Supports batch updates to the index manager for better performance.
     *
     * @param element The PsiElement to be scanned.
     * @param filePath The path of the file containing the element.
     * @param locatorIndices The map to collect locator indices for batch update.
     */
    private fun scanElement(element: PsiElement, filePath: String, locatorIndices: MutableMap<String, String>) {
        try {
            // Build locator index from the PsiElement
            val locatorIndex = locatorBuilder.buildFromPsiElement(element)
            if (locatorIndex != null) {
                // Map the search key to the file path
                locatorIndices[locatorIndex.searchKey] = filePath
                logger.info("Prepared index for ${locatorIndex.searchKey} -> $filePath")
            }
        } catch (e: Exception) {
            logger.warn("Error scanning element in file $filePath: ${element.text}", e)
            // Optional: You might want to handle the error further (e.g., skip this element)
        }
    }
}
