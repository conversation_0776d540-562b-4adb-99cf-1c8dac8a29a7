package com.think1024.tocodesign.ideaplugin.services.codebase

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiJavaCodeReferenceElement
import com.intellij.psi.PsiJavaFile
import com.intellij.psi.PsiManager
import com.intellij.psi.PsiMethod
import com.intellij.psi.PsiReferenceParameterList
import com.intellij.psi.PsiTypeElement
import com.intellij.psi.PsiModifierListOwner

class CodebaseChunkBuilder(private val project: Project) {
    private val logger = Logger.getInstance(CodebaseChunkBuilder::class.java)

    companion object {
        private const val MAX_RAW_CONTENT_LENGTH = 4096
        private const val OVERLAP_SIZE = 512 // 重叠大小，保持上下文连续性
        private const val MIN_CHUNK_SIZE = 256 // 最小chunk大小，避免过小的chunk
    }

    /**
     * 文件内容块数据类
     *
     * @property position 内容块在文件中的位置
     * @property raw 原始内容，包含类接口信息、注释或方法代码
     */
    data class ContentBlock(
        val position: Position,
        val header: String = "", // 头部注释（文件路径、所属类、chunk type等信息）
        val raw: String
    ) {
        fun toMap(): Map<String, Any?> {
            return mapOf(
                "position" to mapOf(
                    "start" to position.start,
                    "end" to position.end
                ),
                "header" to header,
                "raw" to raw
            )
        }
    }

    /**
     * 位置数据类，表示内容块在文件中的位置
     *
     * @property start 开始行号
     * @property end 结束行号
     */
    data class Position(
        val start: Int,
        val end: Int
    )

    // 获取所有类和方法的边界及其信息
    data class ElementInfo(
        val startOffset: Int,
        val endOffset: Int,
        val type: String,
        val signature: String,
        val name: String,
        val className: String? = null,
        val isComplete: Boolean = true,
        val isAutoGenerated: Boolean = false
    )

    /**
     * 从文件中提取内容块
     *
     * @param relativePath 相对于工程根目录的文件路径
     * @return 内容块列表
     */
    internal fun extractContentBlocks(relativePath: String): List<ContentBlock> {
        return com.intellij.openapi.application.ApplicationManager.getApplication().runReadAction<List<ContentBlock>> {
            val absolutePath = project.basePath + "/" + relativePath
            val virtualFile = LocalFileSystem.getInstance().findFileByPath(absolutePath)
            val file: PsiFile? = if (virtualFile != null && virtualFile.exists()) {
                PsiManager.getInstance(project).findFile(virtualFile)
            } else {
                null
            }

            if (file !is PsiJavaFile) return@runReadAction emptyList<ContentBlock>()

            val document = file.viewProvider.document ?: return@runReadAction emptyList<ContentBlock>()
            val fileText = document.text

            // 提取import语句
            val imports = extractImports(file)

            // 收集所有代码元素信息
            val elementInfos = collectElementInfos(file)

            if (elementInfos.isEmpty()) {
                return@runReadAction listOf(createInitialBlock(document, fileText, relativePath))
            }

            // 使用语义感知chunking策略
            val chunks = createSemanticChunks(elementInfos, fileText, document, relativePath, imports)
            return@runReadAction chunks
        }
    }

    /**
     * 提取import语句
     */
    private fun extractImports(file: PsiJavaFile): List<String> {
        return file.importList?.importStatements?.map { it.importReference?.qualifiedName ?: "" } ?: emptyList()
    }

    /**
     * 元素信息收集
     */
    private fun collectElementInfos(file: PsiJavaFile): List<ElementInfo> {
        val elementInfos = mutableListOf<ElementInfo>()

        file.accept(object : com.intellij.psi.PsiRecursiveElementVisitor() {
            override fun visitElement(element: PsiElement) {
                when (element) {
                    is PsiClass -> {
                        if (isActualClassDefinition(element)) {
                            elementInfos.add(
                                ElementInfo(
                                    startOffset = element.textRange.startOffset,
                                    endOffset = element.textRange.endOffset,
                                    type = "class",
                                    signature = buildClassSignature(element),
                                    name = element.name ?: "",
                                    className = element.name,
                                    isComplete = true
                                )
                            )
                        }
                    }

                    is PsiMethod -> {
                        elementInfos.add(
                            ElementInfo(
                                startOffset = element.textRange.startOffset,
                                endOffset = element.textRange.endOffset,
                                type = "method",
                                signature = buildMethodSignature(element),
                                name = element.name,
                                className = element.containingClass?.name,
                                isComplete = true,
                                isAutoGenerated = isAutoGenerated(element)
                            )
                        )
                    }
                }
                super.visitElement(element)
            }
        })

        return elementInfos.sortedBy { it.startOffset }
    }

    /**
     * 语义感知的chunk创建
     */
    private fun createSemanticChunks(
        elementInfos: List<ElementInfo>,
        fileText: String,
        document: com.intellij.openapi.editor.Document,
        relativePath: String,
        imports: List<String>
    ): List<ContentBlock> {
        val chunks = mutableListOf<ContentBlock>()

        // 1. 处理import语句 - 单独作为一个chunk
        val importEndOffset = findImportEndOffset(fileText)
        if (importEndOffset > 0) {
            val importChunk = createImportChunk(
                document, fileText, relativePath, imports, 0, importEndOffset
            )
            chunks.add(importChunk)
        }

        // 2. 按语义边界创建chunks
        val semanticChunks = createChunksBySemanticBoundaries(
            elementInfos, fileText, document, relativePath
        )
        chunks.addAll(semanticChunks)

        // 3. 如果还有剩余内容，创建混合内容chunk
        val lastElementEnd = elementInfos.lastOrNull()?.endOffset ?: importEndOffset
        if (lastElementEnd < fileText.length - 1) {
            val remaining = fileText.substring(lastElementEnd + 1)
            // 如果剩余内容包含任何字母或数字，则认为其有意义
            if (remaining.trim().any { it.isLetterOrDigit() }) {
                val remainingChunk = createMixedContentChunk(
                    document, fileText, relativePath, lastElementEnd + 1, fileText.length - 1
                )
                chunks.add(remainingChunk)
            }
        }

        return chunks
    }

    /**
     * 查找import语句结束位置
     */
    private fun findImportEndOffset(fileText: String): Int {
        val importRegex = Regex("^import\\s+.*", RegexOption.MULTILINE)
        var lastMatchEnd = -1
        importRegex.findAll(fileText).forEach { matchResult ->
            lastMatchEnd = matchResult.range.last
        }
        return if (lastMatchEnd >= 0) lastMatchEnd else 0
    }

    /**
     * 按语义边界创建chunks
     */
    private fun createChunksBySemanticBoundaries(
        elementInfos: List<ElementInfo>,
        fileText: String,
        document: com.intellij.openapi.editor.Document,
        relativePath: String,
    ): List<ContentBlock> {
        val chunks = mutableListOf<ContentBlock>()
        var i = 0
        while (i < elementInfos.size) {
            val currentElement = elementInfos[i]
            if (currentElement.isAutoGenerated) {
                i++
                continue
            }

            // 尝试合并方法
            if (currentElement.type == "method") {
                val methodsToMerge = mutableListOf(currentElement)
                val initialElementEndOffset = getElementEndOffset(i, elementInfos, fileText.length)
                val initialSize = initialElementEndOffset - currentElement.startOffset + 1

                if (initialSize <= MAX_RAW_CONTENT_LENGTH) {
                    var j = i + 1
                    while (j < elementInfos.size) {
                        val nextElement = elementInfos[j]
                        if (nextElement.type == "method" && nextElement.className == currentElement.className && !nextElement.isAutoGenerated) {
                            val potentialEndOffset = getElementEndOffset(j, elementInfos, fileText.length)
                            val newTotalSize = potentialEndOffset - currentElement.startOffset + 1

                            if (newTotalSize <= MAX_RAW_CONTENT_LENGTH) {
                                methodsToMerge.add(nextElement)
                                j++
                            } else {
                                break
                            }
                        } else {
                            break
                        }
                    }
                }

                if (methodsToMerge.size > 1) {
                    val groupEndIndex = i + methodsToMerge.size - 1
                    val startOffset = methodsToMerge.first().startOffset
                    val endOffset = getElementEndOffset(groupEndIndex, elementInfos, fileText.length)
                    chunks.add(
                        createMergedMethodChunk(
                            document, fileText, relativePath, methodsToMerge, startOffset, endOffset
                        )
                    )
                    i += methodsToMerge.size
                    continue
                }
            }

            // 如果没有合并，则正常处理单个元素
            val elementEndOffset = getElementEndOffset(i, elementInfos, fileText.length)
            val elementSize = elementEndOffset - currentElement.startOffset + 1

            if (elementSize <= MAX_RAW_CONTENT_LENGTH) {
                val chunk = createElementChunk(
                    document, fileText, relativePath, currentElement,
                    currentElement.startOffset, elementEndOffset
                )
                chunks.add(chunk)
            } else {
                val splitChunks = splitLargeElement(
                    document, fileText, relativePath, currentElement,
                    currentElement.startOffset, elementEndOffset
                )
                chunks.addAll(splitChunks)
            }
            i++
        }

        return chunks
    }

    /**
     * 创建import chunk
     */
    private fun createImportChunk(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
        imports: List<String>,
        startOffset: Int,
        endOffset: Int
    ): ContentBlock {
        val header = buildString {
            append("// File: $relativePath\n")
            append("// Chunk Type: IMPORTS\n")
        }
        val content = buildString {
            append(header)
            append("\n")
            append(fileText.substring(startOffset, endOffset + 1))
        }

        return ContentBlock(
            position = Position(
                document.getLineNumber(startOffset) + 1,
                document.getLineNumber(endOffset) + 1
            ),
            header = header,
            raw = content
        )
    }

    /**
     * 创建元素chunk
     */
    private fun createElementChunk(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
        elementInfo: ElementInfo,
        startOffset: Int,
        endOffset: Int
    ): ContentBlock {
        val header = buildString {
            append("// File: $relativePath\n")
            if (elementInfo.type == "method" && elementInfo.className != null) {
                append("// Class: ${elementInfo.className}\n")
            }
            append("// Chunk Type: ${elementInfo.type.uppercase()}\n")
        }
        val content = buildString {
            append(header)
            append("\n")
            append(fileText.substring(startOffset, endOffset + 1))
        }

        return ContentBlock(
            position = Position(
                document.getLineNumber(startOffset) + 1,
                document.getLineNumber(endOffset) + 1
            ),
            header = header,
            raw = content
        )
    }

    /**
     * 创建合并后的小方法chunk
     */
    private fun createMergedMethodChunk(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
        elementInfos: List<ElementInfo>,
        startOffset: Int,
        endOffset: Int
    ): ContentBlock {
        val firstElement = elementInfos.first()
        val methodNames = elementInfos.map { it.name }
        val header = buildString {
            append("// File: $relativePath\n")
            if (firstElement.className != null) {
                append("// Class: ${firstElement.className}\n")
            }
            append("// Chunk Type: MERGED_METHODS\n")
            append("// Methods: ${methodNames.joinToString(", ")}\n")
        }
        val content = buildString {
            append(header)
            append("\n")
            append(fileText.substring(startOffset, endOffset + 1))
        }

        return ContentBlock(
            position = Position(
                document.getLineNumber(startOffset) + 1,
                document.getLineNumber(endOffset) + 1
            ),
            header = header,
            raw = content
        )
    }

    /**
     * 分割大元素
     */
    private fun splitLargeElement(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
        elementInfo: ElementInfo,
        startOffset: Int,
        endOffset: Int
    ): List<ContentBlock> {
        val chunks = mutableListOf<ContentBlock>()
        var currentOffset = startOffset

        while (currentOffset < endOffset) {
            var chunkEndOffset = minOf(currentOffset + MAX_RAW_CONTENT_LENGTH, endOffset)

            // 避免创建小于MIN_CHUNK_SIZE的微小末尾块
            val remainingLength = endOffset - currentOffset
            if (remainingLength > MAX_RAW_CONTENT_LENGTH && endOffset - chunkEndOffset < MIN_CHUNK_SIZE) {
                chunkEndOffset -= (MIN_CHUNK_SIZE - (endOffset - chunkEndOffset))
            }

            // 尝试在语义边界处分割
            val adjustedEndOffset = findSemanticBoundary(fileText, currentOffset, chunkEndOffset, endOffset)

            val header = buildString {
                append("// File: $relativePath\n")
                if (elementInfo.type == "method" && elementInfo.className != null) {
                    append("// Class: ${elementInfo.className}\n")
                }
                append("// Chunk Type: ${elementInfo.type.uppercase()}_PART\n")
                append("// Part: ${document.getLineNumber(currentOffset) + 1}-${document.getLineNumber(adjustedEndOffset) + 1}\n")
            }
            val content = buildString {
                append(header)
                append("\n")
                append(fileText.substring(currentOffset, adjustedEndOffset + 1))
            }

            chunks.add(
                ContentBlock(
                    position = Position(
                        document.getLineNumber(currentOffset) + 1,
                        document.getLineNumber(adjustedEndOffset) + 1
                    ),
                    header = header,
                    raw = content
                )
            )

            if (adjustedEndOffset >= endOffset) {
                break
            }

            currentOffset = maxOf(currentOffset + 1, adjustedEndOffset - OVERLAP_SIZE + 1)
        }

        return chunks
    }

    /**
     * 查找语义边界
     */
    private fun findSemanticBoundary(
        fileText: String,
        startOffset: Int,
        preferredEndOffset: Int,
        maxEndOffset: Int
    ): Int {
        // 优先在行边界处分割
        val preferredLineEnd = findLineEnd(fileText, preferredEndOffset)
        if (preferredLineEnd <= maxEndOffset) {
            return preferredLineEnd
        }

        // 如果不行，在句子边界处分割
        val sentenceEnd = findSentenceEnd(fileText, preferredEndOffset, maxEndOffset)
        if (sentenceEnd > startOffset) {
            return sentenceEnd
        }

        return minOf(preferredEndOffset, maxEndOffset)
    }

    /**
     * 获取元素的结束位置
     */
    private fun getElementEndOffset(elementIndex: Int, elementInfos: List<ElementInfo>, fileTextLength: Int): Int {
        return elementInfos.getOrNull(elementIndex + 1)?.startOffset?.minus(1) ?: (fileTextLength - 1)
    }

    /**
     * 查找行结束位置
     */
    private fun findLineEnd(fileText: String, offset: Int): Int {
        val nextNewline = fileText.indexOf('\n', offset)
        return if (nextNewline >= 0) nextNewline else offset
    }

    /**
     * 查找句子结束位置
     */
    private fun findSentenceEnd(fileText: String, startOffset: Int, maxOffset: Int): Int {
        for (i in startOffset downTo startOffset - 100) {
            if (i < 0) break
            val char = fileText[i]
            if (char == ';' || char == '}' || char == '{') {
                return i
            }
        }
        return startOffset
    }

    /**
     * 创建混合内容chunk
     */
    private fun createMixedContentChunk(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
        startOffset: Int,
        endOffset: Int
    ): ContentBlock {
        val header = buildString {
            append("// File: $relativePath\n")
            append("// Chunk Type: MIXED_CONTENT\n")
        }
        val content = buildString {
            append(header)
            append("\n")
            append(fileText.substring(startOffset, endOffset + 1))
        }

        return ContentBlock(
            position = Position(
                document.getLineNumber(startOffset) + 1,
                document.getLineNumber(endOffset) + 1
            ),
            header = header,
            raw = content
        )
    }

    private fun createInitialBlock(
        document: com.intellij.openapi.editor.Document,
        fileText: String,
        relativePath: String,
    ): ContentBlock {
        val header = buildString {
            append("// File: $relativePath\n")
            append("// Chunk Type: UNKNOWN\n")
            append("\n")
        }
        val content = buildString {
            append(header)
            append("\n")
            append(fileText)
        }

        return ContentBlock(
            position = Position(1, document.lineCount),
            header = header,
            raw = content
        )
    }

    private fun buildClassSignature(psiClass: PsiClass): String {
        val className = psiClass.name ?: ""
        val extendsList = psiClass.extendsList?.referencedTypes?.joinToString(", ") { it.name } ?: ""
        val implementsList = psiClass.implementsList?.referencedTypes?.joinToString(", ") { it.name } ?: ""

        return buildString {
            when {
                psiClass.isInterface -> append("interface ")
                psiClass.isEnum -> append("enum class ")
                else -> append("class ")
            }
            append(className)
            if (extendsList.isNotEmpty()) append(" extends $extendsList")
            if (implementsList.isNotEmpty()) append(" implements $implementsList")
        }
    }

    private fun buildMethodSignature(psiMethod: PsiMethod): String {
        val modifiers = psiMethod.modifierList.text
        val returnType = psiMethod.returnType?.presentableText ?: "void"
        val methodName = psiMethod.name
        val parameters = psiMethod.parameterList.parameters.joinToString(", ") {
            "${it.type.presentableText} ${it.name}"
        }

        return "$modifiers $returnType $methodName($parameters)"
    }

    /**
     * 判断PsiClass是否是真正的类定义，而非类型引用中的Class
     */
    private fun isActualClassDefinition(psiClass: PsiClass): Boolean {
        if (psiClass is com.intellij.psi.PsiTypeParameter) {
            return false
        }

        val parent = psiClass.parent
        if (parent is PsiJavaCodeReferenceElement ||
            parent is PsiTypeElement ||
            parent is PsiReferenceParameterList
        ) {
            return false
        }

        return psiClass.nameIdentifier != null && psiClass.isPhysical
    }

    // 判断是否有@AutoGenerated注解
    private fun isAutoGenerated(element: PsiModifierListOwner): Boolean {
        return element.modifierList?.annotations?.any {
            it.qualifiedName == "com.vs.code.AutoGenerated" || it.nameReferenceElement?.text == "AutoGenerated"
        } == true
    }
}
