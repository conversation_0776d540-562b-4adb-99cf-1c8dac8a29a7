package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiJavaFile
import com.intellij.psi.PsiMethod
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction

/**
 * Implementation of ILocatorRuler for handling method-level AutoGenerated annotations.
 * This class provides functionality to locate and navigate to specific methods in Java files
 * based on AutoGenerated annotations.
 *
 * @property project The IntelliJ IDEA project context.
 */
class MethodLocatorRuler(override val project: Project) : ILocatorRuler {
    /**
     * Logger instance for this class.
     */
    override val logger = Logger.getInstance(MethodLocatorRuler::class.java)

    /**
     * Mapping of type to corresponding file path.
     * This mapping is used to determine the directory structure for different types of files.
     * The key represents the type, and the value is a string containing submodule path and file path separated by '|'.
     */
    override val typeToPathMapping = mapOf(
        "rpc" to "service|service", // RPC
        "bto" to "service|service", // BTO Service method
        "api" to "entrance.web|entrance.web.controller", // API
        "bo" to "manager|manager.bo", // BO method
        "dmo" to "service|service.mq.consumer", // Domain message consumer method
        "flow" to "service|service" // flow: service method
    )

    /**
     * Matches the PsiFile based on the provided id and type.
     * This method checks all methods in the file with an AutoGenerated annotation
     * and returns a LocatorResult if any method matches the given id and type.
     *
     * @param psiFile The PSI file to check.
     * @param locatorInfo The LocatorInfo to match against.
     * @return LocatorResult if the PsiFile contains a matching method, null otherwise.
     */
    override fun matches(psiFile: PsiFile, locatorInfo: LocatorBuilder.LocatorInfo): ILocatorRuler.LocatorResult? {
        // Ensure the file is a Java file
        val psiClass = runReadAction {
            (psiFile as? PsiJavaFile)?.classes?.firstOrNull()
        } ?: return null

        // Iterate through all methods in the class
        for (method in psiClass.methods) {
            val isMatch = runReadAction {
                matchesMethod(method, locatorInfo)
            }

            // Check if the current method matches the locator information
            if (isMatch) {
                // If a match is found, return a LocatorResult with the file, class, and matching method
                return ILocatorRuler.LocatorResult(psiFile.virtualFile, psiClass, method)
            }
        }

        // If no matching method is found, return null
        return null
    }

    /**
     * Checks if a PsiMethod matches the given locator information.
     * This method performs the following checks:
     * 1. Verifies if the PsiMethod has an AutoGenerated annotation
     * 2. Extracts UUID information from the annotation
     * 3. Compares the extracted information with the provided locator info
     *
     * @param method The PsiMethod to check.
     * @param locatorInfo The LocatorInfo to match against.
     * @return True if the PsiMethod matches the given locator info, false otherwise.
     */
    private fun matchesMethod(method: PsiMethod, locatorInfo: LocatorBuilder.LocatorInfo): Boolean {
        // Find the AutoGenerated annotation on the method
        val annotation = runReadAction {
            method.annotations.find { it.qualifiedName == locatorMethodAnnotation || it.qualifiedName == defaultAnnotation }
        } ?: return false

        // Extract the UUID information from the annotation outside of read action
        val locatorIndex = extractUuidInfo(annotation)

        // Perform the actual matching
        return locatorIndex.searchKey.equals(locatorInfo.searchKey, ignoreCase = true)
    }

    /**
     * Opens the target file and navigates to the target method.
     * This method opens the file in the editor, locates the method with the AutoGenerated annotation,
     * and positions the cursor at the beginning of that method's name, scrolling it to the center of the window.
     *
     * @param locatorResult The LocatorResult containing the target file and associated PSI elements.
     * @return True if the file was successfully opened and the navigation was performed, false otherwise.
     */
    override fun openAndNavigateToTarget(locatorResult: ILocatorRuler.LocatorResult): Boolean {
        val editor = openFileInEditor(locatorResult.virtualFile) ?: return false

        locatorResult.psiMethod?.let { method ->
            val position = calculateMethodPosition(method, editor)
            navigateToPosition(editor, position)
            return true
        }

        return false
    }

    /**
     * Opens the specified file in the editor.
     *
     * @param virtualFile The virtual file to open.
     * @return The Editor instance if successful, null otherwise.
     */
    private fun openFileInEditor(virtualFile: VirtualFile): Editor? {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val editors = fileEditorManager.openFile(virtualFile, true)
        return (editors.find { it is TextEditor } as? TextEditor)?.editor
    }

    /**
     * Calculates the logical position of the method name.
     *
     * @param method The PsiMethod for which to calculate the position.
     * @param editor The Editor instance.
     * @return The LogicalPosition of the method name.
     */
    private fun calculateMethodPosition(method: PsiMethod, editor: Editor): LogicalPosition {
        val methodNameOffset = getMethodNameOffset(method)
        val document = editor.document
        val methodLineNumber = document.getLineNumber(methodNameOffset)
        val methodColumnNumber = methodNameOffset - document.getLineStartOffset(methodLineNumber)
        return LogicalPosition(methodLineNumber, methodColumnNumber)
    }

    /**
     * Navigates to the specified position in the editor.
     *
     * @param editor The Editor instance.
     * @param position The LogicalPosition to navigate to.
     */
    private fun navigateToPosition(editor: Editor, position: LogicalPosition) {
        editor.caretModel.moveToLogicalPosition(position)
        editor.scrollingModel.scrollTo(position, ScrollType.CENTER)
    }

    /**
     * Calculates the offset of the method name.
     *
     * @param method The PsiMethod for which to calculate the name offset.
     * @return The calculated offset for the method name.
     */
    private fun getMethodNameOffset(method: PsiMethod): Int {
        val methodName = method.nameIdentifier
        return methodName?.textRange?.startOffset ?: method.textOffset
    }
}
