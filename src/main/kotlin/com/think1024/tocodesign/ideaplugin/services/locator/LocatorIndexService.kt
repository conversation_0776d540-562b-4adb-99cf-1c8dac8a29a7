package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiJavaFile
import com.intellij.psi.PsiManager
import com.intellij.util.concurrency.AppExecutorUtil
import com.intellij.util.messages.MessageBusConnection
import com.think1024.tocodesign.ideaplugin.listeners.ProjectIdChangeListener
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import java.util.*

/**
 * Service class responsible for indexing locator files in a project.
 */
@Service(Service.Level.PROJECT)
class LocatorIndexService(private val project: Project) {

    // Logger instance for logging messages
    private val logger = Logger.getInstance(LocatorIndexService::class.java)

    private val projectSettings = ProjectPluginSettings.getInstance(project)
    private var connection: MessageBusConnection? = null

    // Initialize the service
    init {
        listenToProjectIdChanges()
        if (!projectSettings.projectId.isNullOrEmpty()) {
            scheduleScan()
        }
    }

    /**
     * Listens for changes in the projectId setting.
     */
    private fun listenToProjectIdChanges() {
        // Logic to listen for changes in projectId and trigger scan when it becomes non-empty
        connection = project.messageBus.connect().apply {
            subscribe(ProjectIdChangeListener.TOPIC, object : ProjectIdChangeListener {
                override fun projectIdChanged(newProjectId: String?) {
                    // Handle the change
                    if (!newProjectId.isNullOrEmpty()) {
                        scheduleScan()
                    }
                }
            })
        }
    }

    /**
     * Cancels the listener for projectId changes.
     */
    fun cancelProjectIdChangeListener() {
        try {
            connection?.disconnect()
            logger.info("Disconnected ProjectIdChangeListener for project: ${project.name}")
        } catch (e: Exception) {
            logger.warn("Failed to disconnect ProjectIdChangeListener", e)
        }
    }

    /**
     * Schedules a scan for Java files if projectId is set.
     */
    private fun scheduleScan() {
        logger.info("Scheduling initial project scan for ${project.name}")

        // Execute the scan in a background thread to avoid blocking the UI
        AppExecutorUtil.getAppExecutorService().submit {
            DumbService.getInstance(project).smartInvokeLater {
                performInitialScan()
            }
        }
    }

    /**
     * Performs an initial scan of the project to locate and index Java files.
     * This method runs as a background task to avoid blocking the UI thread.
     *
     * The scan process includes the following steps:
     * 1. Iterate through all content roots of the project.
     * 2. Recursively search for Java files in each content root.
     * 3. Scan all found Java files.
     * 4. Update the progress indicator throughout the process.
     * 5. Handle potential cancellations and exceptions.
     */
    private fun performInitialScan() {
        logger.info("Starting initial project scan for ${project.name}")

        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "Scanning Java Files", true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    // Set up the progress indicator
                    indicator.isIndeterminate = false

                    val scanner = LocatorFileScanner(project)
                    val psiManager = PsiManager.getInstance(project)

                    val roots = ProjectRootManager.getInstance(project).contentRoots
                    val totalRoots = roots.size

                    roots.forEachIndexed { index, root ->
                        // Check for user cancellation
                        if (indicator.isCanceled) {
                            logger.info("Scan cancelled for project: ${project.name}")
                            return
                        }

                        // Update progress for each root
                        indicator.fraction = index.toDouble() / totalRoots
                        indicator.text = "Scanning root ${index + 1} of $totalRoots"

                        // Recursively scan for Java files
                        scanForJavaFiles(root, psiManager, scanner, indicator)
                    }

                    logger.info("Completed initial project scan for ${project.name}")
                } catch (e: Exception) {
                    // Log any unexpected errors during the scan process
                    logger.error("Error during initial project scan for ${project.name}", e)
                } finally {
                    // Ensure progress indicator is set to complete
                    indicator.fraction = 1.0
                    indicator.text = "Scan completed"
                }
            }
        })
    }

    /**
     * Recursively scans a directory for Java files.
     *
     * This method uses a stack-based approach to avoid potential stack overflow issues with deep directory structures.
     * It also includes checks for symbolic links to prevent infinite loops.
     * The method uses ReadAction.compute() to ensure thread-safe access to PSI elements.
     *
     * @param rootDirectory The VirtualFile representing the root directory to scan
     * @param psiManager The PsiManager instance for the project
     * @param scanner The LocatorFileScanner instance used to process found Java files
     * @param indicator The ProgressIndicator to update during the scan
     */
    private fun scanForJavaFiles(
        rootDirectory: VirtualFile,
        psiManager: PsiManager,
        scanner: LocatorFileScanner,
        indicator: ProgressIndicator
    ) {
        val directoriesToScan = Stack<VirtualFile>()
        directoriesToScan.push(rootDirectory)
        val scannedPaths = mutableSetOf<String>()

        while (directoriesToScan.isNotEmpty() && !indicator.isCanceled) {
            val currentDirectory = directoriesToScan.pop()

            // Check if we've already scanned this directory (prevents loops from symbolic links)
            if (!scannedPaths.add(currentDirectory.path)) {
                continue
            }

            // Use ReadAction.compute() to ensure thread-safe access to PSI elements
            ReadAction.compute<Unit, Throwable> {
                currentDirectory.children.forEach { child ->
                    if (indicator.isCanceled) return@compute

                    when {
                        child.isDirectory -> directoriesToScan.push(child)
                        child.extension == "java" -> {
                            indicator.text2 = "Scanning file: ${child.name}"
                            // Access PSI elements within the read action
                            psiManager.findFile(child)?.let { psiFile ->
                                if (psiFile is PsiJavaFile) {
                                    scanner.scanFile(psiFile)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}
