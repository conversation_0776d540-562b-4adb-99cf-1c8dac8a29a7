package com.think1024.tocodesign.ideaplugin.services

import com.intellij.codeInsight.CodeInsightSettings
import com.intellij.codeInsight.actions.OptimizeImportsProcessor
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.compiler.CompilerManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager
import com.think1024.tocodesign.ideaplugin.utils.FIX_COMPILER_FILES_KEY


@Service
class QuickFixCompiler(private val project: Project) {

    fun applyQuickFixToVirtualFiles(virtualFiles: List<VirtualFile>) {
        // 在外层等待智能模式
        DumbService.getInstance(project).runWhenSmart {
            ApplicationManager.getApplication().runReadAction {
                val psiFiles = virtualFiles.mapNotNull { virtualFile ->
                    PsiManager.getInstance(project).findFile(virtualFile)
                }
                optimizeImportsForFiles(project, psiFiles)
            }
        }
    }

    fun applyQuickFixToFiles(fileUrls: List<String>) {
        ApplicationManager.getApplication().invokeLater {
            val virtualFiles = fileUrls.mapNotNull { url ->
                com.intellij.openapi.vfs.VfsUtil.findFileByURL(java.net.URL(url))
            }

            val psiFiles = virtualFiles.mapNotNull { virtualFile ->
                PsiManager.getInstance(project).findFile(virtualFile)
            }

            optimizeImportsForFiles(project, psiFiles)
        }
    }

    private fun waitForIndexing(project: Project) {
        val dumbService = DumbService.getInstance(project)
        if (dumbService.isDumb) {
            dumbService.waitForSmartMode()
        }
    }

    private fun optimizeImportsForFiles(project: Project, files: List<PsiFile>) {
        val codeInsightSettings = CodeInsightSettings.getInstance()
        val originalSetting = codeInsightSettings.ADD_UNAMBIGIOUS_IMPORTS_ON_THE_FLY

        try {
            // ✅ 清空上一次存储的文件路径，防止数据污染
            project.putUserData(FIX_COMPILER_FILES_KEY, null)

            // 将目标文件路径存入项目 UserData
            val targetFilePaths = files.map { it.virtualFile.path }.toSet()
            project.putUserData(FIX_COMPILER_FILES_KEY, targetFilePaths)

            // 临时启用 "Add unambiguous imports on the fly"
            codeInsightSettings.ADD_UNAMBIGIOUS_IMPORTS_ON_THE_FLY = true

            // 确保索引已完成
            waitForIndexing(project)

            // 提交所有文档以确保文件状态
            PsiDocumentManager.getInstance(project).commitAllDocuments()

            // 对每个文件执行 Optimize Imports
            for (file in files) {
                if (file.isValid) {
                    WriteCommandAction.runWriteCommandAction(project) {
                        OptimizeImportsProcessor(project, file).run()
                    }
                }
            }

            // 确保所有文档都已提交
            PsiDocumentManager.getInstance(project).commitAllDocuments()

        } finally {
            // 恢复原始设置
            ApplicationManager.getApplication().executeOnPooledThread {
                Thread.sleep(1_000) // 10秒
                ApplicationManager.getApplication().invokeLater {
                    codeInsightSettings.ADD_UNAMBIGIOUS_IMPORTS_ON_THE_FLY = originalSetting
                    // 执行 Build Project
                    CompilerManager.getInstance(project).rebuild { aborted, errors, warnings, context ->
                        println("✅ Build Project Completed")
                        println("Aborted: $aborted, Errors: $errors, Warnings: $warnings")
                    }
                }
            }
        }
    }
}
