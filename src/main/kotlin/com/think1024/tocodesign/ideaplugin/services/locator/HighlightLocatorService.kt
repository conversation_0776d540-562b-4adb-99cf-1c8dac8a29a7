package com.think1024.tocodesign.ideaplugin.services.locator

import com.google.gson.JsonObject
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.intellij.openapi.editor.markup.EffectType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.OpenFileDescriptor
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ColorUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.awt.Font
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * Service class responsible for handling highlight requests sent from a socket client.
 * It processes messages to locate files within the project, verifies file integrity,
 * and performs line highlighting in the IDE editor. This service runs at the project level,
 * meaning it is bound to a specific IntelliJ project instance.
 *
 * Main responsibilities:
 * - Handle incoming socket messages requesting line highlights.
 * - Locate and open specified project files in the editor.
 * - Verify file content using checksums to ensure consistency.
 * - Highlight specified lines using defined text attributes.
 * - Clear highlights when document content changes.
 * - Send responses back to the client, indicating success or error states.
 *
 * This service makes use of several IntelliJ platform APIs:
 * - FileEditorManager: For opening and managing files in the editor.
 * - VirtualFile and LocalFileSystem: For accessing and verifying files.
 * - Editor and MarkupModel: For applying and managing text highlights.
 * - ApplicationManager and runReadAction: For managing thread execution and UI updates.
 *
 * The service is designed to be responsive and non-blocking, making use of threads
 * and Executors to parallelize line highlighting, ensuring main thread responsiveness.
 *
 * Note: Highlighting and UI updates are executed on the Event Dispatch Thread (EDT)
 * to comply with IntelliJ's threading model.
 *
 * @param project The IntelliJ project instance to which this service is bound.
 */
@Service(Service.Level.PROJECT)
class HighlightLocatorService(private val project: Project) {

    // Logger instance for logging information and errors
    private val logger = Logger.getInstance(HighlightLocatorService::class.java)

    // Plugin settings specific to the project
    private val projectSettings = ProjectPluginSettings.getInstance(project)

    // Executor service to handle scheduled highlight tasks
    private val executor: ScheduledExecutorService = Executors.newScheduledThreadPool(4)

    // Future representing the state of the latest scheduled highlight task
    private var highlightFuture: ScheduledFuture<*>? = null

    /**
     * Handles incoming highlight messages, processes the message, and performs highlighting.
     *
     * @param message the socket request message containing highlight information
     */
    fun handleHighlightMessage(message: JsonObject?, response: (result: String?) -> Unit) {
        val highlightInfo = message?.let { LocatorBuilder.HighlightInfo.fromMap(it) }
        val projectBasePath = project.basePath

        if (message == null || highlightInfo == null) {
            response(getI18nString("locator.invalid.message.body"))
            return
        }
        if (projectBasePath == null) {
            response(getI18nString("locator.file.not.found"))
            return
        }

        processHighlightInfo(highlightInfo, projectBasePath, response)
    }

    /**
     * Processes highlight information by verifying file existence and checksum.
     *
     * @param highlightInfo the parsed highlight information from the message
     * @param projectBasePath the base path of the project to search for files
     */
    private fun processHighlightInfo(
        highlightInfo: LocatorBuilder.HighlightInfo,
        projectBasePath: String,
        response: (result: String?) -> Unit
    ) {
        val absolutePath = Paths.get(projectBasePath, highlightInfo.path).toString()

        // 使用 runReadAction 仅来获取 VirtualFile
        val virtualFile = runReadAction {
            LocalFileSystem.getInstance().findFileByPath(absolutePath)
        }

        if (virtualFile == null) {
            response(getI18nString("locator.file.not.found"))
            return
        }

        // 检查文件校验和
        if (!checkFileChecksum(virtualFile, highlightInfo.checksum)) {
            response(getI18nString("locator.file.checksum.mismatch"))
            return
        }
        highlightFile(virtualFile, highlightInfo, response)
    }

    /**
     * Opens a file in the editor and highlights specified lines.
     *
     * @param virtualFile the file to be opened
     * @param highlightInfo the parsed highlight information from the message
     */
    private fun highlightFile(
        virtualFile: VirtualFile,
        highlightInfo: LocatorBuilder.HighlightInfo,
        response: (result: String?) -> Unit
    ) {
        ApplicationManager.getApplication().invokeLater {
            val editor = openFileInEditor(project, virtualFile)
            if (editor == null) {
                response(getI18nString("locator.cannot.open.file"))
                return@invokeLater
            }
            addDocumentListener(editor)
            highlightLinesInEditor(editor, highlightInfo, response)
        }
    }

    /**
     * Adds a document listener to the editor to clear highlights before document changes.
     *
     * @param editor the editor in which to add the listener
     */
    private fun addDocumentListener(editor: Editor) {
        editor.document.addDocumentListener(object : DocumentListener {
            override fun beforeDocumentChange(event: DocumentEvent) {
                clearHighlights(editor)
            }
        })
    }

    /**
     * Parses line ranges from the highlight location and highlights them in the editor.
     *
     * @param editor the editor instance where the lines need to be highlighted
     * @param highlightInfo the information containing the location details for highlighting
     */
    private fun highlightLinesInEditor(
        editor: Editor,
        highlightInfo: LocatorBuilder.HighlightInfo,
        response: (result: String?) -> Unit
    ) {
        val lineRanges = parseLineRanges(highlightInfo.location)
        val failedLines = mutableListOf<String>()
        // Clear existing highlights before applying new ones
        clearHighlights(editor)

        // Cancel any ongoing highlight operation to debounce
        highlightFuture?.cancel(false)
        highlightFuture = executor.schedule({
            lineRanges.forEach { range ->
                val success = highlightLines(editor, range.startLineNo, range.endLineNo)
                if (!success) {
                    failedLines.add("${range.startLineNo}-${range.endLineNo}")
                }
            }

            // Send appropriate response based on the highlight result
            if (failedLines.isNotEmpty()) {
                response(getI18nString("locator.partial.failure", failedLines.joinToString(",")))
            } else {
                response(null)
            }
        }, 200, TimeUnit.MILLISECONDS) // Add a slight delay for debouncing
    }

    /**
     * Clears all highlights in the specified editor.
     *
     * @param editor the editor from which to remove highlights
     */
    private fun clearHighlights(editor: Editor) {
        ApplicationManager.getApplication().invokeLater {
            editor.markupModel.removeAllHighlighters()
        }
    }

    /**
     * Checks if the file's checksum matches the expected checksum.
     *
     * @param virtualFile the file to be checked
     * @param checksum the expected checksum to be matched
     * @return true if the checksums match, false otherwise
     */
    private fun checkFileChecksum(virtualFile: VirtualFile, checksum: String): Boolean {
        return try {
            val fileContent = virtualFile.contentsToByteArray()
            val digest = MessageDigest.getInstance("MD5")
            val computedChecksum = digest.digest(fileContent).joinToString("") { "%02x".format(it) }
            computedChecksum.equals(checksum, ignoreCase = true)
        } catch (e: Exception) {
            logger.warn("Error computing checksum for file: ${virtualFile.path}", e)
            false
        }
    }

    /**
     * Opens a virtual file in the editor and returns the editor instance.
     *
     * @param project the project context
     * @param virtualFile the file to be opened
     * @return the editor instance if opened successfully, null otherwise
     */
    private fun openFileInEditor(project: Project, virtualFile: VirtualFile): Editor? {
        var editor: Editor? = null
        val fileEditorManager = FileEditorManager.getInstance(project)

        // 使用 runReadAction 仅获取 OpenFileDescriptor
        val descriptor = runReadAction {
            OpenFileDescriptor(project, virtualFile)
        }

        ApplicationManager.getApplication().invokeAndWait {
            try {
                editor = fileEditorManager.openTextEditor(descriptor, true)
            } catch (e: Exception) {
                logger.warn("Failed to open file in editor: ${virtualFile.path}", e)
            }
        }
        return editor
    }

    /**
     * Parses line ranges from location data.
     *
     * @param locations list of highlight locations containing line ranges
     * @return list of line ranges
     */
    private fun parseLineRanges(locations: List<LocatorBuilder.HighlightLocation>): List<LocatorBuilder.LineRange> {
        return locations.flatMap { location ->
            location.lineRanges.split(",").mapNotNull { range ->
                range.split("-").map { it.trim().toIntOrNull() }
                    .takeIf { it.size == 2 && it[0] != null && it[1] != null }
                    ?.let { (start, end) -> LocatorBuilder.LineRange(start!!, end!!) }
            }
        }
    }

    /**
     * Highlights the specified range of lines in the editor.
     *
     * @param editor the editor where lines should be highlighted
     * @param startLine the starting line number (1-based)
     * @param endLine the ending line number (1-based)
     * @return true if the lines were successfully highlighted, false otherwise
     */
    private fun highlightLines(editor: Editor, startLine: Int, endLine: Int): Boolean {
        var success = true
        ApplicationManager.getApplication().invokeLater {
            try {
                val document = editor.document
                if (startLine in 1..document.lineCount && endLine in startLine..document.lineCount) {
                    val startOffset = document.getLineStartOffset(startLine - 1)
                    val endOffset = document.getLineEndOffset(endLine - 1)
                    val highlightColor =
                        ColorUtil.stringToColor(ApplicationPluginSettings.getInstance().highlightBackgroundColorValue)
                    val attributes = TextAttributes(
                        null,
                        highlightColor,
                        null,
                        EffectType.ROUNDED_BOX,
                        Font.PLAIN
                    )

                    editor.markupModel.addRangeHighlighter(
                        startOffset,
                        endOffset,
                        HighlighterLayer.SELECTION - 1,
                        attributes,
                        HighlighterTargetArea.LINES_IN_RANGE
                    )
                } else {
                    logger.warn("Line range is out of bounds: $startLine-$endLine")
                    success = false  // Out of range
                }
            } catch (e: Exception) {
                logger.warn("Failed to highlight lines: $startLine-$endLine", e)
                success = false  // Failed to highlight
            }
        }
        return success
    }
}