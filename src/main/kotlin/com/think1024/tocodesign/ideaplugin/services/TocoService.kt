package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.diagnostic.Logger
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.HttpUtil
import com.think1024.tocodesign.ideaplugin.utils.setQueryParams
import org.json.JSONObject
import java.net.URL
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CancellationException
import java.util.concurrent.atomic.AtomicBoolean

object TocoService {
    private val logger = Logger.getInstance(UserService::class.java)

    fun get(pathKey: String, params: Map<String, String> = emptyMap(), headers: Map<String, String> = emptyMap(), timeout: Int = 0): Pair<Any?, String?> {
        val host = ApplicationPluginSettings.getInstance().host
        val cookies = ApplicationPluginSettings.getInstance().cookies
        val path = ConfigUtil.getProperty(pathKey)
        try {
            val url = URL(host + path).setQueryParams(params)
            val response = HttpUtil.get(url.toString(), cookies, headers, timeout)
            val jsonResponse = JSONObject(response ?: "{}")
            if (jsonResponse.get("code") == 200) {
                return Pair(jsonResponse.get("data"), jsonResponse.getString("message"))
            } else {
                logger.warn("Failed to get $pathKey $path ${jsonResponse.get("message")}")
                return Pair(null, jsonResponse.getString("message"))
            }
        } catch (e: Exception) {
            logger.warn("Failed to get $pathKey $path", e)
            return Pair(null, e.message)
        }
    }

    fun getAsync(pathKey: String, params: Map<String, String> = emptyMap(), headers: Map<String, String> = emptyMap(), timeout: Int = 0): CompletableFuture<Pair<Any?, String?>> {
        val result = CompletableFuture<Pair<Any?, String?>>()
        val isCancelled = AtomicBoolean(false)

        // 使用线程池异步执行get请求
        val future = CompletableFuture.supplyAsync {
            try {
                // 检查是否已取消
                if (isCancelled.get()) {
                    throw CancellationException("Request was cancelled")
                }

                // 调用同步的get方法
                val response = get(pathKey, params, headers, timeout)

                // 再次检查是否已取消
                if (isCancelled.get()) {
                    throw CancellationException("Request was cancelled")
                }

                response
            } catch (e: Exception) {
                // 处理异常
                logger.warn("Failed to execute getAsync for $pathKey", e)
                throw e
            }
        }

        // 设置取消处理
        result.whenComplete { _, _ ->
            if (result.isCancelled) {
                isCancelled.set(true)
                future.cancel(true)
            }
        }

        // 处理结果
        future.whenComplete { response, throwable ->
            when {
                throwable != null -> result.completeExceptionally(throwable)
                else -> result.complete(response)
            }
        }

        return result
    }

    fun post(pathKey: String, body: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0): Pair<Any?, String?> {
        val host = ApplicationPluginSettings.getInstance().host
        val cookies = ApplicationPluginSettings.getInstance().cookies
        val path = ConfigUtil.getProperty(pathKey)
        try {
            val response = HttpUtil.post(host + path, cookies, body, headers, timeout)
            val jsonResponse = JSONObject(response ?: "{}")
            if (jsonResponse.get("code") == 200) {
                return Pair(jsonResponse.get("data"), jsonResponse.getString("message"))
            } else {
                logger.warn("Failed to post $pathKey $path ${jsonResponse.get("message")}")
                return Pair(null, jsonResponse.getString("message"))
            }
        } catch (e: Exception) {
            logger.warn("Failed to post $pathKey $path", e)
            return Pair(null, e.message)
        }
    }

    fun postAsync(pathKey: String, body: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0): CompletableFuture<Pair<Any?, String?>> {
        val result = CompletableFuture<Pair<Any?, String?>>()
        val isCancelled = AtomicBoolean(false)

        // 使用线程池异步执行post请求
        val future = CompletableFuture.supplyAsync {
            try {
                // 检查是否已取消
                if (isCancelled.get()) {
                    throw CancellationException("Request was cancelled")
                }

                // 调用同步的post方法
                val response = post(pathKey, body, headers, timeout)

                // 再次检查是否已取消
                if (isCancelled.get()) {
                    throw CancellationException("Request was cancelled")
                }

                response
            } catch (e: Exception) {
                // 处理异常
                logger.warn("Failed to execute postAsync for $pathKey", e)
                throw e
            }
        }

        // 设置取消处理
        result.whenComplete { _, _ ->
            if (result.isCancelled) {
                isCancelled.set(true)
                future.cancel(true)
            }
        }

        // 处理结果
        future.whenComplete { response, throwable ->
            when {
                throwable != null -> result.completeExceptionally(throwable)
                else -> result.complete(response)
            }
        }
        
        return result
    }
}