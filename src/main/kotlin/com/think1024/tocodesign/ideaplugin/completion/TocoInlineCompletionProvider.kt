@file:Suppress("UnstableApiUsage")

package com.think1024.tocodesign.ideaplugin.completion

import com.google.gson.Gson
import com.intellij.codeInsight.inline.completion.*
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionGrayTextElement
import com.intellij.openapi.util.TextRange
import com.think1024.tocodesign.ideaplugin.services.TocoService
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.CompletableFuture
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

class TocoInlineCompletionProvider: DebouncedInlineCompletionProvider() {
    override val id: InlineCompletionProviderID get() = InlineCompletionProviderID("TocoDesign")
    private var fim: CompletableFuture<Pair<Any?, String?>>? = null

    override suspend fun getDebounceDelay(request: InlineCompletionRequest): Duration {
        return 600.milliseconds
    }

    private suspend fun getCompletion(prefix: String, suffix: String): String? {
        val body = Gson().toJson(
            mapOf(
                "model" to "qwen2.5-coder-32b",
                "prompt" to prefix,
                "suffix" to suffix,
                "context" to emptyList<Any>(),
            )
        )
        fim?.cancel(true)
        val fimReq = TocoService.postAsync("url.path.fim", body, emptyMap(), 5)
        fim = fimReq
        val result = withContext(Dispatchers.IO) {
            fimReq.get()
        } ?: return null
        fim = null

        if (result.first !is JSONObject) {
            return null
        }

        val completion = (result.first as JSONObject).get("completions") as String?
        if (completion !is String || completion.trim().isBlank()) {
            return null
        }
        return completion
    }

    override suspend fun getSuggestionDebounced(request: InlineCompletionRequest): InlineCompletionSuggestion {
        if (!ApplicationPluginSettings.getInstance().inlineCodeCompletion) {
            return InlineCompletionSuggestion.empty()
        }
        // startOffset+1为了把用户输入的回车也包含进去
        val prefix = request.document.getText(TextRange(0, request.startOffset + 1))
        val suffix = request.document.getText(TextRange(request.endOffset, request.document.textLength))

        var completion = getCompletion(prefix, suffix) ?: return InlineCompletionSuggestion.empty()

        val content = request.document.getText(TextRange(request.startOffset + 1, request.endOffset))
        completion = completion.removePrefix(content)
        return InlineCompletionSuggestion.withFlow {
            emit(InlineCompletionGrayTextElement(completion))
        }
    }

    override fun isEnabled(event: InlineCompletionEvent): Boolean {
        val project = event.toRequest()?.file?.project
        if (project == null) {
            return false
        }
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return false
        }
        if (!ApplicationPluginSettings.getInstance().inlineCodeCompletion) {
            return false
        }
        val inlineCompletionRequest = event.toRequest()
        val editor = inlineCompletionRequest?.editor
        val file = editor?.virtualFile
        val extension = file?.extension
        if (extension != "java") {
            return false
        }
        if (event is InlineCompletionEvent.DirectCall) {
            return false
        }
        return true
    }

}