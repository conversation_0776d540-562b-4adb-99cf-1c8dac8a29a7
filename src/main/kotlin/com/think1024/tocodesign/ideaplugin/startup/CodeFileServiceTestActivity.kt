package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.DumbAware
import com.think1024.tocodesign.ideaplugin.services.CodeFileService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * CodeFileService测试启动活动
 * 在插件启动后自动执行getFileContent方法的测试
 */
class CodeFileServiceTestActivity : TocoProjectActivity(), DumbAware {
    private val logger = Logger.getInstance(CodeFileServiceTestActivity::class.java)

    override fun runActivity(project: Project) {
        // 使用协程在后台执行测试，避免阻塞UI线程
        CoroutineScope(Dispatchers.IO).launch {
            // 延迟一段时间确保项目完全加载
            delay(2000)

            logger.info("开始执行 CodeFileService.getFileContent 测试")

            val codeFileService = project.service<CodeFileService>()

            // 测试用例1: 测试读取当前文件本身
            testReadCurrentFile(codeFileService)

            testReadClassMethod(codeFileService)

//            // 测试用例2: 测试读取项目中的其他文件
//            testReadProjectFiles(codeFileService)
//
//            // 测试用例3: 测试读取指定行范围的内容
//            testReadFileWithLineRange(codeFileService)
//
//            // 测试用例4: 测试读取不存在的文件
//            testReadNonExistentFile(codeFileService)
//
//            // 测试用例5: 测试批量读取文件
//            testBatchReadFiles(codeFileService)

            logger.info("CodeFileService.getFileContent 测试完成")
        }
    }

    /**
     * 测试读取当前测试文件本身
     */
    private fun testReadCurrentFile(codeFileService: CodeFileService) {
        try {
            val currentFilePath = "modules/employee_management/entrance/web/src/main/java/com/ai_func_test26/employee_management/entrance/web/controller/AdminEmployeeController.java"
            val content = codeFileService.readFileContentWithFileIO(currentFilePath)

            if (content != null && content.isNotEmpty()) {
                logger.info("✅ 测试1通过: 成功读取当前文件，内容长度: ${content.length}")
                logger.info("文件前50个字符: ${content.take(50)}")
            } else {
                logger.warn("❌ 测试1失败: 无法读取当前文件或内容为空")
            }
        } catch (e: Exception) {
            logger.error("❌ 测试1异常: 读取当前文件时发生异常", e)
        }
    }

    private fun testReadClassMethod(codeFileService: CodeFileService) {
        try {
            val classFilePath = "modules/employee_management/entrance/web/src/main/java/com/ai_func_test26/employee_management/entrance/web/controller/AdminEmployeeController.java"
            val content = codeFileService.readClassMethod(classFilePath, "com.ai_func_test26.employee_management.entrance.web.controller.AdminEmployeeController.queryEmployeeList")
            if (content != null ) {
                logger.info("✅ 测试1.1通过: 成功读取CodeFileService类方法")
            } else {
                logger.warn("❌ 测试1.1失败: 无法读取CodeFileService类方法或内容为空")
            }
        } catch (e: Exception) {
            logger.error("❌ 测试1.1异常: 读取CodeFileService类方法时发生异常", e)
        }
    }

}
