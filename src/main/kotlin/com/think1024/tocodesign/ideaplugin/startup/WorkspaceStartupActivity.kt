package com.think1024.tocodesign.ideaplugin.startup
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.actions.hookCloseActions
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import com.think1024.tocodesign.ideaplugin.toco.Fossil
import com.think1024.tocodesign.ideaplugin.toco.FossilResult
import com.think1024.tocodesign.ideaplugin.toco.Item
//import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import com.think1024.tocodesign.ideaplugin.webview.WebviewPackage

class WorkspaceStartupActivity: TocoProjectActivity() {
    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }
        hookCloseActions()
        // 启动同步载入离线包到内存
        WebviewPackage
        ApplicationManager.getApplication().executeOnPooledThread {
            val projectWorkFolder = project.basePath ?: return@executeOnPooledThread
            val projectInfo = ProjectConfigUtil.getProjectInfo(projectWorkFolder) ?: return@executeOnPooledThread
            val name = projectInfo["name"]
            val id = projectInfo["id"]
            if (name == null || id == null) {
                return@executeOnPooledThread
            }
            Fossil.fixFossilAdmin(projectWorkFolder)
            // 如果之前操作中断出现fossil在merge状态的话，revert
            Fossil.abortMergeIfNeeded(projectWorkFolder)
            val item = Item(id, name)
            val projectFile = FileUtil.getFile(Fossil.getProjectFile(name))
            if (projectFile == null) {
                val result = Fossil.createProject(item)
                if (!result.success) {
                    return@executeOnPooledThread
                }
            }
            Fossil.initProject(item, projectWorkFolder) { _, _ -> FossilResult(true) }
        }
        // 插件启动后启动本地server
//        BoilerplateCmd.startServer()
        // 生成本地HTTPS证书，用于前端连通性测试
        BoilerplateCmd.mkcert(project)
    }
}