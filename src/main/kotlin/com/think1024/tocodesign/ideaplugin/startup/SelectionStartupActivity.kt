package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.project.Project
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiJavaFile
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class SelectionStartupActivity : TocoProjectActivity() {
    @OptIn(DelicateCoroutinesApi::class)
    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }
        val multicaster = EditorFactory.getInstance().eventMulticaster

        multicaster.addSelectionListener(object : SelectionListener {
            override fun selectionChanged(e: SelectionEvent) {
                val editor = e.editor
                val selectedText = editor.selectionModel.selectedText ?: return

                // 检查选中内容是否为空或仅包含空白字符
                if (selectedText.trim().isEmpty()) {
                    return
                }

                // 检查编辑器所属文件类型和上下文
                val project = editor.project ?: return
                val editorFile = FileDocumentManager.getInstance().getFile(editor.document)

                // 排除非项目文件或特殊编辑器
                if (editorFile == null || !editorFile.isValid || !editorFile.isInLocalFileSystem) {
                    return  // 排除虚拟文件或非本地文件系统的文件
                }

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(editor.document)
                if (psiFile is PsiJavaFile) {
                    println("✅ 当前选中区域: $selectedText")

                    // 获取文件信息
                    val fileInfo = createFileInfo(editorFile, selectedText)

                    // 启动协程，异步向 WebView 发送消息
                    GlobalScope.launch {
                        try {
                            WebViewBridge.sendIfReady(project, "TocoDesign", "selection-changed", fileInfo)
                        } catch (e: Exception) {
                            println("❌ WebView 请求失败: ${e.message}")
                        }
                    }
                }
            }
        }, project)

        println("✅ SelectionListener 已注册")
    }

    /**
     * 创建包含文件信息的Map
     */
    private fun createFileInfo(file: VirtualFile, content: String): Map<String, String> {
        val projectRelativePath = getProjectRelativePath(file)
        return mapOf(
            "name" to file.name,
            "path" to projectRelativePath,
            "content" to content
        )
    }

    /**
     * 获取文件相对于项目根目录的路径
     */
    private fun getProjectRelativePath(file: VirtualFile): String {
        // 假设项目根目录是文件的上级目录之一
        // 这里简化处理，实际可能需要更复杂的逻辑来确定项目根
        val path = file.path
        val projectRootIndex = path.indexOf("/src/")
        return if (projectRootIndex != -1) {
            path.substring(projectRootIndex + 1) // 去掉项目根目录前缀
        } else {
            // 如果无法确定相对路径，返回完整路径
            path
        }
    }
}