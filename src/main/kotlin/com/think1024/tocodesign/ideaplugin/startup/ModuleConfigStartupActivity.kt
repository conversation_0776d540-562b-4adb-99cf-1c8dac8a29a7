package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ModuleConfigUtil
import org.jetbrains.idea.maven.project.MavenProjectsManager
import java.lang.reflect.Method

/**
 * A startup activity that reloads Maven projects and scans and caches module configurations when a project is opened.
 * This class implements StartupActivity to run on project startup and DumbAware to indicate
 * that it can run even when IDE is in dumb mode (e.g., during indexing).
 */
class ModuleConfigStartupActivity : TocoProjectActivity(), DumbAware {
    // Logger instance for this class
    private val logger = Logger.getInstance(ModuleConfigStartupActivity::class.java)

    /**
     * This method is called when a project is opened.
     * It reloads Maven projects and then scans and caches module configurations for the opened project.
     *
     * @param project The Project instance that has just been opened
     */
    override fun runActivity(project: Project) {
        try {
            if (ProjectPluginSettings.getInstance(project).projectId == null) {
                return
            }
            // Get the MavenProjectsManager instance for the project
            val mavenProjectsManager = MavenProjectsManager.getInstance(project)

            // Check if the project is a Maven project
            if (mavenProjectsManager.isMavenizedProject) {
                // Reload all Maven projects
                ApplicationManager.getApplication().invokeLater({
                    mavenProjectsManager.forceUpdateAllProjectsOrFindAllAvailablePomFiles()
                    logger.info("Maven projects reload triggered for project: ${project.name}")

                    // Wait for Maven reload to complete before scanning and caching modules
                    waitForMavenResolving(mavenProjectsManager)

                    // Scan and cache module configurations
                    ModuleConfigUtil.scanAndCacheModules(project)

                    // Log successful completion
                    logger.info("Module configuration scan and cache completed for project: ${project.name}")
                }, ModalityState.NON_MODAL)
            } else {
                // If it's not a Maven project, just scan and cache modules
                ModuleConfigUtil.scanAndCacheModules(project)
                logger.info("Module configuration scan and cache completed for non-Maven project: ${project.name}")
            }
        } catch (e: Exception) {
            // Log any errors that occur during the process
            logger.warn("Failed to reload Maven projects and scan/cache modules for project: ${project.name}", e)
        }
    }

    /**
     * Attempts to wait for Maven resolving completion using reflection.
     * This method is designed to be compatible with different versions of IntelliJ IDEA.
     *
     * @param mavenProjectsManager The MavenProjectsManager instance
     */
    private fun waitForMavenResolving(mavenProjectsManager: MavenProjectsManager) {
        try {
            val waitMethod: Method? = mavenProjectsManager.javaClass.methods.find {
                it.name == "waitForResolvingCompletion"
            }

            if (waitMethod != null) {
                waitMethod.invoke(mavenProjectsManager)
                logger.info("Successfully waited for Maven resolving completion")
            } else {
                logger.info("waitForResolvingCompletion method not found, skipping wait")
            }
        } catch (e: Exception) {
            logger.warn("Error while waiting for Maven resolving completion", e)
        }
    }
}
