package com.think1024.tocodesign.ideaplugin.env

import com.intellij.execution.RunConfigurationExtension
import com.intellij.execution.configurations.JavaParameters
import com.intellij.execution.configurations.RunConfigurationBase
import com.intellij.execution.configurations.RunnerSettings

class TocoRunConfigurationExtension : RunConfigurationExtension() {
    override fun <T : RunConfigurationBase<*>> updateJavaParameters(configuration: T, params: JavaParameters, runnerSettings: RunnerSettings?) {
        params.env["SERVER_SSL_CERTIFICATE"] = "classpath:localhost.pem"
        params.env["SERVER_SSL_CERTIFICATE_PRIVATE_KEY"] = "classpath:localhost-key.pem"
    }

    override fun isApplicableFor(configuration: RunConfigurationBase<*>): Boolean {
        return true
    }
}