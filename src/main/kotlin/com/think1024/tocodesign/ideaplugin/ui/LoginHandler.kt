package com.think1024.tocodesign.ideaplugin.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.think1024.tocodesign.ideaplugin.services.UserService
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.CookieManager
import com.think1024.tocodesign.ideaplugin.webview.LoadingMode
import com.think1024.tocodesign.ideaplugin.webview.WebViewDialog
import com.think1024.tocodesign.ideaplugin.webview.WebViewManager
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import javax.swing.SwingUtilities
import javax.swing.Timer

/**
 * Handles login requests by displaying a login dialog with an embedded browser.
 * This class is responsible for creating and showing a dialog that contains
 * a WebView component loading the specified login URL.
 */
class LoginHandler(private val project: Project? = null) {
    
    // 保存当前打开的登录对话框的引用
    private var currentLoginDialog: WebViewDialog? = null
    
    // 用于检查 cookie 的定时器
    private var cookieCheckTimer: Timer? = null
    
    /**
     * Handles a login request by showing a login dialog with the specified URL.
     * If a login dialog is already open, it will be brought to the front instead of creating a new one.
     *
     * @return A map containing the status of the operation
     */
    fun handleLogin(url: String? = null, callback: (() -> Unit)? = null): Map<String, Any> {
        SwingUtilities.invokeLater {
            showLoginDialog(url, callback)
        }
        
        return mapOf("status" to "login_dialog_opened")
    }
    
    /**
     * Creates and shows a modal dialog containing a WebView component that loads the login URL.
     * If a dialog is already open, it will be brought to the front instead of creating a new one.
     *
     * @param url The URL to load in the WebView component
     */
    private fun showLoginDialog(url: String? = null, callback: (() -> Unit)? = null) {
        // 如果已经有对话框打开，则将其置于前台
        currentLoginDialog?.let { dialog ->
            if (dialog.isVisible) {
                dialog.toFront()
                return
            }
        }
        
        // 停止之前的 cookie 检查定时器
        cookieCheckTimer?.stop()
        
        // 创建新的对话框
        val host = ApplicationPluginSettings.getInstance().host
        val fallback = host + ConfigUtil.getProperty("url.path.login")
        val loginUrl = url ?: ("$host/manage/login?fallback=$fallback")
        val dialog = WebViewDialog(
            project,
            loginUrl,
            "Login",
            LoadingMode.LOAD_END_HANDLER,
            false
        )
        
        // 确保清除之前的 cookie
        if (ApplicationPluginSettings.getInstance().cookies.isEmpty()) {
            CookieManager.clearCookiesForHost(host)
        }


        // 保存对话框引用
        currentLoginDialog = dialog
        
        // 设置 cookie 检查定时器
        cookieCheckTimer = Timer(1000) { checkCookies(dialog, callback) }
        cookieCheckTimer?.start()
        
        // 添加对话框关闭监听器，清除引用和停止定时器
        Disposer.register(dialog.disposable) {
            if (currentLoginDialog === dialog) {
                currentLoginDialog = null
            }
            cookieCheckTimer?.stop()
            cookieCheckTimer = null
        }
        
        dialog.show()
    }
    
    /**
     * 检查 cookie 是否包含登录令牌，如果包含则处理登录成功逻辑
     * 
     * @param dialog 当前的登录对话框
     */
    private fun checkCookies(dialog: WebViewDialog, callback: (() -> Unit)? = null) {
        dialog.getBrowser().getCookies { cookiesString ->
//            println("cookiesString: $cookiesString")

            val hasValidToken = if (cookiesString.isNotEmpty()) {
                // 解析 cookie 字符串
                val cookies = cookiesString.split(";").map { it.trim() }
                // 查找 U_TOKEN cookie
                val tokenCookie = cookies.find { it.startsWith("${CookieManager.U_TOKEN}=") }
                // 检查 token cookie 是否存在且值不为空
                tokenCookie != null && tokenCookie.length > "${CookieManager.U_TOKEN}=".length
            } else {
                false
            }

            if (hasValidToken) {
                // U_TOKEN found, store cookies and update user info
                CookieManager.storeCookies(cookiesString)
                UserService.getInstance().updateUserInfo()

                updateAllWebViewsCookies(cookiesString)

                callback?.invoke()
                // 关闭对话框
                SwingUtilities.invokeLater {
                    closeDialog(dialog)
                }
            }
        }
    }
    
    /**
     * 关闭登录对话框并清理资源
     * 
     * @param dialog 要关闭的对话框
     */
    private fun closeDialog(dialog: WebViewDialog) {
        cookieCheckTimer?.stop()
        cookieCheckTimer = null
        
        if (currentLoginDialog === dialog) {
            currentLoginDialog = null
        }
        
        dialog.close(0)
    }
    
    /**
     * 处理登出请求，清除 cookie 并更新用户状态
     * 
     * @return 操作状态
     */
    fun handleLogout(): Map<String, Any> {
        // 清除 cookie
        val settings = ApplicationPluginSettings.getInstance()
        CookieManager.clearCookiesForHost(settings.host, CookieManager.U_TOKEN)
        CookieManager.clearCookiesForHost(settings.frontendHost, CookieManager.U_TOKEN)
        
        // 清除用户数据
        UserService.getInstance().clearUserData()
        
        // 更新所有 WebView 的 cookie，清除 U_TOKEN
        // 使用空 Map 而不是只包含空值的 U_TOKEN，确保清除所有 cookie
        updateAllWebViewsCookies(mapOf(CookieManager.U_TOKEN to ""))
        
        return mapOf("status" to "logout_success")
    }
    
    /**
     * 更新所有 WebView 的 cookie
     * 
     * @param cookiesString 要设置的 cookie 字符串
     */
    private fun updateAllWebViewsCookies(cookiesString: String) {
        // 解析 cookie 字符串为 Map
        val cookieMap = mutableMapOf<String, String>()
        if (cookiesString.isNotEmpty()) {
            val cookies = cookiesString.split(";").map { it.trim() }
            for (cookie in cookies) {
                val parts = cookie.split("=", limit = 2)
                if (parts.size == 2) {
                    cookieMap[parts[0]] = parts[1]
                }
            }
        }
        
        updateAllWebViewsCookies(cookieMap)
    }
    
    /**
     * 更新所有 WebView 的 cookie
     * 
     * @param cookieMap 要设置的 cookie 映射
     */
    private fun updateAllWebViewsCookies(cookieMap: Map<String, String>) {
        // 获取所有注册的 WebView 并更新它们的 cookie
        WebViewManager.getAllBrowsers(project)?.forEach { browser ->
            try {
                // 获取当前 URL
                val currentUrl = browser.browser.cefBrowser.url
                // 使用 TocoBrowserFactory 更新 cookie
                TocoBrowserFactory.createBrowserWithCookies(
                    currentUrl,
                    cookieMap,
                    browser.browser
                )

//                // 向 WebView 发送消息，通知 cookie 已更新
//                val isLoggedIn = cookieMap.containsKey(CookieManager.U_TOKEN) && cookieMap[CookieManager.U_TOKEN]?.isNotEmpty() == true
//
//                // 使用协程在后台发送消息
//                kotlinx.coroutines.GlobalScope.launch {
//                    try {
//                        browser.sendToWebview(
//                            "cookie-updated",
//                            mapOf(
//                                "isLoggedIn" to isLoggedIn,
//                                "timestamp" to System.currentTimeMillis()
//                            )
//                        )
//                        println("已通知 WebView cookie 更新: ${currentUrl}")
//                    } catch (e: Exception) {
//                        println("通知 WebView cookie 更新时出错: ${e.message}")
//                    }
//                }
                
                // 使用 Timer 实现简单的延迟刷新
                Timer(200) { _ ->
                    browser.browser.cefBrowser.reload()
                }.apply {
                    isRepeats = false
                    start()
                }
//                println("已更新 WebView cookie: $currentUrl")
            } catch (e: Exception) {
                println("更新 WebView cookie 时出错: ${e.message}")
            }
        }
    }
    
    companion object {
        // 单例实例，确保整个应用中只有一个 LoginHandler
        @Volatile
        private var instance: LoginHandler? = null
        
        /**
         * 获取 LoginHandler 的单例实例
         *
         * @param project 当前项目
         * @return LoginHandler 实例
         */
        fun getInstance(project: Project? = null): LoginHandler {
            return instance ?: synchronized(this) {
                instance ?: LoginHandler(project).also { instance = it }
            }
        }
    }
}