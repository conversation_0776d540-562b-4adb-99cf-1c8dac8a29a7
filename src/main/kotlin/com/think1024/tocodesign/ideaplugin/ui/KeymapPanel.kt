package com.think1024.tocodesign.ideaplugin.ui

import com.intellij.openapi.keymap.KeymapUtil
import com.intellij.ui.components.fields.ExtendableTextField
import java.awt.event.KeyAdapter
import java.awt.event.KeyEvent
import javax.swing.KeyStroke

/**
 * A custom text field that displays the currently selected keystroke.
 */
class KeymapPanel : ExtendableTextField() {
    init {
        isEditable = false
        addKeyListener(object : KeyAdapter() {
            override fun keyPressed(e: KeyEvent) {
                val keyStroke = KeyStroke.getKeyStrokeForEvent(e)
                text = KeymapUtil.getKeystrokeText(keyStroke)
                e.consume()
            }
        })
    }
}