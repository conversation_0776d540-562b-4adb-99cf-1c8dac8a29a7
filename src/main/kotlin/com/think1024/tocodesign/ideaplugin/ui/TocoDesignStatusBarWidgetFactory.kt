package com.think1024.tocodesign.ideaplugin.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.StatusBar
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.StatusBarWidgetFactory
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString

/**
 * Factory class for creating TocoDesign status bar widgets.
 * This class is responsible for managing the lifecycle of TocoDesign status bar widgets.
 */
class TocoDesignStatusBarWidgetFactory : StatusBarWidgetFactory {
    /**
     * Returns the unique identifier for this widget factory.
     *
     * @return The widget ID as a String.
     */
    override fun getId(): String = "TocoDesignStatusBarWidget"

    /**
     * Returns the display name for the widget.
     * This name is internationalized using the language manager.
     *
     * @return The localized display name as a String.
     */
    override fun getDisplayName(): String = getI18nString("statusbar.widget.display.name")

    /**
     * Determines if the widget is available for the given project.
     *
     * @param project The current project.
     * @return Always returns true, indicating the widget is available for all projects.
     */
    override fun isAvailable(project: Project) = ProjectPluginSettings.getInstance(project).projectId != null

    /**
     * Creates a new instance of the TocoDesign status bar widget for the given project.
     *
     * @param project The project for which to create the widget.
     * @return A new TocoDesignStatusBarWidget instance.
     */
    override fun createWidget(project: Project): StatusBarWidget = TocoDesignStatusBarWidget(project)

    /**
     * Disposes of the given widget instance.
     *
     * @param widget The widget to dispose.
     */
    override fun disposeWidget(widget: StatusBarWidget) {
        widget.dispose()
    }

    /**
     * Determines if the widget can be enabled on the given status bar.
     *
     * @param statusBar The status bar to check.
     * @return Always returns true, indicating the widget can be enabled on any status bar.
     */
    override fun canBeEnabledOn(statusBar: StatusBar): Boolean = true
}
