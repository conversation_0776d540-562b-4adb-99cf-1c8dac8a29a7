package com.think1024.tocodesign.ideaplugin.ui

import com.intellij.openapi.ui.ComboBox
import com.intellij.ui.components.JBLabel
import javax.swing.ListCellRenderer

data class ComboItem<T>(
    val label: String,
    val value: T
)

fun <T>createComboBox(items: List<ComboItem<T>>): ComboBox<ComboItem<T>> {
    // 创建 ComboBox
    val comboBox = ComboBox(items.toTypedArray())

    // 设置自定义渲染器
    comboBox.renderer =
        ListCellRenderer { list, value, _, isSelected, _ ->
            val label = JBLabel(value?.label ?: "")
            if (isSelected) {
                label.background = list?.selectionBackground
                label.foreground = list?.selectionForeground
            } else {
                label.background = list?.background
                label.foreground = list?.foreground
            }
            label
        }

    return comboBox
}