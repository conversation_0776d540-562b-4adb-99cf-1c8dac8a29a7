package com.think1024.tocodesign.ideaplugin.ui

import com.intellij.openapi.ui.popup.Balloon
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.ui.JBColor
import com.intellij.ui.awt.RelativePoint
import java.awt.Point
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicReference
import javax.swing.JComponent
import javax.swing.Popup
import javax.swing.SwingUtilities
import javax.swing.Timer
import javax.swing.border.LineBorder

object UIUtil {
    // Concurrent maps to store popups and tasks associated with components
    private val popups = ConcurrentHashMap<JComponent, AtomicReference<Popup?>>()
    private val tasks = ConcurrentHashMap<JComponent, AtomicReference<ScheduledFuture<*>?>>()

    // Executor for scheduling tasks
    private val executor = Executors.newScheduledThreadPool(1)

    /**
     * Flashes a component by alternating its border color.
     *
     * @param component The component to flash
     * @param duration The total duration of the flashing effect in milliseconds
     * @param flashCount The number of times the component should flash
     */
    fun flashComponent(component: JComponent, duration: Int = 1000, flashCount: Int = 3) {
        SwingUtilities.invokeLater {
            val originalBorder = component.border
            val flashBorder = LineBorder(JBColor.ORANGE, 2)

            var flashes = 0
            val timer = Timer(duration / (flashCount * 2)) { event ->
                if (flashes >= flashCount * 2) {
                    component.border = originalBorder
                    (event.source as Timer).stop()
                } else {
                    component.border = if (flashes % 2 == 0) flashBorder else originalBorder
                    flashes++
                }
                component.repaint()
            }
            timer.start()
        }
    }

    /**
     * Shows a temporary tooltip above a component.
     *
     * @param component The component above which to show the tooltip
     * @param message The message to display in the tooltip
     * @param durationMillis The duration for which the tooltip should be visible
     */
    fun showTemporaryTooltip(component: JComponent, message: String, durationMillis: Long = 3000) {
        SwingUtilities.invokeLater {
            val backgroundColor = JBColor.background()

            val balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(message, null, backgroundColor, null)
                .setFadeoutTime(durationMillis)
                .setHideOnAction(true)
                .setHideOnKeyOutside(true)
                .setHideOnClickOutside(true)
                .setAnimationCycle(200)
                .setBorderColor(backgroundColor)
                .createBalloon()

            val centerPoint = Point(component.width / 2, component.height / 2)
            balloon.show(RelativePoint(component, centerPoint), Balloon.Position.above)
        }
    }

    /**
     * Hides any existing popup associated with the given component.
     *
     * @param component The component whose popup should be hidden
     */
    private fun hideExistingPopup(component: JComponent) {
        popups[component]?.getAndSet(null)?.hide()
    }

    /**
     * Cancels any existing task associated with the given component.
     *
     * @param component The component whose task should be cancelled
     */
    private fun cancelExistingTask(component: JComponent) {
        tasks[component]?.getAndSet(null)?.cancel(true)
    }

    /**
     * Cleans up resources used by this utility.
     * Make sure to call this method when your plugin is being unloaded.
     */
    fun cleanUp() {
        executor.shutdown()
        try {
            if (!executor.awaitTermination(800, TimeUnit.MILLISECONDS)) {
                executor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            executor.shutdownNow()
        }
    }
}
