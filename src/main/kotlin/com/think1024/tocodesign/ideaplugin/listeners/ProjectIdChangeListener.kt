package com.think1024.tocodesign.ideaplugin.listeners

import com.intellij.util.messages.Topic

/**
 * Listener interface for handling changes in project ID.
 * Implementations of this interface can be used to respond to project ID changes.
 */
interface ProjectIdChangeListener {

    /**
     * Called when the project ID has changed.
     *
     * @param newProjectId The new project ID, or null if the project ID is unset.
     */
    fun projectIdChanged(newProjectId: String?)

    companion object {
        /**
         * Topic used to subscribe to project ID change events.
         */
        val TOPIC = Topic.create("Project ID Change", ProjectIdChangeListener::class.java)
    }
}