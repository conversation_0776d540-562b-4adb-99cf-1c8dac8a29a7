package com.think1024.tocodesign.ideaplugin.listeners

import com.intellij.ide.plugins.DynamicPluginListener
import com.intellij.ide.plugins.IdeaPluginDescriptor
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.think1024.tocodesign.ideaplugin.services.UserService
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorIndexManager
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.ModuleConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.ProjectResourceUtil
import kotlinx.coroutines.*

/**
 * A listener that handles the plugin unload events. It implements DynamicPluginListener to respond
 * when the plugin is being unloaded from the IDE, executing necessary cleanup tasks.
 */
class PluginUnloadListener : DynamicPluginListener {
    private val logger = Logger.getInstance("com.think1024.tocodesign.ideaplugin.listeners.PluginUnloadListener")

    override fun beforePluginUnload(pluginDescriptor: IdeaPluginDescriptor, isUpdate: Boolean) {
        if (pluginDescriptor.pluginId.idString == ConfigUtil.getProperty("project.plugin.id")) {
            runBlocking {
                // Run logoutUser as a coroutine to allow for potential concurrent tasks
                logoutUser()

                try {
                    ProjectManager.getInstance().openProjects.map { project ->
                        async { cleanProjectResources(project) }
                    }.awaitAll()
                    logger.info("Successfully cleaned up resources during plugin unload")
                } catch (e: Exception) {
                    logger.warn("Failed to clean up resources during plugin unload", e)
                }
            }

            // Immediately trigger saving settings
            ApplicationManager.getApplication().invokeLater {
                try {
                    // Save all settings to persist changes
                    ApplicationManager.getApplication().saveAll()
                } catch (e: Exception) {
                    logger.warn("Failed to save all settings during plugin unload", e)
                }
            }
        }
    }

    /**
     * Logs out the user from the system. If logout fails, it attempts to clear user data.
     */
    private suspend fun logoutUser() = coroutineScope {
        try {
            // Assume logout involves multiple independent operations
            listOf(
                async { UserService.getInstance().logout() }
                // Add other async operations here if needed
            ).awaitAll()
            logger.info("Logout successfully during plugin unload")
        } catch (e: Exception) {
            clearUserData()
            logger.warn("Failed to logout during plugin unload", e)
        }
    }

    /**
     * Clears user data if the logout process fails.
     */
    private fun clearUserData() {
        try {
            UserService.getInstance().clearUserData()
        } catch (e: Exception) {
            logger.warn("Failed to clear user data during plugin unload", e)
        }
    }

    private suspend fun cleanProjectResources(project: Project) {
        try {
            // Use a coroutine scope to run resource cleanup tasks in parallel.
            coroutineScope {

                // Disconnect the message bus to prevent further message processing.
                launch { ProjectResourceUtil.disconnectMessageBus(project) }

                // Cancel any project ID change listeners to clean up memory.
                launch { ProjectResourceUtil.cancelProjectIdChangeListenerForProject(project) }

                // Dispose of the custom status bar widget associated with the project.
                launch { ProjectResourceUtil.disposeTocoDesignStatusBarWidget(project) }

                // Clear index cache associated with the project.
                launch { clearIndexCacheForProject(project) }

                // Clear project configuration caches using the specified config file.
                launch { ProjectConfigUtil.clearCaches(project, ProjectPluginSettings.PROJECT_CONFIG_FILE) }

                // Clear any module configuration cache related to the project.
                launch { ModuleConfigUtil.clearCache(project) }
            }
        } catch (e: Exception) {
            // Log a warning if any exception is raised during the cleanup process.
            logger.warn("Failed to clean resources for project: ${project.name}", e)
        }
    }

    /**
     * Clears the index cache for a specific project.
     *
     * @param project The project whose index cache is to be cleared.
     */
    private fun clearIndexCacheForProject(project: Project) {
        try {
            val indexManager = project.service<LocatorIndexManager>()
            indexManager.clearIndexCache()
        } catch (e: Exception) {
            logger.warn("Failed to clear index cache for project: ${project.name}", e)
        }
    }
}
