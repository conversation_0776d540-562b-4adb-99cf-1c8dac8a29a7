package com.think1024.tocodesign.ideaplugin.listeners

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.psi.PsiManager
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorFileScanner
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Listener class that handles batch file change events in the project.
 * Processes Java files with a queue and scheduled execution to optimize performance.
 *
 * @param project The project to which this listener is attached.
 */
class LocatorFileChangeListener(private val project: Project) : BulkFileListener {
    // Logger instance for logging within the LocatorFileChangeListener class
    private val logger = Logger.getInstance(LocatorFileChangeListener::class.java)

    // Scanner for processing the PSI files associated with the project
    private val scanner = LocatorFileScanner(project)

    // PSI Manager to handle PSI-related operations in the project
    private val psiManager = PsiManager.getInstance(project)

    // Coroutine scope for launching asynchronous tasks in the IO dispatcher
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // Queue to hold file events that need to be processed
    private val fileQueue = ConcurrentLinkedQueue<VFileEvent>()

    // Job representing the scheduled processing task; used to manage its state
    private var scheduledJob: Job? = null


    companion object {
        private const val BATCH_SIZE = 10          // Max number of events to process in one batch
        private const val PROCESS_DELAY_MS = 2000  // Delay in milliseconds before processing
    }

    /**
     * Called after a set of VFileEvents occur. Filters for Java file events and adds them to the queue.
     *
     * @param events List of file events to be processed.
     */
    override fun after(events: List<VFileEvent>) {
        if (project.isDisposed || ProjectPluginSettings.getInstance(project).projectId.isNullOrEmpty()) {
            return
        }

        coroutineScope.launch {
            events.filter { it.file?.extension == "java" }
                .forEach { fileQueue.add(it) }

            scheduleProcessing()
        }
    }

    /**
     * Schedules processing of the queue to occur after a delay or when enough events accumulate.
     * This helps in reducing frequent processing and optimizes performance.
     */
    private fun scheduleProcessing() {
        if (fileQueue.size >= BATCH_SIZE) {
            // Process immediately if the queue has enough events
            processBatch()
        } else if (scheduledJob == null || scheduledJob?.isCompleted == true) {
            // Schedule the process with a delay if not already scheduled
            scheduledJob = coroutineScope.launch {
                delay(PROCESS_DELAY_MS.toLong())  // Adjust this delay based on desired responsiveness
                processBatch()
            }
        }
    }

    /**
     * Processes a batch of file events from the queue. Converts VFileEvents to PSI files and scans them.
     */
    private fun processBatch() {
        // Check if the project is disposed before proceeding
        if (project.isDisposed) {
            logger.warn("Attempted to process files for a disposed project.")
            return
        }

        // List to hold the batch of file events to be processed
        val batch = mutableListOf<VFileEvent>()

        // Collect a batch of events from the queue until the batch size is reached or the queue is empty
        while (batch.size < BATCH_SIZE && fileQueue.isNotEmpty()) {
            fileQueue.poll()?.let { batch.add(it) }
        }

        // If no events were collected, exit the function
        if (batch.isEmpty()) return

        // Use runReadAction to perform the necessary read operations on the files
        val psiFiles = runReadAction {
            batch.mapNotNull { event ->
                event.file?.let { file ->
                    // Check if the file is still valid
                    if (file.isValid) {
                        try {
                            psiManager.findFile(file) ?: run {
                                logger.warn("File not found: ${file.path}")
                                null
                            }
                        } catch (e: Exception) {
                            logger.warn("Error finding file: ${file.path}", e)
                            null
                        }
                    } else {
                        logger.warn("Invalid file: ${file.path}")
                        null
                    }
                }
            }
        }

        // Process each PSI file within the read action to ensure thread safety
        runReadAction {
            psiFiles.forEach { psiFile ->
                scanner.scanFile(psiFile)  // Move this line inside runReadAction
            }
        }
    }

}
