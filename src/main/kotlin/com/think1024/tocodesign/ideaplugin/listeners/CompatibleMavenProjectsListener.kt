package com.think1024.tocodesign.ideaplugin.listeners

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Pair
import org.jetbrains.idea.maven.project.MavenProject
import org.jetbrains.idea.maven.project.MavenProjectChanges
import org.jetbrains.idea.maven.project.MavenProjectsTree
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.Proxy

/**
 * A dynamic proxy listener for MavenProjectsTree to support multiple IntelliJ IDEA versions (2022.2 through 2024.3).
 * This class creates a runtime dynamic proxy instance of MavenProjectsTree.Listener interface,
 * delegating method calls to a provided lambda handler. This approach avoids direct dependency
 * on specific IDE versions and improves compatibility across IDEA releases.
 *
 * @property project The current IntelliJ IDEA project associated with the listener.
 * @property onUpdate A callback lambda invoked when Maven project updates or deletions occur.
 *                  Receives two parameters: a list of updated Maven projects with their changes,
 *                  and a list of deleted Maven projects.
 */
class CompatibleMavenProjectsListener(
    private val project: Project,
    private val onUpdate: (updated: List<Pair<MavenProject, MavenProjectChanges>>, deleted: List<MavenProject>) -> Unit
) {
    private val logger = Logger.getInstance(CompatibleMavenProjectsListener::class.java)

    /**
     * Lazily initialized dynamic proxy instance implementing MavenProjectsTree.Listener.
     * The proxy intercepts method calls and delegates the 'projectsUpdated' method to the `onUpdate` lambda.
     * For Object's standard methods (toString, hashCode, equals), it provides conventional implementations.
     */
    val proxyInstance: MavenProjectsTree.Listener by lazy {
        Proxy.newProxyInstance(
            MavenProjectsTree.Listener::class.java.classLoader,
            arrayOf(MavenProjectsTree.Listener::class.java),
            MavenProjectsListenerHandler()
        ) as MavenProjectsTree.Listener
    }

    /**
     * Inner InvocationHandler class that intercepts calls to MavenProjectsTree.Listener methods.
     * It handles 'projectsUpdated' method by extracting the updated and deleted Maven projects
     * and invoking the provided 'onUpdate' lambda. Other standard Object methods are handled appropriately.
     */
    private inner class MavenProjectsListenerHandler : InvocationHandler {
        override fun invoke(proxy: Any?, method: Method, args: Array<out Any>?): Any? {
            return when (method.name) {
                "projectsUpdated" -> handleProjectsUpdated(args)
                "toString" -> "CompatibleMavenProjectsListenerProxy"
                "hashCode" -> System.identityHashCode(proxy)
                "equals" -> args?.getOrNull(0)?.let { proxy === it } ?: false
                else -> null
            }
        }

        /**
         * Handles the 'projectsUpdated' method invocation.
         * Safely casts the method arguments to expected types: a list of updated Maven projects with changes,
         * and a list of deleted Maven projects. If either list is non-null, invokes the 'onUpdate' lambda.
         * Uses empty lists as defaults for null arguments to avoid NullPointerExceptions.
         *
         * @param args The method arguments passed to 'projectsUpdated'.
         * @return Always returns null as 'projectsUpdated' has void return type.
         */
        private fun handleProjectsUpdated(args: Array<out Any>?): Any? {
            if (args == null || args.size != 2) return null

            try {
                @Suppress("UNCHECKED_CAST")
                val updated = args[0] as? List<Pair<MavenProject, MavenProjectChanges>>
                    ?: (args[0] as? MutableList<Pair<MavenProject, MavenProjectChanges>>)?.toList()

                @Suppress("UNCHECKED_CAST")
                val deleted = args[1] as? List<MavenProject>
                    ?: (args[1] as? MutableList<MavenProject>)?.toList()

                if (updated != null || deleted != null) {
                    onUpdate(updated ?: emptyList(), deleted ?: emptyList())
                }
            } catch (e: Exception) {
                logger.warn("Error during Maven projects listener invocation", e)
            }
            return null
        }
    }
}
