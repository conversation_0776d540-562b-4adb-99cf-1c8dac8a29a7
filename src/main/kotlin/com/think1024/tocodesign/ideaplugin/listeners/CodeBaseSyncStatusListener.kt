package com.think1024.tocodesign.ideaplugin.events

import com.intellij.util.messages.Topic

/**
 * Listener interface for handling changes in codebase sync status.
 * Implementations of this interface can be used to respond to sync status changes.
 */
interface CodeBaseSyncStatusListener {

    /**
     * Called when the syncing state has changed.
     *
     * @param isSyncing Whether the codebase is currently syncing
     */
    fun syncingStateChanged(isSyncing: <PERSON><PERSON><PERSON>)

    fun notSyncedFilesChanged(notSyncedFiles: Int)

    fun totalFilesChanged(totalFiles: Int)

    /**
     * Called when the current syncing file list has changed.
     *
     * @param currentFileList List of files currently being synced
     */
    fun currentFileListChanged(currentFileList: List<String>)

    companion object {
        /**
         * Topic used to subscribe to codebase sync status change events.
         */
        val TOPIC = Topic.create("CodeBase Sync Status Change", CodeBaseSyncStatusListener::class.java)
    }
} 