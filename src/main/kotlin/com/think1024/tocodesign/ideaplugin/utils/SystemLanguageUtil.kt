package com.think1024.tocodesign.ideaplugin.utils
import com.intellij.DynamicBundle;
import java.util.*

/**
 * Utility class for getting system language information.
 */
object SystemLanguageUtil {

    /**
     * Gets the current system language.
     *
     * @return A Locale object representing the current system language
     */
    fun getSystemLanguage(): Locale {
        val locale = DynamicBundle.getLocale()
        if (locale != null) {
            return locale
        }
        return Locale.getDefault()
    }

    /**
     * Gets the language code of the current system language.
     *
     * @return A String representing the language code (e.g., "en" for English, "zh" for Chinese)
     */
    fun getSystemLanguageCode(): String {
        return getSystemLanguage().language
    }

    /**
     * Gets the display name of the current system language.
     *
     * @return A String representing the display name of the language (e.g., "English", "中文")
     */
    fun getSystemLanguageDisplayName(): String {
        return getSystemLanguage().displayLanguage
    }

    /**
     * Checks if the current system language is English.
     *
     * @return true if the system language is English, false otherwise
     */
    fun isEnglish(): Boolean {
        return getSystemLanguageCode().startsWith("en")
    }

    /**
     * Checks if the current system language is Chinese.
     *
     * @return true if the system language is Chinese, false otherwise
     */
    fun isChinese(): Boolean {
        return getSystemLanguageCode().startsWith("zh")
    }
}
