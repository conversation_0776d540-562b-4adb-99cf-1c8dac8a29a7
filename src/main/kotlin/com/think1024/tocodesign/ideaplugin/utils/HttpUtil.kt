// File: src/main/kotlin/com/think1024/tocodesign/ideaplugin/utils/HttpUtil.kt

package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpCookie
import java.net.HttpURLConnection
import java.net.URL

/**
 * Utility object for making HTTP requests.
 * Supports various HTTP methods, custom headers, cookies, and request body.
 */
object HttpUtil {
    private val logger = Logger.getInstance(HttpUtil::class.java)
    /**
     * Performs an HTTP request with the specified method, URL, cookies, and optional body.
     *
     * @param method The HTTP method (GET, POST, PUT, DELETE, etc.)
     * @param urlString The URL to send the request to
     * @param cookies List of cookies to include in the request
     * @param body The request body (for POST, PUT requests)
     * @param headers Additional headers to include in the request
     * @return The response as a string
     * @throws Exception if the request fails or returns an error status code
     */
    fun request(
        method: String,
        urlString: String,
        cookies: List<HttpCookie>,
        body: String? = null,
        headers: Map<String, String> = emptyMap(),
        timeout: Int = 0
    ): String? {
        val url = URL(urlString)
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = method
        if (timeout > 0) {
            connection.connectTimeout = timeout * 1000
        }
        connection.setRequestProperty("Cookie", cookies.joinToString("; ") { "${it.name}=${it.value}" })

        // Set additional headers
        headers.forEach { (key, value) -> connection.setRequestProperty(key, value) }

        // Set request body for POST, PUT, etc.
        if (body != null && (method == "POST" || method == "PUT")) {
            connection.doOutput = true
            connection.setRequestProperty("Content-Type", "application/json")
            OutputStreamWriter(connection.outputStream).use { it.write(body) }
        }

        try {
            val responseCode = connection.responseCode
            val inputStream = if (responseCode >= 400) connection.errorStream else connection.inputStream
            val reader = BufferedReader(InputStreamReader(inputStream))
            val response = StringBuilder()
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                response.append(line)
            }
            reader.close()

            if (responseCode >= 400) {
                logger.warn("HTTP $method request failed. Response Code: $responseCode, Body: $response")
                return null
            }

            return response.toString()
        } finally {
            connection.disconnect()
        }
    }

    /**
     * Performs a GET request.
     *
     * @param urlString The URL to send the GET request to
     * @param cookies List of cookies to include in the request
     * @param headers Additional headers to include in the request
     * @return The response as a string
     */
    fun get(urlString: String, cookies: List<HttpCookie>, headers: Map<String, String> = emptyMap(), timeout: Int = 0) =
        request("GET", urlString, cookies, headers = headers, timeout = timeout)

    /**
     * Performs a POST request.
     *
     * @param urlString The URL to send the POST request to
     * @param cookies List of cookies to include in the request
     * @param body The request body
     * @param headers Additional headers to include in the request
     * @return The response as a string
     */
    fun post(urlString: String, cookies: List<HttpCookie>, body: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0) =
        request("POST", urlString, cookies, body, headers = headers, timeout = timeout)

    /**
     * Performs a PUT request.
     *
     * @param urlString The URL to send the PUT request to
     * @param cookies List of cookies to include in the request
     * @param body The request body
     * @param headers Additional headers to include in the request
     * @return The response as a string
     */
    fun put(urlString: String, cookies: List<HttpCookie>, body: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0) =
        request("PUT", urlString, cookies, body, headers = headers, timeout = timeout)

    /**
     * Performs a DELETE request.
     *
     * @param urlString The URL to send the DELETE request to
     * @param cookies List of cookies to include in the request
     * @param headers Additional headers to include in the request
     * @return The response as a string
     */
    fun delete(urlString: String, cookies: List<HttpCookie>, headers: Map<String, String> = emptyMap(), timeout: Int = 0) =
        request("DELETE", urlString, cookies, headers = headers, timeout = timeout)

    /**
     * Performs a GET request with cookies provided as a string.
     *
     * @param urlString The URL to send the GET request to
     * @param cookiesString Cookies as a string (e.g., "name1=value1; name2=value2")
     * @param headers Additional headers to include in the request
     * @return The response as a string
     */
    fun get(urlString: String, cookiesString: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0): String? {
        val cookies = parseCookies(cookiesString)
        return get(urlString, cookies, headers, timeout)
    }

    fun post(urlString: String, cookiesString: String, body: String, headers: Map<String, String> = emptyMap(), timeout: Int = 0): String? {
        val cookies = parseCookies(cookiesString)
        return post(urlString, cookies, body, headers, timeout)
    }

    /**
     * Parses a cookie string into a list of HttpCookie objects.
     *
     * @param cookiesString Cookies as a string (e.g., "name1=value1; name2=value2")
     * @return List of HttpCookie objects
     */
    fun parseCookies(cookiesString: String): List<HttpCookie> {
        return cookiesString.split(";").mapNotNull { cookieString ->
            val parts = cookieString.trim().split("=", limit = 2)
            if (parts.size == 2) {
                HttpCookie(parts[0], parts[1])
            } else {
                null
            }
        }
    }

    /**
     * Checks if the specified URL is accessible.
     *
     * @param urlString the URL string to check
     * @return true if the URL is accessible; false otherwise
     */
    fun isUrlAccessible(urlString: String): Boolean {
        return try {
            val url = URL(urlString)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "HEAD"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            val responseCode = connection.responseCode
            responseCode in 200..399
        } catch (e: Exception) {
            false
        }
    }
}
