package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vcs.changes.ChangeListManager
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vcs.ProjectLevelVcsManager

class ChangedFilesUtil {
    companion object {
        private val LOG = Logger.getInstance(ChangedFilesUtil::class.java)

        fun getChangedJavaFiles(project: Project): List<VirtualFile> {
            val changeListManager = ChangeListManager.getInstance(project)
            val vcsManager = ProjectLevelVcsManager.getInstance(project)

            if (!vcsManager.hasActiveVcss()) {
                LOG.info("No active VCS found in project")
                return emptyList()
            }

            return changeListManager.allChanges
                .mapNotNull { it.virtualFile }
                .filter { file ->
                    file.isValid && file.extension == "java"
                }
                .also { files ->
                    LOG.info("Found ${files.size} changed Java files")
                    files.forEach { LOG.debug("Changed file: ${it.path}") }
                }
        }
    }
}