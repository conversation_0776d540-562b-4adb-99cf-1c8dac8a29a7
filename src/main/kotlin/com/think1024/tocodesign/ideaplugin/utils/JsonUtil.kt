package com.think1024.tocodesign.ideaplugin.utils

import com.google.gson.JsonObject

/**
 * 从JsonObject安全获取字符串值，当键不存在或值为null时返回null
 */
fun JsonObject.getStringOrNull(key: String): String? {
    return if (this.has(key) && !this.get(key).isJsonNull) {
        this.get(key).asString
    } else {
        null
    }
}

fun JsonObject.getIntOrNull(key: String): Int? {
    return if (this.has(key) && !this.get(key).isJsonNull) {
        this.get(key).asInt
    } else {
        null
    }
}

/**
 * 从JsonObject安全获取布尔值，当键不存在或值为null时返回null
 */
fun JsonObject.getBooleanOrNull(key: String): Boolean? {
    return if (this.has(key) && !this.get(key).isJsonNull) {
        this.get(key).asBoolean
    } else {
        null
    }
}