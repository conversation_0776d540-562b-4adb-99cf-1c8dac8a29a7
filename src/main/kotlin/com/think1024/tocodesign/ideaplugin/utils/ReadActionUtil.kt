package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger

object ReadActionUtil {
    /**
     * Runs the given action within a read action context.
     * This method ensures that the provided action is executed within a read action, preventing potential exceptions.
     *
     * @param action The action to be executed within a read action context.
     * @return The result of the action execution.
     */
    fun <T> runReadAction(action: () -> T): T {
        val logger = Logger.getInstance(ReadActionUtil::class.java)

        return try {
            ApplicationManager.getApplication().runReadAction<T> { action() }
        } catch (e: Exception) {
            logger.warn("Error executing read action", e)
            throw e // Re-throw the exception for further handling if needed
        }
    }
}