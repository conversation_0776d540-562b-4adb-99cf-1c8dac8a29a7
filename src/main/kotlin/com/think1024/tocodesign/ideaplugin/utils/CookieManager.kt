package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.intellij.ui.jcef.JBCefApp
import com.intellij.ui.jcef.JBCefBrowser
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import org.cef.network.CefCookieManager

/**
 * Utility object for managing cookies within the plugin.
 * This object provides methods to store and retrieve cookies using the application's plugin settings.
 */
object CookieManager {
    private val logger = Logger.getInstance(CookieManager::class.java)
    private var browser: JBCefBrowser? = null
    // Constant for the U_TOKEN cookie
    const val U_TOKEN = "U_TOKEN"

    /**
     * Stores the provided cookies string in the application's plugin settings.
     *
     * @param cookies A string containing the cookies to be stored.
     *                This string is expected to be in a format suitable for HTTP requests,
     *                typically in the form of "name1=value1; name2=value2; ...".
     */
    fun storeCookies(cookies: String) {
        val settings = ApplicationPluginSettings.getInstance()
        settings.cookies = cookies
    }

    /**
     * Retrieves the stored cookies from the application's plugin settings.
     *
     * @return A string containing the stored cookies. If no cookies are stored,
     *         this method will return an empty string.
     */
    fun getCookies(): String {
        return ApplicationPluginSettings.getInstance().cookies
    }

    private fun getOrCreateBrowser(): JBCefBrowser {
        if (browser == null || browser!!.isDisposed) {
            browser = JBCefBrowser()
        }
        return browser!!
    }

    private fun getCefCookieManager(): CefCookieManager? {
        val cefBrowser = getOrCreateBrowser()
        return try {
            val methods = listOf("getCookieManager", "doGetCookieManager")
            for (methodName in methods) {
                try {
                    val method = cefBrowser.javaClass.getMethod(methodName)
                    return method.invoke(cefBrowser) as? CefCookieManager
                } catch (_: NoSuchMethodException) {
                    // Method doesn't exist, try the next one
                }
            }
            // If all methods fail, try to get the global cookie manager
            CefCookieManager.getGlobalManager()
        } catch (e: Exception) {
            logger.error("Failed to get CookieManager", e)
            null
        }
    }

    /**
     * Clears cookies for the specified host and optionally for a specific cookie name.
     * If cookieName is provided, it only deletes that specific cookie for the host.
     * If cookieName is null or empty, it deletes all cookies for the host.
     *
     * @param host The host for which to clear cookies. This is typically a domain name.
     * @param cookieName Optional. If provided, only the cookie with this name will be cleared.
     *                   If null or empty, all cookies for the host will be cleared.
     */
    fun clearCookiesForHost(host: String, cookieName: String? = null) {
        val logger = Logger.getInstance("CookieCleaner")

        // Check if JCEF is supported in the current IDE version
        if (!JBCefApp.isSupported()) {
            logger.warn("JCEF is not supported in this IDE version")
            return
        }

        // Get the cookie manager from the browser instance
        val cookieManager = getCefCookieManager()
        if (cookieManager == null) {
            logger.warn("CefCookieManager is not available")
            return
        }

        try {
            // Visit all cookies and determine if the specified cookie was deleted
            val cookieDeleted = findAndClearCookies(cookieManager, host, cookieName, logger)
            // Flush the changes to the cookie store
            flushCookies(cookieManager, logger)

            // Log if the specific cookie was not found and deleted
            if (cookieName != null && !cookieDeleted) {
                logger.info("Specific cookie $cookieName not found for host $host")
            }
        } catch (e: Exception) {
            // Log any exceptions that occur during the process
            logger.warn("Error clearing cookies for host $host", e)
        }

        // Clear the stored cookies in the application settings
        storeCookies("")
    }

    /**
     * Visits all cookies in the cookie manager and deletes cookies based on the specified host and cookie name.
     * This method checks if the cookie's domain matches the host or the default host configured in the application.
     *
     * @param cookieManager The cookie manager responsible for handling cookies.
     * @param host The host for which to clear cookies.
     * @param cookieName Optional. If provided, only the cookie with this name will be cleared.
     *                   If null or empty, all cookies for the host will be cleared.
     * @param logger The logger instance for logging messages.
     * @return Boolean indicating whether a specific cookie was deleted.
     */
    private fun findAndClearCookies(
        cookieManager: CefCookieManager,
        host: String,
        cookieName: String?,
        logger: Logger
    ): Boolean {
        var cookieDeleted = false
        // Get the default host from configuration
        val defaultHost = ConfigUtil.getProperty("url.host.default")
        val defaultFrontendHost = ConfigUtil.getProperty("url.frontend.host.default")
        // Get the previous host from application settings
        val previousHost = ApplicationPluginSettings.getInstance().previousHost
        val previousFrontendHost = ApplicationPluginSettings.getInstance().previousFrontendHost

        // Visit all cookies in the cookie manager
        cookieManager.visitAllCookies { cookie, _, _, delete ->
            // Check if the cookie is null
            if (cookie == null) {
                logger.warn("Encountered a null cookie, skipping...")
                return@visitAllCookies true // Continue visiting cookies
            }

            // Trim leading dot from the cookie's domain (if present)
            val domain = cookie.domain?.trimStart('.')
            // If the domain is null, skip this cookie
            if (domain == null) {
                logger.info("Cookie domain is null, skipping: ${cookie.name}")
                return@visitAllCookies true // Continue visiting cookies
            }

            // Check if the cookie's domain matches the host or previous host or default host
            if (host.contains(domain) ||
                previousHost?.contains(domain) == true ||
                previousFrontendHost?.contains(domain) == true ||
                defaultHost.contains(domain) ||
                defaultFrontendHost.contains(domain))
            {
                if (cookieName.isNullOrEmpty()) {
                    // Delete all cookies for the specified host
                    delete.set(true) // Mark this cookie for deletion
                    logger.info("Marking cookie for deletion: ${cookie.name} in domain: ${cookie.domain}")
                } else if (cookie.name == cookieName) {
                    // Delete only the specific cookie if the name matches
                    delete.set(true) // Mark this specific cookie for deletion
                    logger.info("Marking specific cookie for deletion: $cookieName in domain: ${cookie.domain}")
                    cookieDeleted = true // Indicate that the specific cookie has been deleted
                    return@visitAllCookies false // Stop visiting cookies as we've found the specific one
                }
            }
            !cookieDeleted // Continue if specific cookie not found yet
        }
        return cookieDeleted // Return whether a specific cookie was deleted
    }

    /**
     * Flushes the cookie store to ensure all changes are saved.
     *
     * @param cookieManager The cookie manager responsible for handling cookies.
     * @param logger The logger instance for logging messages.
     */
    private fun flushCookies(cookieManager: CefCookieManager, logger: Logger) {
        // Flush the cookie store to persist any deletions or additions
        cookieManager.flushStore {
            logger.info("Cookie store flushed") // Log that the cookie store has been flushed
        }
    }

}
