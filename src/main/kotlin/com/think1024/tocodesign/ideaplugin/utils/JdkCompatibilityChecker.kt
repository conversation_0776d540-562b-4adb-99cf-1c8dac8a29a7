package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.module.ModuleManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.util.io.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.jetbrains.idea.maven.model.MavenArtifact
import org.jetbrains.idea.maven.model.MavenArtifactNode
import org.jetbrains.idea.maven.project.MavenProject
import org.jetbrains.idea.maven.project.MavenProjectsManager
import java.io.DataInputStream
import java.io.File
import java.io.IOException
import java.util.*
import java.util.jar.JarFile
import java.util.zip.ZipException

/**
 * Utility object for checking JDK compatibility of project dependencies.
 */
object JdkCompatibilityChecker {
    private val LOG = logger<JdkCompatibilityChecker>()

    // Cache for storing already checked JARs to avoid redundant checks
    private val checkedJars = mutableMapOf<String, String?>()

    // Mapping of JDK versions to their corresponding major versions
    val JDK_VERSION_MAP = mapOf(
        "1.8" to 52,
        "9" to 53,
        "10" to 54,
        "11" to 55,
        "12" to 56,
        "13" to 57,
        "14" to 58,
        "15" to 59,
        "16" to 60,
        "17" to 61,
        "18" to 62,
        "19" to 63,
        "20" to 64,
        "21" to 65
    )

    /**
     * Checks the compatibility of all project JARs with the target JDK version.
     *
     * @param project The current IntelliJ IDEA project
     * @param targetJdkVersion The target JDK version to check compatibility against
     * @return A list of incompatible items (JAR files or classes)
     */
    fun checkCompatibility(project: Project, targetJdkVersion: String): List<String> {
        // Clear the set of checked JARs to ensure a fresh check
        checkedJars.clear()

        // Set to store incompatible items found during the check
        val incompatibleItems = mutableSetOf<String>()

        // Get all JAR files in the project
        val allJars = getAllProjectJars(project)

        // Convert the target JDK version to its major version number
        val targetJdkMajorVersion = convertJdkVersionToMajorVersion(targetJdkVersion)

        // Iterate through all JAR files and check their compatibility
        for (jarPath in allJars) {
            // Check each JAR file and add any incompatible items to the set
            checkJarFile(jarPath, targetJdkMajorVersion)?.let { incompatibleItems.add(it) }
        }

        // Return the list of incompatible items
        return incompatibleItems.toList()
    }

    /**
     * Retrieves all JAR files used in the project.
     *
     * @param project The IntelliJ project to analyze.
     * @return A set of JAR file paths.
     */
    private fun getAllProjectJars(project: Project): Set<String> {
        val jarPaths = Collections.synchronizedSet(mutableSetOf<String>())
        val mavenProjectsManager = MavenProjectsManager.getInstance(project)

        if (mavenProjectsManager.isMavenizedProject) {
            val moduleManager = ModuleManager.getInstance(project)
            val localRepository = getMavenLocalRepository(project)

            runBlocking {
                moduleManager.modules.map { module ->
                    async(Dispatchers.Default) {
                        val mavenProject = mavenProjectsManager.findProject(module)
                        if (mavenProject != null) {
                            jarPaths.addAll(getDirectDependencies(mavenProject, localRepository))
                            jarPaths.addAll(getTransitiveDependencies(mavenProject, localRepository))
                        }
                    }
                }.forEach { it.await() }
            }
        }

        return jarPaths.filter { it.endsWith(".jar", ignoreCase = true) }.toSet()
    }

    /**
     * Retrieves direct dependencies of a Maven project.
     *
     * @param mavenProject The Maven project to analyze.
     * @param localRepository The local Maven repository.
     * @return A set of JAR file paths for direct dependencies.
     */
    private fun getDirectDependencies(mavenProject: MavenProject, localRepository: File): Set<String> {
        return mavenProject.dependencies
            .asSequence()
            .filter { it.isResolved }
            .mapNotNull { getArtifactPath(it, localRepository) }
            .filter { File(it).exists() }
            .toSet()
    }

    /**
     * Retrieves transitive dependencies of a Maven project.
     *
     * @param mavenProject The Maven project to analyze.
     * @param localRepository The local Maven repository.
     * @return A set of JAR file paths for transitive dependencies.
     */
    private fun getTransitiveDependencies(mavenProject: MavenProject, localRepository: File): Set<String> {
        val processedArtifacts = mutableSetOf<String>()
        return mavenProject.dependencyTree
            .asSequence()
            .flatMap { flattenDependencyTree(it, processedArtifacts) }
            .filter { it.artifact.isResolved }
            .map { getArtifactPath(it.artifact, localRepository) }
            .filter { File(it).exists() }
            .toSet()
    }

    /**
     * Flattens the dependency tree, avoiding circular dependencies.
     *
     * @param node The current node in the dependency tree.
     * @param processedArtifacts Set of already processed artifacts to avoid circular dependencies.
     * @return A sequence of MavenArtifactNode objects representing the flattened tree.
     */
    private fun flattenDependencyTree(
        node: MavenArtifactNode,
        processedArtifacts: MutableSet<String>
    ): Sequence<MavenArtifactNode> {
        val artifactKey = "${node.artifact.groupId}:${node.artifact.artifactId}:${node.artifact.version}"
        if (artifactKey in processedArtifacts) {
            return emptySequence()
        }
        processedArtifacts.add(artifactKey)

        return sequenceOf(node) + node.dependencies.asSequence()
            .flatMap { flattenDependencyTree(it, processedArtifacts) }
    }

    /**
     * Gets the file path for a Maven artifact.
     *
     * @param artifact The Maven artifact.
     * @param localRepository The local Maven repository.
     * @return The file path of the artifact JAR.
     */
    private fun getArtifactPath(artifact: MavenArtifact, localRepository: File): String {
        val baseVersion = artifact.version.replace(Regex("-\\d{8}\\.\\d{6}-\\d+$"), "-SNAPSHOT")
        val baseDir = listOf(
            artifact.groupId.replace('.', File.separatorChar),
            artifact.artifactId,
            baseVersion
        ).joinToString(File.separator)
        val basePath = File(localRepository, baseDir)

        val fileName = if (baseVersion.endsWith("-SNAPSHOT")) {
            if (!basePath.exists() || !basePath.isDirectory) {
                LOG.warn("SNAPSHOT directory does not exist or is not a directory: $basePath")
                return FileUtil.toSystemDependentName(
                    File(basePath, "${artifact.artifactId}-${artifact.version}.jar").absolutePath
                )
            }

            basePath.listFiles { file ->
                file.isFile && file.name.startsWith("${artifact.artifactId}-") && file.name.endsWith(".jar")
            }?.maxByOrNull { it.lastModified() }?.name ?: "${artifact.artifactId}-${artifact.version}.jar"
        } else {
            "${artifact.artifactId}-${artifact.version}.jar"
        }

        return FileUtil.toSystemDependentName(File(basePath, fileName).absolutePath)
    }

    /**
     * Gets the local Maven repository path.
     *
     * @param project The IntelliJ project.
     * @return The File object representing the local Maven repository.
     */
    private fun getMavenLocalRepository(project: Project): File {
        val mavenProjectsManager = MavenProjectsManager.getInstance(project)
        val mavenGeneralSettings = mavenProjectsManager.generalSettings
        val localRepositoryPath = mavenGeneralSettings.localRepository

        return if (localRepositoryPath.isNullOrEmpty()) {
            File(System.getProperty("user.home"), ".m2${File.separator}repository")
        } else {
            File(localRepositoryPath)
        }
    }

    /**
     * Checks a JAR file for compatibility with the target JDK version.
     *
     * @param jarPath The path to the JAR file.
     * @param targetJdkMajorVersion The target JDK major version.
     * @return A string describing the incompatibility if found, null otherwise.
     */
    private fun checkJarFile(jarPath: String, targetJdkMajorVersion: Int): String? {
        checkedJars[jarPath]?.let { return it }

        try {
            JarFile(jarPath).use { jarFile ->
                val entries = jarFile.entries()
                while (entries.hasMoreElements()) {
                    val entry = entries.nextElement()
                    if (entry.name.endsWith(".class")) {
                        jarFile.getInputStream(entry).use { inputStream ->
                            val dis = DataInputStream(inputStream)
                            if (dis.readInt() == 0xCAFEBABE.toInt()) {
                                dis.readUnsignedShort() // minor version
                                val majorVersion = dis.readUnsignedShort()
                                if (majorVersion > targetJdkMajorVersion) {
                                    val jarJdkVersion = convertMajorVersionToJdk(majorVersion)
                                    val result = "${File(jarPath).name}: $jarJdkVersion"
                                    checkedJars[jarPath] = result
                                    return result
                                }
                                checkedJars[jarPath] = null
                                return null // We only need to check one class file
                            }
                        }
                    }
                }
            }
        } catch (e: IOException) {
            LOG.warn("Failed to read JAR file: $jarPath", e)
        } catch (e: ZipException) {
            LOG.warn("Invalid JAR file: $jarPath", e)
        }
        checkedJars[jarPath] = null
        return null
    }

    /**
     * Converts a JDK major version number to a JDK version string.
     *
     * @param majorVersion The JDK major version number.
     * @return The corresponding JDK version string.
     */
    private fun convertMajorVersionToJdk(majorVersion: Int): String {
        return JDK_VERSION_MAP.entries.find { it.value == majorVersion }?.key ?: "Unknown"
    }

    /**
     * Converts a JDK version string to its corresponding major version number.
     *
     * @param jdkVersion The JDK version string.
     * @return The corresponding major version number.
     * @throws IllegalArgumentException if the JDK version is unsupported.
     */
    private fun convertJdkVersionToMajorVersion(jdkVersion: String): Int {
        return JDK_VERSION_MAP[jdkVersion]
            ?: JDK_VERSION_MAP[jdkVersion.takeWhile { it.isDigit() }]
            ?: throw IllegalArgumentException("Unsupported JDK version: $jdkVersion")
    }

    /**
     * Checks if the given JDK version string is supported.
     *
     * @param jdkVersion The JDK version string to validate.
     * @return true if the JDK version is supported, false otherwise.
     */
    fun isSupportedJdkVersion(jdkVersion: String): Boolean {
        return try {
            convertJdkVersionToMajorVersion(jdkVersion)
            true
        } catch (e: IllegalArgumentException) {
            LOG.warn("Unsupported JDK version: $jdkVersion", e)
            false
        }
    }

    /**
     * Checks if the given JDK version is supported and shows a message dialog if it's not.
     *
     * @param project The current project.
     * @param jdkVersion The JDK version to check.
     * @return true if the JDK version is supported, false otherwise.
     */
    fun checkAndNotifySupportedJdkVersion(project: Project, jdkVersion: String): Boolean {
        if (!isSupportedJdkVersion(jdkVersion)) {
            Messages.showErrorDialog(
                project,
                getI18nString("jdk.version.unsupported.message"),
                getI18nString("jdk.version.unsupported.title")
            )
            return false
        }
        return true
    }
}
