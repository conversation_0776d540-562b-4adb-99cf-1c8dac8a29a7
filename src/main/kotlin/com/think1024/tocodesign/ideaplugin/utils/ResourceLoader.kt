package com.think1024.tocodesign.ideaplugin.utils

import net.jpountz.xxhash.XXHashFactory
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption

object ResourceLoader {
    private fun computeHashFile(path: String): Long {
        val file = FileUtil.getFile(path) ?: return 0
        return computeHashStream(FileInputStream(file))
    }

    private fun computeHashStream(inputStream: InputStream?, maxBytesToRead: Long = 1048576): Long {
        if (inputStream == null) {
            return 0
        }
        val seed = 0x9747b28c
        val hash64 = XXHashFactory.fastestInstance().newStreamingHash64(seed)
        inputStream.use { input ->
            val buffer = ByteArray(8192)
            var bytesRead: Int
            var totalBytesRead: Long = 0
            while (input.read(buffer).also { bytesRead = it } != -1) {
                // 如果已经读取的总字节数加上当前读取的字节数超过1MB
                if (totalBytesRead + bytesRead > maxBytesToRead) {
                    // 只更新哈希值到1MB的限制
                    val remainingBytes = (maxBytesToRead - totalBytesRead).toInt()
                    if (remainingBytes > 0) {
                        hash64.update(buffer, 0, remainingBytes)
                    }
                    break // 达到1MB限制，停止读取
                } else {
                    hash64.update(buffer, 0, bytesRead)
                    totalBytesRead += bytesRead
                }
            }
        }
        return hash64.value
    }

    private fun releaseStream(stream: InputStream?, path: Path) {
        // 如果文件不存在，从资源中提取
        stream?.use { input ->
            Files.copy(input, path, StandardCopyOption.REPLACE_EXISTING)
        } ?: throw IOException("file not found $path")
    }

    fun extractResource(resourcePath: String, targetPath: String): Boolean {
        val stream = ResourceLoader::class.java.getResourceAsStream(resourcePath)
        val extractPath = Path.of(targetPath)
        if (Files.exists(extractPath)) {
            // 如果文件存在对比hash，如果hash不一样则重新提取
            val hash1 = computeHashFile(targetPath.toString())
            val hash2 = computeHashStream(stream)
            if (hash1 != hash2) {
                val stream = ResourceLoader::class.java.getResourceAsStream(resourcePath)
                releaseStream(stream, extractPath)
                return true
            }
        } else {
            // 如果文件不存在，从资源中提取
            releaseStream(stream, extractPath)
            return true
        }
        return false
    }
}