package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.think1024.tocodesign.ideaplugin.services.NotificationService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings.Companion.PROJECT_CONFIG_FILE
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings.Companion.PROJECT_ID_KEY
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings.Companion.PROJECT_NAME_KEY
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.io.FileInputStream
import java.io.IOException
import java.nio.file.Paths
import java.util.*

/**
 * Utility object for managing project configuration files.
 * This object provides methods for reading, caching, and retrieving project configuration data.
 */
object ProjectConfigUtil {
    private val logger = Logger.getInstance(ProjectConfigUtil::class.java)
    private const val MAX_SEARCH_DEPTH = 5

    /**
     * Reads the project configuration from a specified file.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file to read.
     * @return A map of configuration properties, or null if the file couldn't be read.
     */
    fun readProjectConfig(project: Project, fileName: String): Map<String, String>? {
        logger.debug("Reading project config for file: $fileName")
        val settings = ProjectPluginSettings.getInstance(project)

        // Check if the config is already cached
        settings.configCache[fileName]?.let {
            logger.debug("Config found in cache for file: $fileName")
            return it
        }

        val projectRoot = project.getProjectRoot()
        if (projectRoot == null) {
            logger.warn("Unable to determine project root for file: $fileName")
            NotificationService.instance.notifyError(
                getI18nString("notification.error.project.root.not.found.title"),
                getI18nString("notification.error.project.root.not.found.message"),
                project
            )
            clearCaches(project, fileName)
            return null
        }

        projectRoot.refresh(false, true)
        val configFile = findConfigFile(project, projectRoot, fileName)

        if (configFile == null) {
            logger.warn("Unable to find configuration file: $fileName")
            NotificationService.instance.notifyError(
                getI18nString("notification.error.config.not.found.title"),
                getI18nString("notification.error.config.not.found.message", fileName),
                project
            )
            clearCaches(project, fileName)
            return null
        }

        return try {
            val properties = Properties()
            configFile.inputStream.use { stream ->
                properties.load(stream)
            }
            val config = properties.stringPropertyNames().associateWith { properties.getProperty(it) }.toMutableMap()

            updateCaches(project, fileName, config, configFile.path)

            logger.debug("Successfully read and cached config for file: $fileName")
            config
        } catch (e: IOException) {
            logger.error("Error reading configuration file: $fileName", e)
            NotificationService.instance.notifyWarning(
                getI18nString("notification.warning.config.read.error.title"),
                getI18nString("notification.warning.config.read.error.message", e.message ?: ""),
                project
            )
            clearCaches(project, fileName)
            null
        }
    }

    /**
     * Updates various caches with the newly read configuration data.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file.
     * @param config The configuration data.
     * @param filePath The path of the configuration file.
     */
    private fun updateCaches(project: Project, fileName: String, config: MutableMap<String, String>, filePath: String) {
        val settings = ProjectPluginSettings.getInstance(project)
        settings.configCache[fileName] = config
        settings.updateFilePathCache(fileName, filePath)
        settings.projectId = config[PROJECT_ID_KEY]
        settings.projectName = config[PROJECT_NAME_KEY]
    }

    /**
     * Clears all caches related to a specific configuration file.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file.
     */
    fun clearCaches(project: Project, fileName: String) {
        val settings = ProjectPluginSettings.getInstance(project)
        settings.clearConfigCache(fileName)
        settings.clearFilePathCache(fileName)
        settings.projectId = null
        settings.projectName = null
        settings.bubbleMessagesMap.clear()
    }

    /**
     * Gets the project root directory as a VirtualFile.
     *
     * @return The project root VirtualFile, or null if it can't be determined.
     */
    private fun Project.getProjectRoot(): VirtualFile? {
        val projectDir = this.basePath
        if (projectDir == null) {
            logger.warn("Unable to determine base path for project")
            return null
        }
        return VirtualFileManager.getInstance().findFileByUrl("file://$projectDir")
    }

    /**
     * Searches for a configuration file in the project directory and its subdirectories.
     *
     * @param project The current project.
     * @param dir The starting directory for the search.
     * @param fileName The name of the configuration file to find.
     * @return The found configuration file as a VirtualFile, or null if not found.
     */
    private fun findConfigFile(project: Project, dir: VirtualFile, fileName: String): VirtualFile? {
        logger.debug("Searching for config file: $fileName in directory: ${dir.path}")
        val settings = ProjectPluginSettings.getInstance(project)

        // Check if the file path is cached
        settings.filePathCache[fileName]?.let { cachedPath ->
            logger.debug("Config file path found in cache: $cachedPath")
            return VirtualFileManager.getInstance().findFileByUrl("file://$cachedPath")
        }

        val queue: Queue<Pair<VirtualFile, Int>> = LinkedList()
        queue.offer(Pair(dir, 0))

        while (queue.isNotEmpty()) {
            val (currentDir, depth) = queue.poll()

            if (depth > MAX_SEARCH_DEPTH) {
                logger.debug("Reached max search depth for file: $fileName in directory: ${currentDir.path}")
                continue
            }

            val file = currentDir.findChild(fileName)
            if (file != null && !file.isDirectory) {
                logger.debug("Found config file: ${file.path}")
                settings.updateFilePathCache(fileName, file.path)
                return file
            }

            for (child in currentDir.children) {
                if (child.isDirectory) {
                    queue.offer(Pair(child, depth + 1))
                }
            }
        }

        logger.warn("Unable to find config file: $fileName")
        return null
    }

    /**
     * Retrieves a specific property from the project configuration.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file.
     * @param propertyName The name of the property to retrieve.
     * @return The value of the property, or null if not found.
     */
    fun getProjectConfigProperty(project: Project, fileName: String, propertyName: String): String? {
        logger.debug("Getting project config property: $propertyName from file: $fileName")
        return readProjectConfig(project, fileName)?.get(propertyName)
    }

    /**
     * Clears all caches for the project, or for a specific file if provided.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file (optional).
     */
    fun clearCache(project: Project, fileName: String? = null) {
        logger.debug("Clearing cache${if (fileName != null) " for file: $fileName" else ""}")
        val settings = ProjectPluginSettings.getInstance(project)
        settings.clearConfigCache(fileName)
        settings.clearFilePathCache(fileName)
        settings.projectId = null
        settings.projectName = null
    }

    /**
     * Updates a cached property for a specific configuration file.
     *
     * @param project The current project.
     * @param fileName The name of the configuration file.
     * @param propertyName The name of the property to update.
     * @param propertyValue The new value for the property.
     */
    fun updateCachedProperty(project: Project, fileName: String, propertyName: String, propertyValue: String) {
        logger.debug("Updating cached property: $propertyName in file: $fileName")
        ProjectPluginSettings.getInstance(project).updateCachedProperty(fileName, propertyName, propertyValue)
    }

    private fun getConfig(configFilePath: String): Map<String, String>? {
        // 必须使用File读取，使用VirtualFile会出现和文件系统中的文件不一致的情况
        val configFile = FileUtil.getFile(configFilePath) ?: return null

        return try {
            val properties = Properties()
            FileInputStream(configFile).use { stream ->
                properties.load(stream)
            }
            val config = properties.stringPropertyNames().associateWith { properties.getProperty(it) }.toMutableMap()
            config
        } catch (_: IOException) {
            null
        }
    }

    fun getProjectInfo(projectWorkFolder: String): Map<String, String>? {
        val configFilePath = Paths.get(projectWorkFolder, PROJECT_CONFIG_FILE).toString()
        return getConfig(configFilePath)
    }

    fun getModuleInfo(moduleFolder: String): Map<String, String>? {
        val configFilePath = Paths.get(moduleFolder, "module").toString()
        return getConfig(configFilePath)
    }
}
