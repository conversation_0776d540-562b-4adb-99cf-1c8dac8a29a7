package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import java.io.BufferedReader
import java.io.InputStreamReader
import java.text.MessageFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * LanguageManager is responsible for managing internationalization (i18n) in the plugin.
 * It provides functionality to load and retrieve localized strings from properties files.
 * This object supports multiple languages and allows for dynamic string formatting.
 */
object LanguageManager {
    private val logger = Logger.getInstance(LanguageManager::class.java)
    private val resources = ConcurrentHashMap<String, ResourceBundle>()

    /**
     * Custom ResourceBundle.Control that uses UTF-8 encoding to read properties files.
     * This allows for proper handling of non-ASCII characters in the localization files.
     */
    private object UTF8Control : ResourceBundle.Control() {
        override fun newBundle(
            baseName: String,
            locale: Locale,
            format: String,
            loader: ClassLoader,
            reload: Boolean
        ): ResourceBundle? {
            val bundleName = toBundleName(baseName, locale)
            val resourceName = toResourceName(bundleName, "properties")

            return loader.getResourceAsStream(resourceName)?.use { stream ->
                try {
                    InputStreamReader(stream, Charsets.UTF_8).use { reader ->
                        PropertyResourceBundle(BufferedReader(reader))
                    }
                } catch (e: Exception) {
                    logger.warn("Failed to load resource bundle: $resourceName", e)
                    null
                }
            }
        }
    }

    /**
     * Retrieves a localized string for the given key.
     *
     * @param key The key of the string to retrieve.
     * @return The localized string if found, or the key itself if not found.
     */
    fun getI18nString(key: String): String {
        return getLocalizedString(key)
    }

    /**
     * Retrieves a localized string for the given key and formats it with the provided arguments.
     * This method uses MessageFormat to insert the arguments into the localized string.
     *
     * @param key The key of the string to retrieve.
     * @param args Variable number of arguments to be inserted into the localized string.
     * @return The localized and formatted string if found, or the key itself if not found.
     */
    fun getI18nString(key: String, vararg args: String): String {
        val localizedString = getLocalizedString(key)
        return try {
            MessageFormat.format(localizedString, *args)
        } catch (e: IllegalArgumentException) {
            logger.warn("Failed to format localized string for key: $key", e)
            localizedString
        }
    }

    /**
     * Internal method to retrieve a localized string for the given key.
     * This method handles the locale selection and resource bundle loading.
     *
     * @param key The key of the string to retrieve.
     * @return The localized string if found, or the key itself if not found.
     */
    private fun getLocalizedString(key: String): String {
        val settings = ApplicationPluginSettings.getInstance()
        val locale = when (settings.language) {
            "English" -> Locale.ENGLISH
            "简体中文" -> Locale("zh", "CN")
            "System Default" -> getSystemDefaultLocale()
            else -> {
                logger.info("Unsupported language setting: ${settings.language}. Using English.")
                Locale.ENGLISH
            }
        }

        // Load resource bundle for the specified locale
        val localizedBundle = getResourceBundleForLocale(locale)

        // Attempt to get the localized string
        val localizedString = localizedBundle?.getStringOrNull(key)

        return localizedString ?: run {
            logger.warn("Missing translation for key: $key in locale: $locale. Falling back to English.")
            // Fallback to English if key not found
            val englishBundle = getResourceBundleForLocale(Locale.ENGLISH)
            englishBundle?.getStringOrNull(key) ?: key
        }
    }

    /**
     * Helper method to load the resource bundle for a given locale.
     *
     * @param locale The locale for which to load the resource bundle.
     * @return The loaded resource bundle or null if it fails.
     */
    private fun getResourceBundleForLocale(locale: Locale): ResourceBundle? {
        if (!resources.containsKey(locale.toString())) {
            logger.info("Loading resource bundle for locale: $locale")
            try {
                resources[locale.toString()] = ResourceBundle.getBundle("messages/messages", locale, UTF8Control)
            } catch (e: Exception) {
                logger.warn("Failed to load resource bundle for locale: $locale", e)
                return null
            }
        }
        return resources[locale.toString()]
    }

    /**
     * Extension function to safely get a string from a resource bundle.
     *
     * @param key The key to look up.
     * @return The string value or null if not found.
     */
    private fun ResourceBundle.getStringOrNull(key: String): String? {
        return try {
            getString(key)
        } catch (e: MissingResourceException) {
            logger.warn("Missing resource for key: $key", e)
            null
        }
    }


    /**
     * Determines the system default locale based on the system language.
     * This method uses SystemLanguageUtil to detect the system language.
     *
     * @return The appropriate Locale based on the system language.
     */
    private fun getSystemDefaultLocale(): Locale {
        return when {
            SystemLanguageUtil.isEnglish() -> Locale.ENGLISH
            SystemLanguageUtil.isChinese() -> Locale("zh", "CN")
            else -> {
                logger.info("System language is neither English nor Chinese. Using English as default.")
                Locale.ENGLISH
            }
        }
    }
}
