package com.think1024.tocodesign.ideaplugin.utils

import java.util.regex.Pattern
import com.intellij.openapi.util.Key

/**
 * Global constants for the plugin.
 */
object Constants {
    /**
     * Regular expression pattern for validating Host URLs.
     */
    val HOST_URL_PATTERN: Pattern =
        Pattern.compile("^(https?://)([\\w.-]+|\\d{1,3}(\\.\\d{1,3}){3})(:[0-9]{1,5})?$")

    // You can add other global constants here as needed
}

// 用于存储目标文件路径的 key
val FIX_COMPILER_FILES_KEY = Key<Set<String>>("FIX_COMPILER_FILES_KEY")
