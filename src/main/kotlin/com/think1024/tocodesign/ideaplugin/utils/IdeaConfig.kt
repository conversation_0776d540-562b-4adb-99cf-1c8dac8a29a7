package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.ide.ui.LafManager
import com.intellij.ide.ui.laf.UIThemeLookAndFeelInfo
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.colors.EditorColorsScheme
import com.intellij.ui.ColorUtil


class IdeaConfig {

    companion object {
        fun getEditorTheme(scheme: EditorColorsScheme?): String {
            if (scheme == null) {
                return "DARK"
            }
            return if (ColorUtil.isDark(scheme.defaultBackground)) {
                "DARK"
            } else {
                "DEFAULT"
            }
        }

        fun getTheme(theme: UIThemeLookAndFeelInfo): String {
            return if (theme.isDark) {
                "DARK"
            } else {
                "DEFAULT"
            }
        }

        fun getCurrentEditorTheme(): String {
            return if (EditorColorsManager.getInstance().isDarkEditor) {
                "DARK"
            } else {
                "DEFAULT"
            }
        }

        fun getCurrentTheme(): String {
            return getTheme(LafManager.getInstance().currentUIThemeLookAndFeel)
        }

        fun getLang(): String {
            return if (SystemLanguageUtil.isChinese()) {
                "zh-CN"
            } else {
                "en-US"
            }
        }
    }
}