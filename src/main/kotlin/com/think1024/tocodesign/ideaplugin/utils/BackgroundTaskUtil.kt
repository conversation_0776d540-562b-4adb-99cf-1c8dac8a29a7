package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project

/**
 * Utility class for executing background tasks.
 */
object BackgroundTaskUtil {

    private val logger = Logger.getInstance(BackgroundTaskUtil::class.java)

    /**
     * Executes a background task with the given parameters.
     *
     * @param project The project in which the task is executed.
     * @param taskName The name of the task that will be displayed in the UI.
     * @param action The action to be executed in the background. It should return a result of type T.
     * @param onSuccess A callback function that will be invoked if the action completes successfully.
     *                  It receives the result of type T.
     * @param onFailure A callback function that will be invoked if an exception occurs during the action.
     *                  It receives the thrown exception.
     */
    fun <T> executeInBackground(
        project: Project,
        taskName: String,
        action: () -> T,
        onSuccess: (T) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        object : Task.Backgroundable(project, taskName, true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    // Check for task cancellation
                    if (indicator.isCanceled) {
                        logger.info("Task $taskName was cancelled.")
                        return
                    }

                    // Execute the action and get the result
                    val result = action()

                    // Execute onSuccess in a pooled thread, ensure any UI update is done properly
                    ApplicationManager.getApplication().executeOnPooledThread {
                        try {
                            onSuccess(result)
                        } catch (e: Exception) {
                            logger.warn("Error in onSuccess callback", e)
                        }
                    }
                } catch (e: Exception) {
                    logger.warn("Error executing background task", e)

                    // Execute onFailure in a pooled thread, ensure any UI update is done properly
                    ApplicationManager.getApplication().executeOnPooledThread {
                        try {
                            onFailure(e)
                        } catch (ex: Exception) {
                            logger.warn("Error in onFailure callback", ex)
                        }
                    }
                }
            }
        }.queue()
    }
}