package com.think1024.tocodesign.ideaplugin.utils

import java.net.URI
import java.net.URL

fun URL.setQueryParams(params: Map<String, String>): URL {
    // 将 URL 转换为 URI
    val uri = this.toURI()

    // 构建查询字符串
    val queryString = params.entries.joinToString("&") { (key, value) ->
        "${java.net.URLEncoder.encode(key, "UTF-8")}=${java.net.URLEncoder.encode(value, "UTF-8")}"
    }

    // 创建新的 URI
    val newUri = URI(
        uri.scheme,
        uri.userInfo,
        uri.host,
        uri.port,
        uri.path,
        queryString,
        uri.fragment
    )

    // 转换为 URL
    return newUri.toURL()
}