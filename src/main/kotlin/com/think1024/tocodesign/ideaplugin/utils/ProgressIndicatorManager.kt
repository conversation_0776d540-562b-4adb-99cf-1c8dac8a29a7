package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.progress.Task
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.project.Project

class ProgressIndicator {
    private var progressIndicator: ProgressIndicator? = null
    private var showing = false

    fun show(project: Project, title: String, canBeCancelled: Boolean) {
        hide()
        showing = true
        val modalTask = object : Task.Modal(project, title, canBeCancelled) {
            override fun run(indicator: ProgressIndicator) {
                if (showing) {
                    showing = false
                    progressIndicator = indicator
                    while (!indicator.isCanceled) {
                        Thread.sleep(100)
                    }
                }
            }

            override fun onCancel() {
                progressIndicator = null
            }

            override fun onFinished() {
                progressIndicator = null
            }
        }
        ProgressManager.getInstance().run(modalTask)
    }

    fun setText(text: String) {
        progressIndicator?.let {
            it.text = text
        }
    }

    fun setFraction(fraction: Double) {
        progressIndicator?.let {
            it.fraction = fraction
        }
    }

    fun setIndeterminate(isIndeterminate: Boolean) {
        progressIndicator?.let {
            it.isIndeterminate = isIndeterminate
        }
    }

    fun hide() {
        showing = false
        progressIndicator?.let {
            if (!it.isCanceled) {
                it.cancel()
            }
        }
    }
}