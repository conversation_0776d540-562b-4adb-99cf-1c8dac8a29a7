package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import java.io.InputStreamReader
import java.util.*

/**
 * Utility object for reading configuration properties.
 */
object ConfigUtil {
    private val logger = Logger.getInstance(ConfigUtil::class.java)
    private val properties: Properties = Properties()

    init {
        val inputStream = ConfigUtil::class.java.classLoader.getResourceAsStream("config.properties")
        if (inputStream == null) {
            logger.warn("config.properties not found")
        } else {
            try {
                InputStreamReader(inputStream, "UTF-8").use { reader ->
                    properties.load(reader)
                }
                logger.info("Loaded properties: ${properties.stringPropertyNames()}")
            } catch (e: Exception) {
                logger.error("Failed to load config.properties", e)
            }
        }
    }

    /**
     * Gets a property value by its key.
     * @param key The property key.
     * @return The property value, or null if not found.
     */
    fun getProperty(key: String): String {
        return properties.getProperty(key)
    }

    /**
     * Gets an integer property value by its key.
     * @param key The property key.
     * @param defaultValue The default value to return if the property is not found or is not a valid integer.
     * @return The property value as an Int, or the default value if not found or invalid.
     */
    fun getIntProperty(key: String, defaultValue: Int): Int {
        return getProperty(key).toIntOrNull() ?: defaultValue
    }
}