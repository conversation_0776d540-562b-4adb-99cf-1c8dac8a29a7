package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger

/**
 * GlobalExceptionHandler is a singleton object that implements Thread.UncaughtExceptionHandler.
 * It is responsible for handling uncaught exceptions in the application.
 */
object GlobalExceptionHandler : Thread.UncaughtExceptionHandler {
    // Logger instance for logging errors and exceptions
    private val logger = Logger.getInstance(GlobalExceptionHandler::class.java)

    /**
     * This method is called when a thread terminates due to an uncaught exception.
     *
     * @param thread The thread that has thrown the uncaught exception.
     * @param throwable The exception that was thrown.
     */
    override fun uncaughtException(thread: Thread, throwable: Throwable) {
        // Check if the throwable is an instance of Exception
        if (throwable is Exception) {
            // Log the uncaught exception with thread name and exception message
            logger.error("Uncaught exception in thread '${thread.name}': ${throwable.message}", throwable)
        }

        // Optionally: Handle specific types of exceptions or perform any cleanup
    }

    /**
     * Initializes the global exception handler.
     * This method sets this instance as the default uncaught exception handler for all threads.
     */
    fun initialize() {
        // Set this instance as the default uncaught exception handler
        Thread.setDefaultUncaughtExceptionHandler(this)
    }
}
