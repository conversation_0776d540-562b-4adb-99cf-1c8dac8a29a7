package com.think1024.tocodesign.ideaplugin.utils

import org.json.JSONArray
import org.json.JSONObject

/**
 * Utility object for handling JSON conversions with depth control
 */
object JacksonUtil {
    // Maximum depth for JSON parsing to prevent stack overflow on deeply nested structures
    private const val MAX_DEPTH = 5 // Adjust this value based on your needs

    /**
     * Converts a JSONObject to a Map<String, Any?> with depth control
     *
     * @param maxDepth Maximum depth for recursion (default is MAX_DEPTH)
     * @return Map<String, Any?> representation of the JSONObject
     */
    fun JSONObject.toMap(maxDepth: Int = MAX_DEPTH): Map<String, Any?> {
        return toMapInternal(maxDepth, 0)
    }

    /**
     * Internal recursive function to convert JSONObject to Map
     *
     * @param maxDepth Maximum allowed depth for recursion
     * @param currentDepth Current depth in the recursion
     * @return Map<String, Any?> representation of the JSONObject
     */
    private fun JSONObject.toMapInternal(maxDepth: Int, currentDepth: Int): Map<String, Any?> {
        // Check if maximum depth is reached
        if (currentDepth >= maxDepth) {
            return mapOf("_maxDepthReached" to true)
        }

        val map = mutableMapOf<String, Any?>()
        for (keyObj in this.keys()) {
            val key = keyObj.toString()
            when (val value = this.opt(key)) {
                is JSONObject -> map[key] = value.toMapInternal(maxDepth, currentDepth + 1)
                is JSONArray -> map[key] = value.toListInternal(maxDepth, currentDepth + 1)
                JSONObject.NULL -> map[key] = null
                else -> map[key] = value
            }
        }
        return map
    }

    /**
     * Converts a JSONArray to a List<Any?> with depth control
     *
     * @param maxDepth Maximum depth for recursion (default is MAX_DEPTH)
     * @return List<Any?> representation of the JSONArray
     */
    fun JSONArray.toList(maxDepth: Int = MAX_DEPTH): List<Any?> {
        return toListInternal(maxDepth, 0)
    }

    /**
     * Internal recursive function to convert JSONArray to List
     *
     * @param maxDepth Maximum allowed depth for recursion
     * @param currentDepth Current depth in the recursion
     * @return List<Any?> representation of the JSONArray
     */
    private fun JSONArray.toListInternal(maxDepth: Int, currentDepth: Int): List<Any?> {
        // Check if maximum depth is reached
        if (currentDepth >= maxDepth) {
            return listOf(mapOf("_maxDepthReached" to true))
        }

        return (0 until this.length()).map { index ->
            when (val value = this.opt(index)) {
                is JSONObject -> value.toMapInternal(maxDepth, currentDepth + 1)
                is JSONArray -> value.toListInternal(maxDepth, currentDepth + 1)
                JSONObject.NULL -> null
                else -> value
            }
        }
    }
}
