package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings

/**
 * Extension function for Project to easily retrieve ProjectPluginSettings.
 *
 * This function provides a convenient way to access the ProjectPluginSettings
 * service for a given project. It utilizes IntelliJ IDEA's service system
 * to retrieve the singleton instance of ProjectPluginSettings associated
 * with the project.
 *
 * Usage:
 * val settings = project.getPluginSettings()
 *
 * @receiver Project The project for which to retrieve the settings
 * @return ProjectPluginSettings The settings instance for the project
 */
fun Project.getPluginSettings(): ProjectPluginSettings =
    this.getService(ProjectPluginSettings::class.java)