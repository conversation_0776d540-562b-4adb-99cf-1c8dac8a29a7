package com.think1024.tocodesign.ideaplugin.utils

/**
 * Utility object for reading configuration properties.
 */
object MatchUtil {
    /**
     * 模糊匹配并返回匹配位置
     */
    data class FuzzyMatchResult(
        val score: Int,
        val matchedIndices: List<Int> // 匹配字符在目标字符串中的索引
    )

    fun fuzzyMatchWithIndices(query: String, target: String): FuzzyMatchResult {
        if (query.isEmpty()) return FuzzyMatchResult(100, emptyList())
        if (target.isEmpty()) return FuzzyMatchResult(0, emptyList())

        val lowerQuery = query.lowercase()
        val lowerTarget = target.lowercase()

        var score = 0
        var targetIndex = 0
        var lastMatchIndex = -1
        val matchedIndices = mutableListOf<Int>()

        for (queryChar in lowerQuery) {
            var found = false
            for (i in targetIndex until lowerTarget.length) {
                if (lowerTarget[i] == queryChar) {
                    if (i == lastMatchIndex + 1) score += 5 // 连续匹配加分
                    if (i == 0) score += 10 // 起始位置匹配加分
                    if (i > 0 && lowerTarget[i-1] in listOf(' ', '_', '-', '.')) score += 8 // 单词开头匹配加分

                    targetIndex = i + 1
                    lastMatchIndex = i
                    matchedIndices.add(i)
                    found = true
                    break
                }
            }

            if (!found) return FuzzyMatchResult(0, emptyList())
        }

        score += (100 * query.length / target.length.coerceAtLeast(1))
        return FuzzyMatchResult(score, matchedIndices)
    }

    /**
     * 根据匹配位置生成高亮HTML
     */
    fun highlightMatches(target: String, matchedIndices: List<Int>): String {
        if (matchedIndices.isEmpty()) return target

        val result = StringBuilder()
        var lastIndex = 0

        for (index in matchedIndices.sorted()) {
            // 添加非匹配部分
            if (index > lastIndex) {
                result.append(target.substring(lastIndex, index))
            }

            // 添加匹配部分（用<b>包裹）
            result.append("<b>").append(target[index]).append("</b>")
            lastIndex = index + 1
        }

        // 添加剩余部分
        if (lastIndex < target.length) {
            result.append(target.substring(lastIndex))
        }

        return result.toString()
    }
}