package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.StatusBar
import com.intellij.openapi.wm.WindowManager
import com.intellij.util.messages.MessageBusConnection
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorIndexService
import com.think1024.tocodesign.ideaplugin.ui.TocoDesignStatusBarWidget

/**
 * Utility object for managing project resources. Provides methods to disconnect
 * socket connections, message bus connections for a given project. These utilities help prevent resource leaks and ensure
 * efficient resource management when a project or plugin is closed or unloaded.
 */
object ProjectResourceUtil {
    private val logger = Logger.getInstance(ProjectResourceUtil::class.java)

    /**
     * Disconnects the MessageBus connection for the provided project.
     * This operation is crucial to prevent memory leaks as projects close.
     * Logs information about the success or failure of the operation.
     *
     * @param project The project whose MessageBus connection should be disconnected.
     */
    fun disconnectMessageBus(project: Project) {
        try {
            val connection: MessageBusConnection = project.messageBus.connect()
            connection.disconnect()
            logger.info("MessageBus connection successfully disconnected.")
        } catch (e: Exception) {
            logger.warn("Failed to disconnect MessageBus connection.", e)
        }
    }

    /**
     * Disposes the TocoDesignStatusBarWidget for the given project.
     * @param project The project whose status bar widget should be disposed.
     */
    fun disposeTocoDesignStatusBarWidget(project: Project) {
        // Get the status bar for the project
        val statusBar: StatusBar? = WindowManager.getInstance().getStatusBar(project)

        // Find the TocoDesignStatusBarWidget and dispose it
        statusBar?.getWidget(TocoDesignStatusBarWidget.ID)?.let { widget ->
            if (widget is TocoDesignStatusBarWidget) {
                widget.dispose()
            }
        }
    }

    fun cancelProjectIdChangeListenerForProject(project: Project) {
        try {
            val locatorIndexService = project.service<LocatorIndexService>()
            // Ensure the service is available before calling its methods
            if (locatorIndexService != null) {
                locatorIndexService.cancelProjectIdChangeListener()
                logger.info("Project ID change listener cancelled for project: ${project.name}")
            } else {
                logger.warn("LocatorIndexService is not available for project: ${project.name}")
            }
        } catch (e: Exception) {
            logger.warn("Failed to cancel project ID change listener for project: ${project.name}", e)
        }
    }
}
