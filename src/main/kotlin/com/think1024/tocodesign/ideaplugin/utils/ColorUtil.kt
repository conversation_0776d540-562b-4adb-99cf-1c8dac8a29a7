package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.intellij.ui.JBColor
import java.awt.Color

/**
 * Utility class for color-related operations.
 * Provides methods to convert Color objects to strings and vice versa.
 * This can be useful for persisting Color settings as strings in configurations.
 */
object ColorUtil {

    private val logger = Logger.getInstance(ColorUtil::class.java)

    /**
     * Converts a Color object to a string representation.
     * Format: "red,green,blue"
     *
     * @param color the Color object to convert
     * @return a string representation of the color in the format "red,green,blue"
     */
    fun colorToString(color: Color): String {
        return "${color.red},${color.green},${color.blue}"
    }

    /**
     * Converts a string representation of a color back to a Color object.
     * Expects format: "red,green,blue"
     *
     * @param colorString the string representation of the color
     * @return a Color object represented by the string, or a default JBColor.LIGHT_GRAY if parsing fails
     */
    fun stringToColor(colorString: String): Color {
        return try {
            val parts = colorString.split(",").map { it.toInt() }
            require(parts.size == 3) { "Invalid color string format" }
            Color(parts[0], parts[1], parts[2])
        } catch (e: Exception) {
            // Log the error and return a default color
            logger.error("Error parsing color string: $colorString, using default color. Exception: ${e.message}")
            JBColor.LIGHT_GRAY
        }
    }
}