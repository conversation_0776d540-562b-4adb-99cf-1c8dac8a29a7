package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.project.Project
import java.time.Duration
import java.time.Instant

/**
 * Utility class for managing cached values using PropertiesComponent on a per-project basis.
 * This class provides methods to add, retrieve, and manage cached entries with expiration.
 *
 * @property cacheKeyPrefix A prefix used for all cache keys to avoid conflicts with other caches.
 * @property expirationHours The number of hours after which a cache entry is considered expired.
 */
class ProjectPropertyCache(
    private val cacheKeyPrefix: String,
    private val expirationHours: Long
) {
    private val cacheKeysProperty = "$cacheKeyPrefix.keys"

    /**
     * Adds a new cache entry with the current timestamp.
     *
     * @param project The IntelliJ project in which to store the cache entry.
     * @param key The key for the cache entry. It will be prefixed with [cacheKeyPrefix].
     * @param value The value to cache.
     */
    fun addCacheEntry(project: Project, key: String, value: String) {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        val fullKey = "$cacheKeyPrefix.$key"
        propertiesComponent.setValue(fullKey, value)
        propertiesComponent.setValue("$fullKey.timestamp", Instant.now().toEpochMilli().toString())
        addCacheKey(project, fullKey)
    }

    /**
     * Retrieves a cache entry if it exists and is not expired.
     *
     * @param project The IntelliJ project from which to retrieve the cache entry.
     * @param key The key for the cache entry. It will be prefixed with [cacheKeyPrefix].
     * @return The cached value if it exists and is not expired, null otherwise.
     */
    fun getCacheEntry(project: Project, key: String): String? {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        val fullKey = "$cacheKeyPrefix.$key"
        val timestamp = getTimestamp(project, fullKey)

        if (timestamp != null && !isExpired(timestamp)) {
            return propertiesComponent.getValue(fullKey)
        }

        // If the entry is expired or doesn't exist, remove it from the cache
        removeCacheEntry(project, fullKey)
        return null
    }

    /**
     * Removes a cache entry and its associated timestamp.
     *
     * @param project The IntelliJ project from which to remove the cache entry.
     * @param key The key for the cache entry to remove. It will be prefixed with [cacheKeyPrefix].
     */
    fun removeCacheEntry(project: Project, key: String) {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        val fullKey = "$cacheKeyPrefix.$key"
        propertiesComponent.unsetValue(fullKey)
        propertiesComponent.unsetValue("$fullKey.timestamp")
        removeCacheKey(project, fullKey)
    }

    /**
     * Clears all cache entries for the given project.
     *
     * @param project The IntelliJ project whose cache should be cleared.
     */
    fun clearCache(project: Project) {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        val propertyNames = getCacheKeys(project)
        propertyNames.forEach { key ->
            propertiesComponent.unsetValue(key)
            propertiesComponent.unsetValue("$key.timestamp")
        }
        setCacheKeys(project, emptyList())
    }

    /**
     * Checks if a cache entry has expired based on its timestamp.
     *
     * @param timestamp The timestamp of the cache entry in milliseconds since epoch.
     * @return true if the entry has expired, false otherwise.
     */
    private fun isExpired(timestamp: Long): Boolean {
        val now = Instant.now().toEpochMilli()
        val age = Duration.ofMillis(now - timestamp)
        return age.toHours() > expirationHours
    }

    /**
     * Retrieves the timestamp of a cache entry.
     *
     * @param project The IntelliJ project containing the cache entry.
     * @param key The full key (including prefix) of the cache entry.
     * @return The timestamp as a Long, or null if not found.
     */
    private fun getTimestamp(project: Project, key: String): Long? {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        return propertiesComponent.getValue("$key.timestamp")?.toLongOrNull()
    }

    /**
     * Adds a key to the list of cached keys for the project.
     *
     * @param project The IntelliJ project.
     * @param key The full key (including prefix) to add.
     */
    private fun addCacheKey(project: Project, key: String) {
        val existingKeys = getCacheKeys(project).toMutableList()
        if (!existingKeys.contains(key)) {
            existingKeys.add(key)
            setCacheKeys(project, existingKeys)
        }
    }

    /**
     * Removes a key from the list of cached keys for the project.
     *
     * @param project The IntelliJ project.
     * @param key The full key (including prefix) to remove.
     */
    private fun removeCacheKey(project: Project, key: String) {
        val existingKeys = getCacheKeys(project).toMutableList()
        existingKeys.remove(key)
        setCacheKeys(project, existingKeys)
    }

    /**
     * Retrieves the list of all cached keys for the project.
     *
     * @param project The IntelliJ project.
     * @return A list of all cached keys (including prefixes).
     */
    private fun getCacheKeys(project: Project): List<String> {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        return propertiesComponent.getValue(cacheKeysProperty)?.split(",") ?: emptyList()
    }

    /**
     * Sets the list of cached keys for the project.
     *
     * @param project The IntelliJ project.
     * @param keys The list of keys (including prefixes) to set.
     */
    private fun setCacheKeys(project: Project, keys: List<String>) {
        val propertiesComponent = PropertiesComponent.getInstance(project)
        propertiesComponent.setValue(cacheKeysProperty, keys.joinToString(","))
    }
}