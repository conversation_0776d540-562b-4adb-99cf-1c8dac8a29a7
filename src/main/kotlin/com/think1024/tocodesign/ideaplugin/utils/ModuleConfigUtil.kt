package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.module.ModuleManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ModuleRootManager
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiJavaFile
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction
import org.jetbrains.idea.maven.model.MavenId
import org.jetbrains.idea.maven.project.MavenProject
import org.jetbrains.idea.maven.project.MavenProjectsManager
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * Utility object for handling module configurations, specifically for Maven projects.
 * It provides functionalities to find properties from configuration files and manage caching.
 */
object ModuleConfigUtil {
    private val logger = Logger.getInstance(ModuleConfigUtil::class.java)
    private const val CACHE_PREFIX = "MavenConfig"
    private const val CACHE_EXPIRATION_HOURS = 24L
    private const val MODULE_FILE_NAME = "module"
    private const val MODULE_ID_KEY = "id"

    private val propertyCache = ProjectPropertyCache(CACHE_PREFIX, CACHE_EXPIRATION_HOURS)
    private val directoryCache = ConcurrentHashMap<String, VirtualFile?>()

    /**
     * Finds the nearest configuration property for a given Java file.
     *
     * This method retrieves the group ID and artifact ID based on the provided Java file.
     * It then checks the property cache for an existing entry. If none is found,
     * it searches for the nearest configuration property in the project's structure.
     *
     * @param file The PsiJavaFile for which the property is searched.
     * @param configFileName The name of the configuration file.
     * @param propertyName The name of the property to find.
     * @return The value of the property, or null if not found.
     */
    fun findNearestConfigProperty(file: PsiJavaFile, configFileName: String, propertyName: String): String? {
        val project = file.project
        val groupId = getProjectGroupId(project) ?: return null
        val artifactId = getArtifactIdFromPackageName(file.packageName, groupId) ?: return null

        val cacheKey = "$artifactId:$configFileName:$propertyName"
        propertyCache.getCacheEntry(project, cacheKey)?.let { return it }

        return findNearestConfigPropertyInternal(file, configFileName, propertyName, groupId, artifactId)
            ?.also { propertyCache.addCacheEntry(project, cacheKey, it) }
    }

    /**
     * Retrieves the group ID of the project.
     *
     * This method checks whether the project is a Maven project and retrieves the group ID
     * from the first Maven project associated with it.
     *
     * @param project The current project.
     * @return The group ID or null if not found.
     */
    private fun getProjectGroupId(project: Project): String? {
        return runReadAction {
            val mavenProjectsManager = MavenProjectsManager.getInstance(project)
            if (mavenProjectsManager.isMavenizedProject) {
                mavenProjectsManager.projects.firstOrNull()?.mavenId?.groupId
            } else {
                null
            }
        }
    }

    /**
     * Derives the artifact ID from the package name and group ID.
     *
     * This method splits both the package name and group ID into parts and checks if
     * the package name starts with the group ID. If so, it returns the next part as the artifact ID.
     *
     * @param packageName The package name of the Java file.
     * @param groupId The group ID of the project.
     * @return The artifact ID or null if not derivable.
     */
    private fun getArtifactIdFromPackageName(packageName: String, groupId: String): String? {
        val packageNameParts = packageName.split(".")
        val groupIdParts = groupId.split(".")
        return if (packageNameParts.size > groupIdParts.size &&
            packageNameParts.subList(0, groupIdParts.size) == groupIdParts
        ) {
            packageNameParts.getOrNull(groupIdParts.size)
        } else {
            null
        }
    }

    /**
     * Scans and caches modules for the given project.
     *
     * This method first checks if the project is disposed. If not, it clears the existing
     * cache. It verifies whether the project has any Maven projects. If it does, it
     * iterates through all Maven projects and caches module information from each project.
     * If no Maven projects are found, it falls back to scanning modules based on their
     * directory structure.
     *
     * @param project The current project to scan for modules.
     */
    fun scanAndCacheModules(project: Project) {
        // Check if the project is disposed to avoid unnecessary processing
        if (project.isDisposed) {
            logger.warn("Project is disposed, skipping module scanning")
            return
        }

        logger.info("Starting to scan and cache modules for project: ${project.name}")

        // Get the instance of MavenProjectsManager for the given project
        val mavenProjectsManager = MavenProjectsManager.getInstance(project)
        // Get the plugin settings for the project
        val settings = ProjectPluginSettings.getInstance(project)

        // Clear the existing cache of module ID to path mappings
        settings.clearModuleIdToPathCache()
        logger.info("Cleared existing module cache")

        // Check if the project is not a Maven project or has no Maven projects
        if (!mavenProjectsManager.isMavenizedProject || !mavenProjectsManager.hasProjects()) {
            logger.info("No Maven projects found. Using alternative scanning method.")
            scanModulesByDirectory(project, settings)
            return
        }

        // Run a read action to fetch Maven projects safely
        val mavenProjects = runReadAction { mavenProjectsManager.projects }
        if (mavenProjects.isEmpty()) {
            logger.warn("Maven projects list is empty. Using alternative method.")
            scanModulesByDirectory(project, settings)
            return
        }

        // Iterate over each Maven project to cache module information
        mavenProjects.forEach { mavenProject ->
            cacheModuleFromFile(mavenProject, settings)
        }
    }

    /**
     * Cache module information from the specified Maven project.
     *
     * This method attempts to load the properties from the module file located in the
     * Maven project directory. If successful, it updates the project settings cache
     * with the module ID and its corresponding directory path.
     *
     * @param mavenProject The Maven project to load module information from.
     * @param settings The project plugin settings used to update the module ID to path cache.
     */
    private fun cacheModuleFromFile(mavenProject: MavenProject, settings: ProjectPluginSettings) {
        val moduleFile = File(mavenProject.directory, MODULE_FILE_NAME)
        if (moduleFile.exists()) {
            try {
                loadModuleProperties(moduleFile)?.let { moduleId ->
                    settings.updateModuleIdToPathCache(moduleId, mavenProject.directory)
                    logger.info("Cached module: $moduleId at ${mavenProject.directory}")
                } ?: logger.warn("Module ID not found in ${moduleFile.absolutePath}")
            } catch (e: Exception) {
                logger.warn("Error loading module file ${moduleFile.absolutePath}: ${e.message}", e)
            }
        } else {
            logger.debug("Module file does not exist: ${moduleFile.absolutePath}")
        }
    }

    /**
     * Load properties from a module file and retrieve the module ID.
     *
     * This method reads the properties file from the module and returns the module ID.
     * If an error occurs during loading, it logs a warning and returns null.
     *
     * @param moduleFile The module file to read from.
     * @return The module ID or null if not found.
     */
    private fun loadModuleProperties(moduleFile: File): String? {
        return try {
            Properties().apply {
                moduleFile.inputStream().use { load(it) }
            }.getProperty(MODULE_ID_KEY)
        } catch (e: Exception) {
            logger.warn(
                "Failed to load properties from module file: ${moduleFile.absolutePath}, error: ${e.message}",
                e
            )
            null
        }
    }

    /**
     * Scans the modules in the project based on the directory structure and caches their IDs.
     *
     * This method retrieves the content roots of each module and looks for a specific
     * module properties file. If found, it reads the module ID from the properties
     * file and updates the project settings cache with the module ID and its corresponding
     * directory path.
     *
     * @param project The current project for which modules are being scanned.
     * @param settings The project plugin settings used to update the module ID to path cache.
     */
    private fun scanModulesByDirectory(project: Project, settings: ProjectPluginSettings) {
        logger.info("Starting module scanning for project by directory: ${project.name}")

        val moduleManager = ModuleManager.getInstance(project)
        moduleManager.modules.forEach { module ->
            val moduleRootManager = ModuleRootManager.getInstance(module)
            moduleRootManager.contentRoots.forEach { contentRoot ->
                val moduleFile = VfsUtil.findRelativeFile(contentRoot, MODULE_FILE_NAME)
                if (moduleFile != null && !moduleFile.isDirectory) {
                    loadModuleProperties(moduleFile.toIoFile())?.let { moduleId ->
                        settings.updateModuleIdToPathCache(moduleId, contentRoot.path)
                        logger.info("Cached module (alternative method): $moduleId at ${contentRoot.path}")
                    } ?: logger.warn("Module ID not found in ${moduleFile.path}")
                }
            }
        }
    }

    /**
     * Converts a VirtualFile to a File.
     *
     * This extension function provides a convenient way to convert a VirtualFile
     * instance to a standard File instance, which can be useful for various file operations.
     */
    private fun VirtualFile.toIoFile(): File = File(this.path)

    /**
     * Retrieves the module directory from the cache.
     *
     * This method checks the cache for the module directory associated with the given module ID.
     * If not found, it retrieves the path from project settings and looks it up in the file system.
     *
     * @param project The current project.
     * @param moduleId The ID of the module.
     * @return The virtual file of the module directory or null if not found.
     */
    fun getModuleDirectoryFromCache(project: Project, moduleId: String): VirtualFile? {
        return directoryCache.computeIfAbsent(moduleId) {
            val settings = ProjectPluginSettings.getInstance(project)
            settings.getModulePathFromCache(moduleId)
                ?.let { LocalFileSystem.getInstance().findFileByPath(it) }
        }
    }

    /**
     * Internal method to find the nearest configuration property.
     *
     * This method searches for the nearest configuration property associated with the given
     * Java file by checking its Maven project and traversing up the directory structure.
     *
     * @param file The PsiJavaFile for which the property is searched.
     * @param configFileName The name of the configuration file.
     * @param propertyName The name of the property to find.
     * @param groupId The group ID of the project.
     * @param artifactId The artifact ID of the project.
     * @return The value of the property, or null if not found.
     */
    private fun findNearestConfigPropertyInternal(
        file: PsiJavaFile,
        configFileName: String,
        propertyName: String,
        groupId: String,
        artifactId: String
    ): String? {
        // Obtain the project from the given PsiJavaFile
        val project = file.project
        // Get the MavenProjectsManager instance for the project
        val mavenProjectsManager = MavenProjectsManager.getInstance(project)

        // Find the Maven project that contains the given file, running inside a read action
        val currentMavenProject = runReadAction {
            mavenProjectsManager.findContainingProject(file.virtualFile)
        } ?: return null // Return null if no containing Maven project is found

        // Find the root Maven project based on groupId and artifactId
        val rootMavenProject = findRootMavenProject(project, currentMavenProject, groupId, artifactId)
        // Determine the starting directory for searching the config file
        val startDir = getVirtualFile(rootMavenProject?.directory ?: currentMavenProject.directory)
        // Search for the configuration file in ancestor directories
        val configContent = startDir?.let { findConfigFileInAncestors(it, configFileName) }
        // Extract the desired property from the configuration file content, if available
        return configContent?.let { extractPropertyFromContent(it, propertyName) }
    }

    /**
     * Converts a directory path to a virtual file.
     *
     * This method attempts to find a virtual file corresponding to the given directory path.
     *
     * @param dirPath The directory path as a string.
     * @return The corresponding virtual file or null if not found.
     */
    private fun getVirtualFile(dirPath: String): VirtualFile? {
        return LocalFileSystem.getInstance().findFileByIoFile(File(dirPath))
    }

    /**
     * Finds the root Maven project matching a specific group ID and artifact ID.
     *
     * This method traverses the hierarchy of Maven projects, starting from the provided
     * starting project, to locate the root project that matches the specified group ID
     * and artifact ID. It moves up the hierarchy by finding the parent project of the
     * current project at each step until it either finds a match or reaches the top.
     *
     * @param project The current project.
     * @param startProject The starting Maven project from which to begin the search.
     * @param targetGroupId The target group ID to match against.
     * @param targetArtifactId The target artifact ID to match against.
     * @return The root Maven project that matches the specified IDs, or null if not found.
     */
    private fun findRootMavenProject(
        project: Project,
        startProject: MavenProject,
        targetGroupId: String,
        targetArtifactId: String
    ): MavenProject? {
        // Obtain the MavenProjectsManager for the given project
        val mavenProjectsManager = MavenProjectsManager.getInstance(project)

        // Retrieve all Maven projects associated with the project in a read action
        val allProjects = runReadAction { mavenProjectsManager.projects }
        // Initialize the current project to start with
        var currentProject: MavenProject? = startProject

        // Traverse up the project hierarchy
        while (currentProject != null) {
            // Check if the current project's groupId and artifactId match the target
            if (currentProject.mavenId.groupId == targetGroupId &&
                currentProject.mavenId.artifactId == targetArtifactId
            ) {
                return currentProject // Return the matching project
            }
            // Move to the parent project in the hierarchy
            currentProject = findParentProject(allProjects, currentProject)
        }

        // Return null if no matching project is found in the hierarchy
        return null
    }

    /**
     * Finds the parent project of a given Maven project.
     *
     * This method looks up the parent ID of the specified child project and searches
     * through all projects to find and return the corresponding parent project.
     *
     * @param allProjects The list of all Maven projects.
     * @param childProject The child Maven project.
     * @return The parent Maven project or null if not found.
     */
    private fun findParentProject(allProjects: List<MavenProject>, childProject: MavenProject): MavenProject? {
        val parentId: MavenId = childProject.parentId ?: return null
        if (parentId.equals(childProject.mavenId.groupId, childProject.mavenId.artifactId)) {
            return null
        }
        return allProjects.find { project ->
            project.mavenId.groupId == parentId.groupId &&
                    project.mavenId.artifactId == parentId.artifactId &&
                    project.mavenId.version == parentId.version
        }
    }

    /**
     * Searches for a configuration file in the ancestor directories.
     *
     * This method starts from a given directory and traverses up through its parent directories
     * until it finds a configuration file with the specified name. If the file is found, it reads
     * and returns its contents. This is useful for locating configuration files that may reside
     * in various levels of the directory structure relative to a project.
     *
     * @param startDir The starting directory from which to begin searching.
     * @param configFileName The name of the configuration file to search for.
     * @return The content of the configuration file as a string, or null if not found.
     */
    private fun findConfigFileInAncestors(startDir: VirtualFile, configFileName: String): String? {
        var currentDir: VirtualFile? = startDir

        // Traverse up the directory hierarchy
        while (currentDir != null) {
            // Look for the configuration file in the current directory
            val configFile = currentDir.findChild(configFileName)
            if (configFile != null && !configFile.isDirectory) {
                // If found, read and return the content of the file
                return configFile.inputStream.bufferedReader().use { it.readText() }
            }
            // Move to the parent directory
            currentDir = currentDir.parent
        }
        // Return null if the file was not found in any ancestor directories
        return null
    }

    /**
     * Extracts a property's value from a configuration file content.
     *
     * This method loads a properties object from the provided content and retrieves
     * the specified property's value. If an error occurs during loading, it logs a warning
     * and returns null.
     *
     * @param content The content of the configuration file.
     * @param propertyName The name of the property to extract.
     * @return The value of the property or null if not found.
     */
    private fun extractPropertyFromContent(content: String, propertyName: String): String? {
        return try {
            Properties().apply {
                load(content.byteInputStream())
            }.getProperty(propertyName)
        } catch (e: Exception) {
            logger.warn("Failed to extract property from content: ${e.message}", e)
            null
        }
    }

    /**
     * Clears the cache for a given project.
     *
     * This method clears both the property cache and the directory cache associated
     * with the specified project, preparing for future caching operations.
     *
     * @param project The current project.
     */
    fun clearCache(project: Project) {
        propertyCache.clearCache(project)
        directoryCache.clear()
    }
}
