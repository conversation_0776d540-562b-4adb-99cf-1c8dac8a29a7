package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.application.PathManager
import java.nio.file.Path
import java.nio.file.Paths;

object PathUtil {
    private var pluginDataDir: Path? = null
    fun getPluginDataDir(): Path {
        if (pluginDataDir == null) {
            val pluginId = PluginManager.getPluginByClass(PathUtil::class.java)?.pluginId?.idString
            val configPath: Path = Paths.get(PathManager.getConfigPath())
            pluginDataDir = Path.of(configPath.parent.toString(), pluginId)
        }
        return pluginDataDir!!
    }
}