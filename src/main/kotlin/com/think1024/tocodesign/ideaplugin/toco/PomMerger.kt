package com.think1024.tocodesign.ideaplugin.toco

import org.w3c.dom.*
import java.io.StringReader
import java.io.StringWriter
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.transform.OutputKeys
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult
import javax.xml.transform.stream.StreamSource

object PomMerger {
    /**
     * Data classes representing the POM structure.
     */
    data class Dependency(
        val groupId: String,
        val artifactId: String,
        val version: String,
        val type: String,
    )

    /**
     * Deduplicates dependencies based on groupId and artifactId.
     * @param deps1 First list of dependencies.
     * @param deps2 Second list of dependencies.
     * @return Deduplicated list of dependencies, preferring deps1 versions.
     */
    private fun dedupeDependencies(deps1: List<Dependency>, deps2: List<Dependency>): List<Dependency> {
        val depMap = mutableMapOf<String, Dependency>()

        // Process first list
        for (dep in deps1) {
            val key = "${dep.groupId}:${dep.artifactId}"
            depMap[key] = dep
        }

        // Process second list, adding only new dependencies
        for (dep in deps2) {
            val key = "${dep.groupId}:${dep.artifactId}"
            if (!depMap.containsKey(key)) {
                depMap[key] = dep
            }
        }

        return depMap.values.toList()
    }

    /**
     * Deduplicates modules.
     * @param mods1 First list of modules.
     * @param mods2 Second list of modules.
     * @return Deduplicated list of modules.
     */
    private fun dedupeModules(mods1: List<String>, mods2: List<String>): List<String> {
        return (mods1 + mods2).distinct()
    }

    fun removeModule(xml: String, moduleName: String): String {
        val moduleNodeValue = "modules/$moduleName"
        val factory = DocumentBuilderFactory.newInstance()
        val builder = factory.newDocumentBuilder()
        val pom = builder.parse(xml.byteInputStream())

        val pomProject = pom.getElementsByTagName("project").item(0)
        val modules = getChildren(pomProject).find { it.nodeName == "modules" }
        val moduleList = getChildren(modules).map { it.textContent }.filter { it != moduleNodeValue }
        setModulesToNode(moduleList, modules, pom)

        return printXML(pom)
    }

    /**
     * Merges two POM XML files.
     * @param xml1 Content of the first POM file.
     * @param xml2 Content of the second POM file.
     * @return Merged POM XML as a string.
     * @throws Exception if merging fails.
     */
    fun mergePomFiles(xml1: String, xml2: String): String? {
        try {
            // Parse XML
            val factory = DocumentBuilderFactory.newInstance()
            val builder = factory.newDocumentBuilder()
            val pom1 = builder.parse(xml1.byteInputStream())
            val pom2 = builder.parse(xml2.byteInputStream())

            val pom1Project = pom1.getElementsByTagName("project").item(0)
            val pom2Project = pom2.getElementsByTagName("project").item(0)

            val dependencies1 = getChildren(pom1Project).find { it.nodeName == "dependencies" }
            val dependencyList1 = getDependencies(dependencies1)
            val dependencies2 = getChildren(pom2Project).find { it.nodeName == "dependencies" }
            val dependencyList2 = getDependencies(dependencies2)
            val mergedDependencyList = dedupeDependencies(dependencyList1, dependencyList2)
            setDependenciesToNode(mergedDependencyList, dependencies1, pom1)

            val modules1 = getChildren(pom1Project).find { it.nodeName == "modules" }
            val moduleList1 = getChildren(modules1).map { it.textContent }
            val modules2 = getChildren(pom2Project).find { it.nodeName == "modules" }
            val moduleList2 = getChildren(modules2).map { it.textContent }
            val mergedModuleList = dedupeModules(moduleList1, moduleList2)
            setModulesToNode(mergedModuleList, modules1, pom1)

            return printXML(pom1)
        } catch (e: Exception) {
            println("Failed to merge POM files: ${e.message}")
            return null
        }
    }

    private fun printXML(pom: Document): String {
        val xslt = """
            <xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
                <xsl:output method="xml" indent="yes" encoding="UTF-8" omit-xml-declaration="no"/>
                <xsl:strip-space elements="*"/>
                <xsl:template match="@*|node()">
                    <xsl:copy>
                        <xsl:apply-templates select="@*|node()"/>
                    </xsl:copy>
                </xsl:template>
            </xsl:stylesheet>
        """.trimIndent()
        val xsltSource = StreamSource(StringReader(xslt))
        val transformerFactory = TransformerFactory.newInstance()
        transformerFactory.setAttribute("indent-number", 4)
        val transformer = transformerFactory.newTransformer(xsltSource).apply {
            setOutputProperty(OutputKeys.INDENT, "yes")
            setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no")
            setOutputProperty(OutputKeys.METHOD, "xml")
            setOutputProperty(OutputKeys.ENCODING, "UTF-8")
            setOutputProperty(OutputKeys.STANDALONE, "yes")
            setOutputProperty(OutputKeys.VERSION, "1.0")
        }
        val writer = StringWriter()
        transformer.transform(DOMSource(pom), StreamResult(writer))
        return writer.toString()
    }

    private fun setDependenciesToNode(dependencyList: List<Dependency>, dependenciesNode: Node?, doc: Document) {
        if (dependenciesNode == null) {
            return
        }
        getChildren(dependenciesNode).forEach { dependenciesNode.removeChild(it) }
        dependencyList.forEach {
            val node = doc.createElement("dependency")
            if (it.groupId.isNotEmpty()) {
                val child = doc.createElement("groupId")
                child.textContent = it.groupId
                node.appendChild(child)
            }
            if (it.artifactId.isNotEmpty()) {
                val child = doc.createElement("artifactId")
                child.textContent = it.artifactId
                node.appendChild(child)
            }
            if (it.version.isNotEmpty()) {
                val child = doc.createElement("version")
                child.textContent = it.version
                node.appendChild(child)
            }
            if (it.type.isNotEmpty()) {
                val child = doc.createElement("type")
                child.textContent = it.type
                node.appendChild(child)
            }
            dependenciesNode.appendChild(node)
        }
    }

    private fun setModulesToNode(moduleList: List<String>, modulesNode: Node?, doc: Document) {
        if (modulesNode == null) {
            return
        }
        getChildren(modulesNode).forEach { modulesNode.removeChild(it) }
        moduleList.forEach {
            val node = doc.createElement("module")
            node.textContent = it
            modulesNode.appendChild(node)
        }
    }

    private fun getDependencies(dependencies: Node?): List<Dependency> {
        return getChildren(dependencies).map { Dependency(
            artifactId = getChildren(it).find { it.nodeName == "artifactId" }?.textContent ?: "",
            groupId = getChildren(it).find { it.nodeName == "groupId" }?.textContent ?: "",
            version = getChildren(it).find { it.nodeName == "version" }?.textContent ?: "",
            type = getChildren(it).find { it.nodeName == "type" }?.textContent ?: "",
        )}
    }

    private fun getChildren(node: Node?): List<Node> {
        var ret = mutableListOf<Node>()
        if (node == null) {
            return ret
        }
        for (i in 0..node.childNodes.length - 1) {
            val node = node.childNodes.item(i)
            if (node.nodeType == Node.ELEMENT_NODE) {
                ret.add(node)
            }
        }
        return ret
    }
}