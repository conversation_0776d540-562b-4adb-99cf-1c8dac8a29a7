package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx
import com.intellij.openapi.fileEditor.impl.EditorWindow
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectCloseHandler
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString

class TocoProjectCloseHandler : ProjectCloseHandler {
    fun isTocoFileModified(file: VirtualFile, project: Project): Boolean {
        val editors: Array<FileEditor?> = FileEditorManager.getInstance(project).getEditors(file)
        for (editor in editors) {
            if (editor is TocoWebViewEditor) {
                return editor.isModified
            }
        }
        return false
    }

    override fun canClose(project: Project): Boolean {
        val fileEditorManager = FileEditorManagerEx.getInstanceEx(project)
        val window = fileEditorManager.currentWindow
        if (window == null) {
            return true
        }
        val modifiedFile = window.fileList.find { isTocoFileModified(it, project) }
        if (modifiedFile == null) {
            return true
        }
        val result: Int = Messages.showYesNoDialog(
            project,
            getI18nString("message.editor.modified.confirm.message"),
            getI18nString("message.project.modified.confirm.title"),
            Messages.getWarningIcon()
        )
        return result == Messages.OK
    }
}