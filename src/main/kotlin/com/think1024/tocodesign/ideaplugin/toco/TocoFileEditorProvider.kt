package com.think1024.tocodesign.ideaplugin.toco

import TocoFileType
import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.util.Key
import com.intellij.openapi.project.DumbAware

class TocoWebViewEditorProvider : FileEditorProvider, DumbAware {
    override fun accept(project: Project, file: VirtualFile): <PERSON>olean {
        return file is TocoVirtualFile || (file.name == "/test.toco" && file.fileType == TocoFileType)
    }

    override fun createEditor(project: Project, file: VirtualFile): FileEditor {
        val url = file.getUserData(TocoVirtualFile.URL_KEY) ?: "https://default-url.com"
        println("✅ createEditor called for: $file (${file::class.simpleName})")
        return TocoWebViewEditor(project, file, url)
    }

    override fun getEditorTypeId(): String = "toco-webview-editor"
    override fun getPolicy(): FileEditorPolicy = FileEditorPolicy.HIDE_DEFAULT_EDITOR
}

