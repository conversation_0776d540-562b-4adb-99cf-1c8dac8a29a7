package com.think1024.tocodesign.ideaplugin.toco

import TocoFileType
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileSystem
import com.intellij.testFramework.LightVirtualFile
import com.intellij.ui.AnimatedIcon
import javax.swing.Icon

class TocoVirtualFile(
    var webUrl: String,
    private var displayName: String,
    private var displayIcon: Icon,
    var type: String = "java"  // 添加类型字段
) : LightVirtualFile(displayName, TocoFileType, "") {
    private var loading = true

    init {
        putUserData(URL_KEY, webUrl)
        putUserData(TITLE_KEY, displayName)
        putUserData(TYPE_KEY, type)
        putUserData(ICON_KEY, displayIcon)
    }

    companion object {
        val URL_KEY = Key.create<String>("TOCO_WEBVIEW_URL")
        val TITLE_KEY = Key.create<String>("TOCO_WEBVIEW_TITLE")
        val TYPE_KEY = Key.create<String>("TOCO_WEBVIEW_TYPE")
        val ICON_KEY = Key.create<Icon>("TOCO_WEBVIEW_ICON")
    }

    // 重写getIcon方法以确保返回正确的图标
    fun getIcon(): Icon = getUserData(ICON_KEY) ?: displayIcon

    // 获取显示图标的方法
    fun getDisplayIcon(): Icon = getUserData(ICON_KEY) ?: displayIcon

    // 重写getFileType方法以返回自定义的文件类型
    override fun getFileType(): FileType {
        return object : FileType by TocoFileType {
            override fun getIcon(): Icon = if (loading)  AnimatedIcon.Default.INSTANCE else getUserData(ICON_KEY) ?: displayIcon
        }
    }

    // 添加更新属性的方法
    fun update(newUrl: String, newTitle: String, newIcon: Icon, newType: String) {
        // 更新属性
        this.webUrl = newUrl
        this.displayName = newTitle
        this.displayIcon = newIcon
        this.type = newType

        // 更新用户数据（关键：确保 UI 能读取到最新值）
        putUserData(URL_KEY, newUrl)
        putUserData(TITLE_KEY, newTitle)
        putUserData(TYPE_KEY, newType)
        putUserData(ICON_KEY, newIcon)

        // 通知 IDE 文件属性已变更（触发 UI 刷新）
//        firePropertyChange(VirtualFile.PROP_NAME, null, newTitle)
//        firePropertyChange(VirtualFile.PROP_ICON, null, newIcon)
    }
    
    // 重写equals和hashCode方法以确保正确的标识
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is TocoVirtualFile) return false
        return webUrl == other.webUrl
    }

    override fun hashCode(): Int {
        return webUrl.hashCode()
    }

    fun setLoading(loading: Boolean) {
        this.loading = loading
    }
}