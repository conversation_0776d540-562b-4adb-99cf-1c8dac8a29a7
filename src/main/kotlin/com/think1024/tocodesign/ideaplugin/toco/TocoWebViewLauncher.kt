package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.icons.AllIcons
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.IconLoader
import javax.swing.Icon

@Service(Service.Level.PROJECT)
@State(
    name = "TocoWebViewLauncher",
    storages = [Storage("tocoWebViews.xml")]
)
class TocoWebViewLauncherState : PersistentStateComponent<TocoWebViewLauncherState.State> {
    
    data class State(
        var openedFiles: List<OpenedFile> = emptyList()
    )
    
    data class OpenedFile(
        var url: String = "",
        var title: String = "",
        var type: String = "java"
    )
    
    private var myState = State()
    
    override fun getState(): State = myState
    
    override fun loadState(state: State) {
        myState = state
    }
    
    fun addOpenedFile(url: String, title: String, type: String) {
        val existingIndex = myState.openedFiles.indexOfFirst { it.url == url }
        if (existingIndex >= 0) {
            myState.openedFiles = myState.openedFiles.toMutableList().apply {
                this[existingIndex] = OpenedFile(url, title, type)
            }
        } else {
            myState.openedFiles = myState.openedFiles + OpenedFile(url, title, type)
        }
    }

    fun updateOpenedFile(oldUrl: String, newUrl: String, title: String, type: String) {
        val existingIndex = myState.openedFiles.indexOfFirst { it.url == oldUrl }
        if (existingIndex >= 0) {
            myState.openedFiles = myState.openedFiles.toMutableList().apply {
                this[existingIndex] = OpenedFile(newUrl, title, type)
            }
        }
    }
    
    fun removeOpenedFile(url: String) {
        myState.openedFiles = myState.openedFiles.filter { it.url != url }
    }
    
    fun getOpenedFiles(): List<OpenedFile> = myState.openedFiles
    
    companion object {
        fun getInstance(project: Project): TocoWebViewLauncherState {
            return project.getService(TocoWebViewLauncherState::class.java)
        }
    }
}

object TocoWebViewLauncher {
    private val openedFiles = mutableSetOf<String>()  // 运行时缓存

    // 定义图标映射
    val iconMap: Map<String, Icon> = mapOf(
        "project-home" to IconLoader.getIcon("/icons/project-home.svg", TocoWebViewLauncher::class.java), // project-home 对应的图标
        "module-home" to IconLoader.getIcon("/icons/module-home.svg", TocoWebViewLauncher::class.java), // module-home 对应的图标
        "api" to IconLoader.getIcon("/icons/api.svg", TocoWebViewLauncher::class.java), // api 对应的图标
        "bo" to IconLoader.getIcon("/icons/bo.svg", TocoWebViewLauncher::class.java), // bo 对应的图标
        "dto" to IconLoader.getIcon("/icons/dto.svg", TocoWebViewLauncher::class.java), // dto 对应的图标
        "enum" to IconLoader.getIcon("/icons/enum.svg", TocoWebViewLauncher::class.java), // enum 对应的图标
        "eo" to IconLoader.getIcon("/icons/eo.svg", TocoWebViewLauncher::class.java), // eo 对应的图标
        "er" to IconLoader.getIcon("/icons/er.svg", TocoWebViewLauncher::class.java), // er 对应的图标
        "flow" to IconLoader.getIcon("/icons/flow.svg", TocoWebViewLauncher::class.java), // flow 对应的图标
        "msg" to IconLoader.getIcon("/icons/msg.svg", TocoWebViewLauncher::class.java), // msg 对应的图标
        "read" to IconLoader.getIcon("/icons/read.svg", TocoWebViewLauncher::class.java), // read 对应的图标
        "rpc" to IconLoader.getIcon("/icons/rpc.svg", TocoWebViewLauncher::class.java), // rpc 对应的图标
        "vo" to IconLoader.getIcon("/icons/vo.svg", TocoWebViewLauncher::class.java), // vo 对应的图标
        "write" to IconLoader.getIcon("/icons/write.svg", TocoWebViewLauncher::class.java), // write 对应的图标
    )

    fun openPage(
        project: Project? = null,
        url: String,
        title: String = "Test",
        type: String = "java", // 新增 type 参数，来选择图标
        history: Boolean = false,
        method: String = "push"
    ) {
        if (project == null) {
            return
        }
        // 根据 type 参数选择对应的图标
        val icon: Icon = iconMap[type] ?: AllIcons.FileTypes.Unknown // 如果 type 没有匹配，默认使用 Unknown 图标
        
        println("✅ 打开页面: $title, 类型: $type, 图标: ${icon.toString()}")

        // 如果是 replace 方法，调用替换逻辑
        if (method == "replace") {
            if (replaceCurrentPage(project, url, title, icon, type)) {
                return // 替换成功则直接返回
            }
            // 替换失败则继续执行常规打开逻辑
        }

        // 创建 TocoVirtualFile
        val file = TocoVirtualFile(url, title, icon, type)
        
        // 确保文件类型数据被正确设置
        file.putUserData(TocoVirtualFile.TYPE_KEY, type)

        if (openedFiles.contains(url)) {
            // 如果已经打开，可能需要更新图标
            updateFileIcon(project, url, icon, type)
        } else {
            openedFiles.add(url)
            // 保存到持久化状态
            TocoWebViewLauncherState.getInstance(project).addOpenedFile(url, title, type)
        }

        ApplicationManager.getApplication().invokeLater {
            // 打开文件并设置 icon 和 title
            FileEditorManager.getInstance(project).openFile(file, !history)
        }
    }

    /**
     * 替换当前活动标签页的内容
     * @return 替换成功返回 true，否则返回 false
     */
    private fun replaceCurrentPage(
        project: Project,
        url: String,
        title: String,
        icon: Icon,
        type: String
    ): Boolean {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val selectedFiles = fileEditorManager.selectedFiles

        if (selectedFiles.isEmpty()) {
            return false // 没有选中的文件，替换失败
        }

        val currentFile = selectedFiles[0]

        // 检查当前文件是否是 TocoVirtualFile 类型
        if (currentFile !is TocoVirtualFile) {
            return false // 当前文件不是 TocoVirtualFile，替换失败
        }

        val oldUrl = currentFile.url

        // 更新当前文件的信息
        currentFile.update(url, title, icon, type)

        // 4. 关键：获取当前 WebView 并加载新 URL
        ApplicationManager.getApplication().invokeLater {
            // 获取当前文件对应的编辑器
            val editors = fileEditorManager.getAllEditors(currentFile)
            if (editors.isNotEmpty()) {
                val editor = editors[0]

                // 尝试将编辑器转换为 WebView 容器（具体类名取决于你的实现）
                if (editor is TocoWebViewEditor) { // 替换为实际的 WebView 编辑器类
                    editor.setState(TocoWebViewState(
                        url = url ?: "",
                        title = title ?: "",
                        type = type ?: "JAVA",
                    ))
                }
            }

            // 刷新编辑器 UI（标题、图标等）
            fileEditorManager.updateFilePresentation(currentFile)
        }
        // 保存到持久化状态
        TocoWebViewLauncherState.getInstance(project).updateOpenedFile(oldUrl, url, title, type)

        // 如果 URL 发生变化，更新已打开文件集合
        if (oldUrl != url) {
            openedFiles.remove(oldUrl)
            openedFiles.add(url)
        }

        return true // 替换成功
    }

    fun clearFile(url: String) {
        openedFiles.remove(url)
        // 从持久化状态中移除
        ApplicationManager.getApplication().getComponent(TocoWebViewLauncherState::class.java)?.let {
            it.removeOpenedFile(url)
        }
    }

    // 在项目打开时恢复标签页
    fun restoreOpenedFiles(project: Project) {
        val state = TocoWebViewLauncherState.getInstance(project)
        val filesToOpen = state.getOpenedFiles()

        for (fileInfo in filesToOpen) {
            openPage(project, fileInfo.url, fileInfo.title, fileInfo.type, true)
        }
    }

    // 添加更新文件图标的方法
    private fun updateFileIcon(project: Project, url: String, icon: Icon, type: String) {
        val fileEditorManager = FileEditorManager.getInstance(project)
        val openFiles = fileEditorManager.openFiles

        for (file in openFiles) {
            if (file is TocoVirtualFile && file.webUrl == url) {
                // 更新文件类型和图标
                file.putUserData(TocoVirtualFile.TYPE_KEY, type)

                // 刷新编辑器UI以显示新图标
                ApplicationManager.getApplication().invokeLater {
                    fileEditorManager.updateFilePresentation(file)
                }

                break
            }
        }
    }
}