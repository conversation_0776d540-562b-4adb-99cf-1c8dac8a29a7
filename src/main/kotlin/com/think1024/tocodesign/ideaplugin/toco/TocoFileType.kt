import com.intellij.openapi.fileTypes.LanguageFileType
import com.intellij.lang.Language
import javax.swing.Icon
import com.intellij.icons.AllIcons
import com.intellij.openapi.fileTypes.PlainTextLanguage

object TocoFileType : LanguageFileType(PlainTextLanguage.INSTANCE) {
    // 默认的图标是 Java 图标
    private var customIcon: Icon = AllIcons.FileTypes.Java

    override fun getName(): String = "Toco"
    override fun getDescription(): String = "Toco WebView virtual file"
    override fun getDefaultExtension(): String = "toco"

    // 返回自定义图标
    override fun getIcon(): Icon = customIcon

    // 设置新的图标
    fun setIcon(icon: Icon) {
        customIcon = icon
    }
}