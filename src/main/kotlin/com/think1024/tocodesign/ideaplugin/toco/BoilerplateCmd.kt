package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.BinaryRunner
import com.think1024.tocodesign.ideaplugin.utils.BinaryRunner.Result
import com.think1024.tocodesign.ideaplugin.utils.PathUtil
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.json.JSONObject
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.security.cert.CertificateException
import java.io.StringReader
import java.io.File
import java.nio.file.Path
import java.util.Date

object BoilerplateCmd {
    data class GenerateProjectConfig(
        val url: String,
        val ref: String,
        val version: String,
        val projectName: String,
        val projectId: String,
        val path: String,
        val groupId: String?,
        // val gitUrl: String?,
        // val message: String = "init",
    )

    data class GenerateModuleConfig(
        val url: String,
        val ref: String,
        val projectId: String,
        val projectName: String,
        val groupId: String,
        val moduleName: String,
        val moduleId: String,
//        val gitUrl: String? = null,
    )

    private var _serverProcess: Process? = null
    private var _port: Int? = null
    private const val BINARY_NAME = "boilerplate"

//    private fun runBoilerplateCmdAsync(vararg args: String, callback: (Result) -> Unit) {
//        val path = BinaryRunner.getBinaryPath(BINARY_NAME)
//        return BinaryRunner.runAsync(path.toString(), *args, callback = callback)
//    }

    private fun runBoilerplateCmd(vararg args: String, cwd: String = ""): Result {
        val path = BinaryRunner.getBinaryPath(BINARY_NAME)
        return BinaryRunner.runSync(path.toString(), *args, cwd = cwd)
    }

    fun generateProject(config: GenerateProjectConfig): Result {
        val (url, ref, version, projectName, projectId, path, groupId) = config
        val dataJson = Json.encodeToString(
            buildJsonObject {
                put("projectName", projectName)
                put("groupId", groupId)
                put("vsVersion", version)
                put("projectId", projectId)
            }
        )
        val args = mutableListOf(
            "new",
            "-t", url,
            "-r", ref,
            "-d", if(BinaryRunner.getPlatform() == "win32") JSONObject.quote(dataJson) else dataJson,
            "-p", path,
        )
        return this.runBoilerplateCmd(*args.toTypedArray())
    }

    fun generateModule(config: GenerateModuleConfig, path: String): Result {
        val (url, ref, projectId, projectName, groupId, moduleName, moduleId) = config
        val dataJson = Json.encodeToString(
            buildJsonObject {
                put("projectName", projectName)
                put("groupId", groupId)
                put("projectId", projectId)
                put("moduleName", moduleName)
                put("moduleId", moduleId)
            }
        )
        val args = mutableListOf(
            "new-module",
            "-t", url,
            "-r", ref,
            "-d", if(BinaryRunner.getPlatform() == "win32") JSONObject.quote(dataJson) else dataJson,
            "-p", path,
        )
        return this.runBoilerplateCmd(*args.toTypedArray())
    }

    private fun checkPemExpiry(pemFilePath: String): Boolean {
        try {
            // 1. 读取 PEM 文件内容
            val pemContent = File(pemFilePath).readText()

            // 2. 清理 PEM 格式的头尾标记
            val certificateText = pemContent
                .replace("-----BEGIN CERTIFICATE-----", "")
                .replace("-----END CERTIFICATE-----", "")
                .replace("\\s".toRegex(), "") // 移除换行符和空白字符

            // 3. 将 Base64 编码的证书解码并解析为 X509Certificate
            val certificateFactory = CertificateFactory.getInstance("X.509")
            val certificate = certificateFactory.generateCertificate(
                StringReader("-----BEGIN CERTIFICATE-----\n$certificateText\n-----END CERTIFICATE-----")
                    .use { it.readText() }
                    .byteInputStream()
            ) as X509Certificate

            // 4. 获取证书的过期时间和当前时间
            val notAfter = certificate.notAfter // 证书过期时间
            val currentTime = Date()

            // 5. 检查证书是否过期
            return if (currentTime.after(notAfter)) {
                println("证书已过期，过期时间: ${notAfter}")
                false
            } else {
                println("证书未过期，有效期至: ${notAfter}")
                true
            }
        } catch (e: CertificateException) {
            println("解析证书失败: ${e.message}")
            return false
        } catch (e: Exception) {
            println("发生错误: ${e.message}")
            return false
        }
    }

    fun mkcert(project: Project) {
        if (project.basePath.isNullOrEmpty()) {
            return
        }
        // 证书目录
        val resourceDir = Path.of(project.basePath!!, "/entrance/web/src/main/resources")

        // 创建文件夹
        File(resourceDir.toString()).mkdirs()

        // 检查pem文件是否存在
        val pemFilePath = Path.of(resourceDir.toString(), "localhost.pem").toString()
        val pemFile = File(pemFilePath)
        val pemKeyFilePath = Path.of(resourceDir.toString(), "localhost-key.pem").toString()
        val pemKeyFile = File(pemKeyFilePath)
        if (pemFile.exists() && checkPemExpiry(pemFilePath) && pemKeyFile.exists()) {
            return
        }
        pemFile.delete()
        pemKeyFile.delete()
        val args = mutableListOf("mkcert", "localhost")
        this.runBoilerplateCmd(*args.toTypedArray(), cwd = resourceDir.toString())
    }

//    fun startServer() {
//        if (_serverProcess != null) {
//            stopServer()
//        }
//        val settings = ApplicationPluginSettings.getInstance()
//        var host = settings.host
//        if (host.contains("local.teitui.com")) {
//            host = "https://toco.teitui.com"
//        }
//        val path = BinaryRunner.getBinaryPath(BINARY_NAME)
//        _serverProcess = BinaryRunner.runCommand(
//            path.toString(),
//            "server",
//            "--data-dir", PathUtil.getPluginDataDir().toString(),
//            "--api-baseurl", host,
//            cwd = "",
//            onOutput = { line ->
//                val regex = Regex("HTTP server running on :(\\d+)")
//                val match = regex.find(line)
//                if (match != null) {
//                    _port = match.groupValues[1].toInt()
//                    println(("Server started, port = $_port"))
//                } else {
//                    println("Server: $line")
//                }
//            },
//            onError = { line -> println("Error: $line"); stopServer() },
//            onExit = { code -> println("Exit: $code"); stopServer() },
//        )
//    }
//
//    private fun stopServer() {
//        _serverProcess?.destroy()
//        _serverProcess = null
//    }
}