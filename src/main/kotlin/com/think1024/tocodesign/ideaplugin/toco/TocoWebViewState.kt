package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.fileEditor.FileEditorState
import com.intellij.openapi.fileEditor.FileEditorStateLevel
import java.io.Serializable

class TocoWebViewState(
    val url: String,
    val title: String,
    val type: String
) : FileEditorState, Serializable {
    
    override fun canBeMergedWith(otherState: FileEditorState, level: FileEditorStateLevel): Boolean {
        return otherState is TocoWebViewState
    }
    
    companion object {
        private const val serialVersionUID = 1L
    }
}