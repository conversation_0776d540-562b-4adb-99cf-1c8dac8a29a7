package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.editor.colors.ColorKey
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.impl.EditorTabColorProvider
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.FileColorManager
import java.awt.Color


class TocoFileEditorTabColorProvider(): EditorTabColorProvider {
    override fun getEditorTabColor(project: Project, file: VirtualFile): Color? {
        val colorManager = FileColorManager.getInstance(project)
        if (!colorManager.isEnabledForTabs) {
            return null
        }
        if (file is TocoVirtualFile) {
            val fileEditorManager = FileEditorManager.getInstance(project)
            val editors = fileEditorManager.getEditors(file)
            val modifiedEditor = editors.find { it -> it.isModified }
            return if (modifiedEditor != null) colorManager.getColor("Blue") else null
        }
        return colorManager.getFileColor(file)
    }
}