# Opt-out flag for bundling Kotlin standard library -> https://jb.gg/intellij-platform-kotlin-stdlib
kotlin.stdlib.default.dependency=false
# 启用 K2 编译器
kotlin.experimental.tryK2=true
kotlin.compiler.suppressExperimentalK2Warning=true
kotlin.daemon.jvm.options=-Xmx4g
# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache=true
# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching=true
# Gradle JVM args
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# ?? Kotlin ?????????
kotlin.daemon.jvmargs=-Xmx4096m -Xms512m -XX:+UseParallelGC
kotlin.daemon.useFallbackStrategy=true
kotlin.daemon.client.alive.check.interval.ms=5000
kotlin.daemon.client.connect.timeout.ms=30000
kotlin.daemon.shutdown.delay.ms=3000
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true

# ?? Kotlin ???????????????????
kotlin.compiler.execution.strategy=in-process
