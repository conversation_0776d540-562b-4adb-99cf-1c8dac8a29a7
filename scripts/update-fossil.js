const fs = require("fs");
const path = require("path");
const https = require("https");
const zlib = require("zlib");
const { <PERSON>uffer } = require('buffer');
const { exec } = require("child_process");

const host = "https://fossil-scm.org";
const platformToDirMap = {
    macarm: "darwin",
    macx64: "darwin",
    linux: "linux",
    win64: "win32",
};
const targetVersion = "2.26";
// const targetVersion = "latest";

const getText = (url) => {
    return new Promise((resolve, reject) => {
        https
            .get(new URL(url, host), (res) => {
                let data = "";
                res.on("data", (chunk) => {
                    data += chunk.toString("utf-8");
                });
                res.on("end", () => {
                    resolve(data);
                });
                res.on("error", (err) => {
                    reject(err);
                });
            })
            .on("error", (err) => {
                reject(err);
            });
    });
};

const getZipFilePath = (platform) => {
    const fileName = `${platform}.tar.gz`;
    const binPath = path.resolve(__filename, "../../bin");
    const filePath = path.join(binPath, platformToDirMap[platform], fileName);
    return filePath;
};

const getZip = (platform, url, ifneeded) => {
    return new Promise((resolve, reject) => {
        const filePath = getZipFilePath(platform);
        const dir = path.dirname(filePath);
        const fileName = path.basename(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        if (!fs.existsSync(filePath) || !ifneeded) {
            console.info(`正在下载 ${platform} ${fileName}`);
            const file = fs.createWriteStream(filePath);
            https
                .get(new URL(url, host), (res) => {
                    res.pipe(file);
                    file.on("finish", () => {
                        file.close((err) => {
                            if (err) {
                                reject(err.message);
                            } else {
                                console.info(`${platform} ${fileName} 下载完成`);
                                resolve();
                            }
                        });
                    });
                })
                .on("error", (err) => {
                    fs.unlinkSync(filePath);
                    reject(err.message);
                });
        } else {
            console.info(`${platform} ${fileName} 已存在`);
            resolve();
        }
    });
};

const executeCodeAsFunctionBody = (code) => {
    let ret;
    try {
        ret = new Function(`return ${code}`);
        ret = ret();
    } catch (err) {
        // ignore error
        ret = undefined;
    }
    return ret;
};

const parseRelease = (release, sequence, jx) => {
    const platforms = Object.keys(platformToDirMap);
    const products = {};
    for (let j = 0; j < jx.length; j++) {
        const nm = jx[j].name;
        if (!release.re.test(nm)) continue;
        for (let k = 0; k < sequence.length; k++) {
            if (platforms.includes(sequence[k].name) && sequence[k].re.test(nm)) {
                products[sequence[k].name] = jx[j];
                break;
            }
        }
    }
    if (Object.keys(products).length !== platforms.length) {
        return undefined;
    }
    const ret = {};
    for (let k = 0; k < sequence.length; k++) {
        const nm = sequence[k].name;
        if (products[nm]) {
            ret[nm] = `/home/<USER>/${encodeURI(products[nm].name)}`;
        }
    }
    return ret;
};

const parse = (jsCode, listStr) => {
    const jx = JSON.parse(listStr);
    const releasesMatch = jsCode.match(/var releases = (\[[\s\S]*?\])/);
    const releases = executeCodeAsFunctionBody(releasesMatch[1]);
    const sequenceMatch = jsCode.match(/var sequence = (\[[\s\S]*?\])/);
    const sequence = executeCodeAsFunctionBody(sequenceMatch[1]);
    let map = undefined;
    const regex = /Version\s+(\d+\.\d+(?:\.\d+)?)/;
    for (let index = 0; index < releases.length; index++) {
        const release = releases[index];
        const version = release.title.match(regex)?.[1];
        const match =
            version === targetVersion || (targetVersion === "latest" && index === 0);
        if (match) {
            map = parseRelease(release, sequence, jx);
            if (map) {
                break;
            }
        }
    }
    if (!map) {
        map = parseRelease(releases[releases.length - 1], sequence, jx);
    }
    return map;
};

// Simplified tar.gz extraction for single file
const untar = (zipFilePath, binName) => {
    try {
        const dir = path.dirname(zipFilePath);
        const data = fs.readFileSync(zipFilePath);
        const decompressed = zlib.gunzipSync(data);

        // Basic tar parsing
        let offset = 0;
        while (offset < decompressed.length) {
            const header = decompressed.slice(offset, offset + 512);
            const name = header.slice(0, 100).toString().replace(/\0.*$/, "");
            const size = parseInt(header.slice(124, 136).toString(), 8);
            const type = header[156];

            if (name === binName && type === 48) {
                // Regular file
                const content = decompressed.slice(offset + 512, offset + 512 + size);
                const binPath = path.join(dir, binName);
                fs.writeFileSync(binPath, content);
                fs.chmodSync(binPath, "777");
                return true;
            }

            offset += 512 + Math.ceil(size / 512) * 512;
        }
        return false;
    } catch (error) {
        console.error(`Error in untarSingleFile: ${error.message}`);
        return false;
    }
};

// Simplified zip extraction for Windows
// 查找ZIP文件中央目录结尾位置
const findEndOfCentralDirectory = (buffer) => {
    const signature = Buffer.from([0x50, 0x4b, 0x05, 0x06]);
    const maxSearchLength = 0xffff + 22;
    const start = Math.max(0, buffer.length - maxSearchLength);
    for (let offset = buffer.length - 4; offset >= start; offset--) {
        if (buffer.subarray(offset, offset + 4).equals(signature)) {
            return offset;
        }
    }
    throw new Error("End of central directory not found");
};

const unzip = (zipFilePath, fileName) => {
    try {
        // 读取ZIP文件到内存
        const buffer = fs.readFileSync(zipFilePath);

        // 解析中央目录结构
        const endOffset = findEndOfCentralDirectory(buffer);
        const dirOffset = buffer.readUInt32LE(endOffset + 16);
        const dirSize = buffer.readUInt32LE(endOffset + 12);

        let pos = dirOffset;
        let fileData = null;

        // 遍历中央目录条目
        while (pos < dirOffset + dirSize) {
            const signature = buffer.readUInt32LE(pos);
            if (signature !== 0x02014b50) break; // 目录条目签名校验

            // 解析文件名信息
            const nameLen = buffer.readUInt16LE(pos + 28);
            const headerOffset = buffer.readUInt32LE(pos + 42);
            const currentName = buffer.toString("utf8", pos + 46, pos + 46 + nameLen);

            if (currentName === fileName) {
                // 定位到本地文件头
                const localHeader = buffer.readUInt32LE(headerOffset);
                if (localHeader !== 0x04034b50) throw new Error("Invalid local header");

                // 提取压缩数据信息
                const method = buffer.readUInt16LE(headerOffset + 8);
                const dataOffset =
                    headerOffset +
                    30 +
                    buffer.readUInt16LE(headerOffset + 26) + // 文件名长度
                    buffer.readUInt16LE(headerOffset + 28); // 额外字段长度
                const compressedSize = buffer.readUInt32LE(headerOffset + 18);
                const compressedData = buffer.subarray(
                    dataOffset,
                    dataOffset + compressedSize
                );

                // 解压数据
                fileData =
                    method === 0
                        ? compressedData
                        : method === 8
                            ? zlib.inflateRawSync(compressedData)
                            : null;
                if (!fileData) throw new Error(`Unsupported method: ${method}`);
                break;
            }

            // 移动到下一个条目
            pos +=
                46 +
                nameLen +
                buffer.readUInt16LE(pos + 30) + // 额外字段长度
                buffer.readUInt16LE(pos + 32); // 注释长度
        }

        if (!fileData) throw new Error("File not found in archive");

        // 构建输出路径
        const outputPath = path.join(path.dirname(zipFilePath), fileName);

        // 确保目录存在（支持fileName包含子目录的情况）
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 写入文件
        fs.writeFileSync(outputPath, fileData);
        return true;
    } catch (error) {
        console.error(`Error in unzipSingleFile: ${error.message}`);
        return false;
    }
};

const ifneeded = process.argv[2];
console.info(`正在下载fossil元信息`);
Promise.all([getText("/home/<USER>/download.js"), getText("/home/<USER>")])
    .then(([js, list]) => {
        const map = parse(js, list);
        if (!map) {
            console.error(`获取fossil元信息失败`);
            return;
        }
        let platforms = Object.keys(map);
        if (ifneeded) {
            platforms = platforms.filter((platform) => {
                const zipFilePath = getZipFilePath(platform);
                const dir = path.dirname(zipFilePath);
                const binName = platform === "win64" ? "fossil.exe" : "fossil";
                return !fs.existsSync(path.join(dir, binName));
            });
        }
        if (platforms.length === 0) {
            console.info("完成");
            return;
        }
        const requests = platforms.map((platform) => {
            const url = map[platform];
            return getZip(platform, url, ifneeded);
        });
        Promise.all(requests)
            .then(() => {
                console.info(`下载fossil zip完成，开始解压缩...`);
                try {
                    platforms.forEach((platform) => {
                        console.info(`正在解压缩${platform}...`);
                        let zipFilePath = getZipFilePath(platform);
                        let success = false;
                        if (platform.startsWith("mac")) {
                            const dir = path.join(path.dirname(zipFilePath), platform);
                            if (!fs.existsSync(dir)) {
                                fs.mkdirSync(dir, { recursive: true });
                            }
                            const newPath = path.join(dir, path.basename(zipFilePath));
                            fs.renameSync(zipFilePath, newPath);
                            zipFilePath = newPath;
                            success = untar(zipFilePath, "fossil");
                        } else if (platform === "win64") {
                            success = unzip(zipFilePath, "fossil.exe");
                        } else {
                            success = untar(zipFilePath, "fossil");
                        }
                        if (success) {
                            fs.rmSync(zipFilePath, { force: true, recursive: true });
                            console.info(`解压缩${platform}完成`);
                        } else {
                            console.error(`解压缩${platform}失败`);
                        }
                    });
                    console.info(`正在合并mac端fossil...`);
                    // 合并macos, lipo -c
                    const macosPlatforms = Object.keys(platformToDirMap).filter(
                        (platform) => platform.startsWith("mac")
                    );
                    const dir = path.dirname(getZipFilePath(macosPlatforms[0]));
                    const binName = "fossil";
                    exec(
                        `lipo ${macosPlatforms.map((platform) => `./${platform}/${binName}`).join(" ")} -c -o ${binName}`,
                        { cwd: dir },
                        (error) => {
                            if (error) {
                                console.error(`合并mac端fossil失败`, error);
                            } else {
                                macosPlatforms.forEach((platform) => {
                                    fs.rmSync(path.join(dir, platform), {
                                        force: true,
                                        recursive: true,
                                    });
                                });
                                const binPath = path.join(dir, binName);
                                fs.chmodSync(binPath, "777");
                                console.info(`合并mac端fossil完成`);
                            }
                        }
                    );
                } catch (err) {
                    console.error(`解压缩fossil zip失败`, err);
                }
            })
            .catch((err) => {
                console.error(`下载fossil zip失败`, err);
            });
    })
    .catch((err) => {
        console.error(`下载fossil元信息失败`, err);
    });
