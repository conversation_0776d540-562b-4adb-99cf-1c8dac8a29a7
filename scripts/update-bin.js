const fs = require("fs");
const path = require("path");
const http = require('https');

const platformToDirMap = {
  darwin: 'darwin',
  linux: 'linux',
  windows: 'win32',
};

const get = (platform, fileName, ifneeded) => {
  return new Promise((resolve, reject) => {
    const binPath = path.resolve(__filename, '../../bin');
    const filePath = path.join(binPath, platformToDirMap[platform], fileName);
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    const url = `https://tocode-release.oss-cn-hangzhou.aliyuncs.com/boilerplate/${platform}/${fileName}`;
    if (!fs.existsSync(filePath) || !ifneeded) {
      console.info(`正在下载 ${platform} ${fileName}`);
      const file = fs.createWriteStream(filePath);
      http.get(url, (res) => {
        res.pipe(file);
        file.on("finish", () => {
          file.close((err) => {
            if (err) {
              reject(err.message);
            } else {
              fs.chmodSync(filePath,'777');
              const workspacePath = path.join(dir, 'workspace' + (platform === "windows" ? ".exe" : ""));
              if (fs.existsSync(workspacePath)) {
                fs.rmSync(workspacePath);
              }
              console.info(`${platform} ${fileName} 下载完成`);
              resolve();
            }
          });
        });
      }).on('error', (err) => {
        fs.unlink(filePath);
        reject(err.message);
      });
    } else {
      console.info(`${platform} ${fileName} 已存在`);
      resolve();
    }
  });
};

const updateWorkspaceCli = async (ifneeded) => {
  try {
    await get('darwin', 'boilerplate', ifneeded);
    await get('linux', 'boilerplate', ifneeded);
    await get('windows', 'boilerplate.exe', ifneeded);
  } catch (error) {
    console.log(error);
  }
};

const ifneeded = process.argv[2];
updateWorkspaceCli(ifneeded);